# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Taskmaster - Include all files for traceability
# Only exclude sensitive config if needed
# .taskmaster/config.json (commented out - we want to track this)

# Cursor rules - Include all files for traceability
# .cursor/ (commented out - we want to track this)

# Added by Task Master AI
*.log

# Roo Code files (not wanted in this project)
.roo/
.roomodes