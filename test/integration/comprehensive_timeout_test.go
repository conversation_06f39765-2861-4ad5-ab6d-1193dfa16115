package integration

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

// TestComprehensiveTimeoutIntegration tests the entire timeout system working together
func TestComprehensiveTimeoutIntegration(t *testing.T) {
	// Create a server with configurable delays
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		delay := r.URL.Query().Get("delay")
		if delay != "" {
			if duration, err := time.ParseDuration(delay); err == nil {
				time.Sleep(duration)
			}
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// Configure client with comprehensive timeout settings and disable retries for predictable timeout testing
	config := client.DefaultConfig()
	config.TimeoutStrategy = client.NewBalancedTimeoutStrategy()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
	config.TimeoutMonitoring.Enabled = true

	httpClient := client.NewHTTPClient(config)
	httpClient.DisableRetries() // Disable retries for predictable timeout behavior
	defer httpClient.Close()

	tests := []struct {
		name           string
		requestTimeout time.Duration
		serverDelay    string
		expectTimeout  bool
		description    string
	}{
		{
			name:           "fast_request_no_timeout",
			requestTimeout: 2 * time.Second,
			serverDelay:    "100ms",
			expectTimeout:  false,
			description:    "Fast server response should not timeout",
		},
		{
			name:           "slow_request_with_timeout",
			requestTimeout: 500 * time.Millisecond,
			serverDelay:    "2s", // Increased delay to ensure timeout with retries disabled
			expectTimeout:  true,
			description:    "Slow server response should timeout with short timeout",
		},
		{
			name:           "no_request_timeout_uses_client_defaults",
			requestTimeout: 0,
			serverDelay:    "100ms",
			expectTimeout:  false,
			description:    "No request timeout should use client default timeouts",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request with specific timeout
			request := &client.Request{
				Method:  "GET",
				URL:     fmt.Sprintf("%s?delay=%s", server.URL, tt.serverDelay),
				Timeout: tt.requestTimeout,
			}

			// Execute request and measure time
			start := time.Now()
			response, err := httpClient.Execute(context.Background(), request)
			duration := time.Since(start)

			// Verify timeout behavior (with retries disabled, timeouts should be more predictable)
			if tt.expectTimeout {
				if err == nil {
					t.Errorf("Expected timeout error but got success: %v", response)
				}
				// With retries disabled, timeout should be closer to requested timeout
				maxAllowedTime := tt.requestTimeout + 2*time.Second // Allow reasonable overhead
				if duration > maxAllowedTime {
					t.Errorf("Request took too long for timeout test: %v > %v", duration, maxAllowedTime)
				}
			} else {
				if err != nil {
					t.Errorf("Expected success but got error: %v", err)
				}
				if response == nil {
					t.Error("Expected response but got nil")
				}
			}

			// Verify metrics are being collected
			summary := httpClient.GetMetricsSummary()
			if summary == nil {
				t.Error("Expected metrics summary but got nil")
			}
		})
	}

	// Verify timeout monitoring captured the test data
	timeoutReport := httpClient.GetTimeoutMonitoringReport()
	if enabled, ok := timeoutReport["enabled"]; !ok || !enabled.(bool) {
		t.Error("Expected timeout monitoring to be enabled")
	}

	// Verify that some requests were processed (may be tracked in different metrics)
	stats := httpClient.GetHTTPMethodStats()
	totalRequests := stats.GetTotalRequests()
	if totalRequests == 0 {
		t.Error("Expected some requests to be tracked in HTTP method stats")
	}

	t.Logf("Comprehensive timeout integration test completed with %d total requests", totalRequests)
}

// TestTimeoutMetricsAccuracy tests the accuracy of timeout metrics and reporting
func TestTimeoutMetricsAccuracy(t *testing.T) {
	// Create server with controllable behavior
	timeoutCount := 0
	successCount := 0

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		delay := r.URL.Query().Get("delay")
		if delay != "" {
			if duration, err := time.ParseDuration(delay); err == nil {
				if duration > 800*time.Millisecond {
					timeoutCount++
				} else {
					successCount++
				}
				time.Sleep(duration)
			}
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
	config.TimeoutMonitoring.Enabled = true

	httpClient := client.NewHTTPClient(config)
	defer httpClient.Close()

	// Execute requests with known timeout patterns
	testCases := []struct {
		delay   string
		timeout time.Duration
	}{
		{"100ms", 1 * time.Second},     // Should succeed
		{"200ms", 1 * time.Second},     // Should succeed
		{"1s", 500 * time.Millisecond}, // Should timeout
		{"2s", 500 * time.Millisecond}, // Should timeout
		{"100ms", 1 * time.Second},     // Should succeed
	}

	expectedTimeouts := 0
	expectedSuccesses := 0

	for i, tc := range testCases {
		request := &client.Request{
			Method:  "GET",
			URL:     fmt.Sprintf("%s?delay=%s", server.URL, tc.delay),
			Timeout: tc.timeout,
		}

		_, err := httpClient.Execute(context.Background(), request)

		if tc.timeout < 1*time.Second && (tc.delay == "1s" || tc.delay == "2s") {
			expectedTimeouts++
			if err == nil {
				t.Errorf("Test case %d: Expected timeout but got success", i)
			}
		} else {
			expectedSuccesses++
			if err != nil {
				t.Errorf("Test case %d: Expected success but got error: %v", i, err)
			}
		}
	}

	// Verify metrics accuracy
	summary := httpClient.GetMetricsSummary()
	timeoutMonitoring := summary["timeout_monitoring"].(map[string]interface{})

	// Note: The exact counts might not match due to timing variations,
	// but we should have some timeouts recorded
	if totalTimeouts, ok := timeoutMonitoring["total_request_timeouts"]; ok && expectedTimeouts > 0 {
		var timeoutCount int64
		switch v := totalTimeouts.(type) {
		case int:
			timeoutCount = int64(v)
		case int64:
			timeoutCount = v
		}
		if timeoutCount == 0 {
			t.Error("Expected some timeout errors to be recorded in metrics")
		}
	}
}

// TestTimeoutRecoveryScenarios tests timeout recovery and resilience
func TestTimeoutRecoveryScenarios(t *testing.T) {
	// Create server that becomes responsive after initial timeouts
	callCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		callCount++
		// First few calls are slow (cause timeouts), then become fast
		if callCount <= 3 {
			time.Sleep(2 * time.Second) // Cause timeout
		} else {
			time.Sleep(50 * time.Millisecond) // Fast response
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(fmt.Sprintf("Response %d", callCount)))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.TimeoutStrategy = client.NewBalancedTimeoutStrategy()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
	config.TimeoutMonitoring.Enabled = true

	httpClient := client.NewHTTPClient(config)
	defer httpClient.Close()

	// Execute multiple requests to test recovery
	timeoutPhase := true
	recoveryPhase := false

	for i := 0; i < 6; i++ {
		request := &client.Request{
			Method:  "GET",
			URL:     server.URL,
			Timeout: 1 * time.Second,
		}

		response, err := httpClient.Execute(context.Background(), request)

		if i < 3 && timeoutPhase {
			// Should timeout in first phase
			if err == nil {
				t.Errorf("Request %d: Expected timeout but got success", i)
			}
		} else if i >= 3 {
			// Should succeed in recovery phase
			recoveryPhase = true
			if err != nil {
				t.Errorf("Request %d: Expected success in recovery phase but got error: %v", i, err)
			}
			if response == nil {
				t.Errorf("Request %d: Expected response in recovery phase but got nil", i)
			}
		}
	}

	if !recoveryPhase {
		t.Error("Expected to test recovery phase but never reached it")
	}

	// Verify recovery metrics
	summary := httpClient.GetMetricsSummary()
	timeoutMonitoring := summary["timeout_monitoring"].(map[string]interface{})

	if recoveries, ok := timeoutMonitoring["timeout_recoveries"]; ok {
		var recoveryCount int64
		switch v := recoveries.(type) {
		case int:
			recoveryCount = int64(v)
		case int64:
			recoveryCount = v
		}
		if recoveryCount == 0 {
			t.Error("Expected some timeout recoveries to be recorded")
		}
	}
}

// TestTimeoutDistributionTracking tests timeout value distribution tracking
func TestTimeoutDistributionTracking(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Variable delay based on request
		delay := r.URL.Query().Get("delay")
		if delay != "" {
			if duration, err := time.ParseDuration(delay); err == nil {
				time.Sleep(duration)
			}
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
	config.TimeoutMonitoring.Enabled = true
	config.TimeoutMonitoring.DistributionBuckets = []time.Duration{
		100 * time.Millisecond,
		500 * time.Millisecond,
		1 * time.Second,
		5 * time.Second,
	}

	httpClient := client.NewHTTPClient(config)
	defer httpClient.Close()

	// Test different timeout scenarios
	timeouts := []time.Duration{
		50 * time.Millisecond,  // Below first bucket
		200 * time.Millisecond, // Second bucket
		800 * time.Millisecond, // Third bucket
		2 * time.Second,        // Fourth bucket
	}

	for _, timeout := range timeouts {
		request := &client.Request{
			Method:  "GET",
			URL:     fmt.Sprintf("%s?delay=%dms", server.URL, int(timeout.Milliseconds())+100), // Delay longer than timeout
			Timeout: timeout,
		}

		// Execute request (will timeout)
		httpClient.Execute(context.Background(), request)
	}

	// Verify distribution tracking
	breakdown := httpClient.GetTimeoutBreakdownSummary()
	if totalRequests, ok := breakdown["total_requests"]; ok {
		var totalReqCount int64
		switch v := totalRequests.(type) {
		case int:
			totalReqCount = int64(v)
		case int64:
			totalReqCount = v
		}
		if totalReqCount == 0 {
			t.Error("Expected some requests to be tracked in breakdown")
		}
	}

	// Verify timeout monitoring recorded the timeouts
	report := httpClient.GetTimeoutMonitoringReport()
	if !report["enabled"].(bool) {
		t.Error("Expected timeout monitoring to be enabled")
	}
}

// TestTimeoutEdgeCases tests various edge cases and error conditions
func TestTimeoutEdgeCases(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()

	httpClient := client.NewHTTPClient(config)
	defer httpClient.Close()

	tests := []struct {
		name           string
		timeout        time.Duration
		contextTimeout time.Duration
		expectError    bool
		description    string
	}{
		{
			name:        "zero_timeout",
			timeout:     0,
			expectError: false,
			description: "Zero timeout should use client defaults",
		},
		{
			name:        "very_small_timeout",
			timeout:     1 * time.Nanosecond,
			expectError: true,
			description: "Extremely small timeout should cause immediate timeout",
		},
		{
			name:           "context_timeout_shorter",
			timeout:        5 * time.Second,
			contextTimeout: 100 * time.Millisecond,
			expectError:    true,
			description:    "Context timeout should take precedence when shorter",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			if tt.contextTimeout > 0 {
				var cancel context.CancelFunc
				ctx, cancel = context.WithTimeout(ctx, tt.contextTimeout)
				defer cancel()
			}

			request := &client.Request{
				Method:  "GET",
				URL:     server.URL,
				Timeout: tt.timeout,
			}

			_, err := httpClient.Execute(ctx, request)

			if tt.expectError && err == nil {
				t.Errorf("Expected error but got success for %s", tt.description)
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected success but got error for %s: %v", tt.description, err)
			}
		})
	}
}

// TestTimeoutWithRetryInteraction tests timeout behavior with retry logic
func TestTimeoutWithRetryInteraction(t *testing.T) {
	attemptCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attemptCount++
		// First attempt times out, second succeeds
		if attemptCount == 1 {
			time.Sleep(2 * time.Second)
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(fmt.Sprintf("Attempt %d", attemptCount)))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.TimeoutStrategy = client.NewBalancedTimeoutStrategy()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
	config.TimeoutMonitoring.Enabled = true

	httpClient := client.NewHTTPClient(config)
	defer httpClient.Close()

	request := &client.Request{
		Method:  "GET",
		URL:     server.URL,
		Timeout: 1 * time.Second,
	}

	start := time.Now()
	response, err := httpClient.Execute(context.Background(), request)
	duration := time.Since(start)

	// Should succeed after retry (implementation depends on retry logic)
	if err != nil && attemptCount == 1 {
		t.Logf("Request failed as expected on first attempt: %v", err)
	} else if response != nil {
		t.Logf("Request succeeded with response: %s", string(response.Body))
	}

	// Verify timeout monitoring captured the retry behavior
	summary := httpClient.GetMetricsSummary()
	if summary == nil {
		t.Error("Expected metrics summary")
	}

	t.Logf("Test completed in %v with %d attempts", duration, attemptCount)
}

// TestConcurrentTimeoutHandling tests timeout handling under concurrent load
func TestConcurrentTimeoutHandling(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Random delay
		delay := r.URL.Query().Get("delay")
		if delay != "" {
			if duration, err := time.ParseDuration(delay); err == nil {
				time.Sleep(duration)
			}
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.TimeoutStrategy = client.NewBalancedTimeoutStrategy()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
	config.TimeoutMonitoring.Enabled = true

	httpClient := client.NewHTTPClient(config)
	defer httpClient.Close()

	// Launch concurrent requests with different timeout characteristics
	const numRequests = 10
	done := make(chan bool, numRequests)

	for i := 0; i < numRequests; i++ {
		go func(requestId int) {
			defer func() { done <- true }()

			var delay string
			var timeout time.Duration

			// Vary timeout and delay patterns
			if requestId%3 == 0 {
				delay = "2s"
				timeout = 500 * time.Millisecond // Will timeout
			} else {
				delay = "100ms"
				timeout = 1 * time.Second // Will succeed
			}

			request := &client.Request{
				Method:  "GET",
				URL:     fmt.Sprintf("%s?delay=%s", server.URL, delay),
				Timeout: timeout,
			}

			_, err := httpClient.Execute(context.Background(), request)

			// Log results but don't fail test for individual request outcomes
			if err != nil {
				t.Logf("Request %d failed as expected: %v", requestId, err)
			} else {
				t.Logf("Request %d succeeded as expected", requestId)
			}
		}(i)
	}

	// Wait for all requests to complete
	for i := 0; i < numRequests; i++ {
		<-done
	}

	// Verify system handled concurrent timeouts correctly
	summary := httpClient.GetMetricsSummary()
	timeoutMonitoring := summary["timeout_monitoring"].(map[string]interface{})

	if totalRequests, ok := timeoutMonitoring["total_requests"]; ok {
		var totalReqCount int64
		switch v := totalRequests.(type) {
		case int:
			totalReqCount = int64(v)
		case int64:
			totalReqCount = v
		}
		if totalReqCount == 0 {
			t.Error("Expected some requests to be tracked in concurrent test")
		}
		t.Logf("Concurrent test completed with %d total requests tracked", totalReqCount)
	} else {
		t.Error("Expected total_requests field in timeout monitoring")
	}
}

// BenchmarkTimeoutOverhead benchmarks the performance overhead of timeout management
func BenchmarkTimeoutOverhead(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// Test with and without timeout monitoring
	configs := map[string]*client.Config{
		"no_monitoring": func() *client.Config {
			c := client.DefaultConfig()
			c.TimeoutMonitoring.Enabled = false
			return c
		}(),
		"with_monitoring": func() *client.Config {
			c := client.DefaultConfig()
			c.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
			c.TimeoutMonitoring.Enabled = true
			return c
		}(),
	}

	for name, config := range configs {
		b.Run(name, func(b *testing.B) {
			httpClient := client.NewHTTPClient(config)
			defer httpClient.Close()

			request := &client.Request{
				Method:  "GET",
				URL:     server.URL,
				Timeout: 5 * time.Second,
			}

			b.ResetTimer()
			b.ReportAllocs()

			for i := 0; i < b.N; i++ {
				_, err := httpClient.Execute(context.Background(), request)
				if err != nil {
					b.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}
