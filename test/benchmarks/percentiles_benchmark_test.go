package benchmarks

import (
	"fmt"
	"math"
	"math/rand"
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// Test data generators for different scenarios
func generateNormalData(n int, mean, stddev float64) []float64 {
	rand.Seed(42) // Fixed seed for reproducible benchmarks
	data := make([]float64, n)
	for i := 0; i < n; i++ {
		// Box-Muller transform for normal distribution
		u1 := rand.Float64()
		u2 := rand.Float64()
		z := math.Sqrt(-2*math.Log(u1)) * math.Cos(2*math.Pi*u2)
		data[i] = mean + stddev*z
	}
	return data
}

func generateUniformData(n int, min, max float64) []float64 {
	rand.Seed(42)
	data := make([]float64, n)
	for i := 0; i < n; i++ {
		data[i] = min + rand.Float64()*(max-min)
	}
	return data
}

func generateExponentialData(n int, lambda float64) []float64 {
	rand.Seed(42)
	data := make([]float64, n)
	for i := 0; i < n; i++ {
		data[i] = -math.Log(rand.Float64()) / lambda
	}
	return data
}

func generateSkewedData(n int) []float64 {
	rand.Seed(42)
	data := make([]float64, n)
	for i := 0; i < n; i++ {
		if rand.Float64() < 0.9 { // 90% of values between 0-10
			data[i] = rand.Float64() * 10
		} else { // 10% of values between 100-1000 (outliers)
			data[i] = 100 + rand.Float64()*900
		}
	}
	return data
}

// Benchmark configurations
var benchmarkSizes = []int{1000, 10000, 100000}
var quantiles = []float64{0.5, 0.95, 0.99, 0.999}

// Benchmark exact percentile calculator
func BenchmarkExactPercentile(b *testing.B) {
	for _, size := range benchmarkSizes {
		data := generateNormalData(size, 100, 15)

		b.Run(fmt.Sprintf("Size_%d", size), func(b *testing.B) {
			calc := metrics.NewExactPercentileCalculator()

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				calc.Reset()
				for _, value := range data {
					calc.Add(value)
				}
				// Calculate all common quantiles
				for _, q := range quantiles {
					calc.Quantile(q)
				}
			}
		})
	}
}

// Benchmark T-Digest percentile calculator
func BenchmarkTDigestPercentile(b *testing.B) {
	compressionLevels := []float64{50, 100, 200}

	for _, compression := range compressionLevels {
		for _, size := range benchmarkSizes {
			data := generateNormalData(size, 100, 15)

			b.Run(fmt.Sprintf("Compression_%d_Size_%d", int(compression), size), func(b *testing.B) {
				calc := metrics.NewTDigestPercentileCalculator(compression)

				b.ResetTimer()
				for i := 0; i < b.N; i++ {
					calc.Reset()
					for _, value := range data {
						calc.Add(value)
					}
					// Calculate all common quantiles
					for _, q := range quantiles {
						calc.Quantile(q)
					}
				}
			})
		}
	}
}

// Benchmark P² percentile calculator
func BenchmarkP2Percentile(b *testing.B) {
	for _, q := range quantiles {
		for _, size := range benchmarkSizes {
			data := generateNormalData(size, 100, 15)

			b.Run(fmt.Sprintf("Quantile_%d_Size_%d", int(q*1000), size), func(b *testing.B) {
				calc := metrics.NewP2PercentileCalculator(q)

				b.ResetTimer()
				for i := 0; i < b.N; i++ {
					calc.Reset()
					for _, value := range data {
						calc.Add(value)
					}
					calc.Quantile(q)
				}
			})
		}
	}
}

// Memory usage benchmarks
func BenchmarkMemoryUsage(b *testing.B) {
	size := 10000
	data := generateNormalData(size, 100, 15)

	algorithms := []struct {
		name string
		calc metrics.PercentileCalculator
	}{
		{"Exact", metrics.NewExactPercentileCalculator()},
		{"TDigest_100", metrics.NewTDigestPercentileCalculator(100)},
		{"TDigest_200", metrics.NewTDigestPercentileCalculator(200)},
		{"P2_Median", metrics.NewP2PercentileCalculator(0.5)},
		{"P2_P95", metrics.NewP2PercentileCalculator(0.95)},
	}

	for _, alg := range algorithms {
		b.Run(alg.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				alg.calc.Reset()
				for _, value := range data {
					alg.calc.Add(value)
				}

				// Report memory usage
				if i == 0 { // Only report once
					memUsage := alg.calc.MemoryUsage()
					b.ReportMetric(float64(memUsage), "bytes/memory")
				}
			}
		})
	}
}

// Accuracy comparison benchmarks
func BenchmarkAccuracyComparison(b *testing.B) {
	size := 50000
	distributions := map[string][]float64{
		"Normal":      generateNormalData(size, 100, 15),
		"Uniform":     generateUniformData(size, 0, 200),
		"Exponential": generateExponentialData(size, 0.1),
		"Skewed":      generateSkewedData(size),
	}

	for distName, data := range distributions {
		b.Run(distName, func(b *testing.B) {
			// Calculate exact values as ground truth
			exactCalc := metrics.NewExactPercentileCalculator()
			for _, value := range data {
				exactCalc.Add(value)
			}

			var exactResults []float64
			for _, q := range quantiles {
				result, _ := exactCalc.Quantile(q)
				exactResults = append(exactResults, result)
			}

			// Test approximate algorithms
			tdigestCalc := metrics.NewTDigestPercentileCalculator(100)
			for _, value := range data {
				tdigestCalc.Add(value)
			}

			b.Run("TDigest_Accuracy", func(b *testing.B) {
				for i := 0; i < b.N; i++ {
					var totalError float64
					for j, q := range quantiles {
						result, _ := tdigestCalc.Quantile(q)
						error := math.Abs(result-exactResults[j]) / exactResults[j]
						totalError += error
					}
					avgError := totalError / float64(len(quantiles))
					b.ReportMetric(avgError*100, "percent_error")
				}
			})
		})
	}
}

// Streaming performance benchmark
func BenchmarkStreamingPerformance(b *testing.B) {
	algorithms := []struct {
		name string
		calc metrics.PercentileCalculator
	}{
		{"Exact", metrics.NewExactPercentileCalculator()},
		{"TDigest", metrics.NewTDigestPercentileCalculator(100)},
		{"P2_Median", metrics.NewP2PercentileCalculator(0.5)},
	}

	for _, alg := range algorithms {
		b.Run(alg.name, func(b *testing.B) {
			calc := alg.calc

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				// Simulate streaming data
				value := float64(i%1000) + rand.Float64()
				calc.Add(value)

				// Occasionally query percentiles (simulate real usage)
				if i%1000 == 0 {
					calc.Quantile(0.95)
				}
			}
		})
	}
}

// Multi-quantile calculator benchmark
func BenchmarkMultiQuantileCalculator(b *testing.B) {
	size := 10000
	data := generateNormalData(size, 100, 15)
	testQuantiles := []float64{0.25, 0.5, 0.75, 0.95, 0.99}

	algorithms := []metrics.PercentileAlgorithm{
		metrics.AlgorithmExact,
		metrics.AlgorithmTDigest,
	}

	for _, alg := range algorithms {
		b.Run(fmt.Sprintf("Algorithm_%d", int(alg)), func(b *testing.B) {
			multiCalc, _ := metrics.NewMultiQuantileCalculator(alg, testQuantiles)

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				multiCalc.Reset()
				for _, value := range data {
					multiCalc.Add(value)
				}
				multiCalc.AllQuantiles()
			}
		})
	}
}

// Concurrent access benchmark
func BenchmarkConcurrentAccess(b *testing.B) {
	calc := metrics.NewTDigestPercentileCalculator(100)

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// Simulate concurrent writes
			value := rand.Float64() * 1000
			calc.Add(value)

			// Occasionally read (10% of operations)
			if rand.Intn(10) == 0 {
				calc.Quantile(0.95)
			}
		}
	})
}

// Edge cases benchmark
func BenchmarkEdgeCases(b *testing.B) {
	testCases := []struct {
		name string
		data []float64
	}{
		{"Empty", []float64{}},
		{"SingleValue", []float64{42.0}},
		{"TwoValues", []float64{1.0, 100.0}},
		{"AllSame", []float64{42.0, 42.0, 42.0, 42.0, 42.0}},
		{"WithNaN", []float64{1.0, math.NaN(), 3.0, 4.0, 5.0}},
		{"WithInf", []float64{1.0, math.Inf(1), 3.0, math.Inf(-1), 5.0}},
		{"VeryLarge", []float64{1e308, 1e307, 1e306, 1e305, 1e304}},
		{"VerySmall", []float64{1e-308, 1e-307, 1e-306, 1e-305, 1e-304}},
	}

	algorithms := []struct {
		name string
		calc metrics.PercentileCalculator
	}{
		{"Exact", metrics.NewExactPercentileCalculator()},
		{"TDigest", metrics.NewTDigestPercentileCalculator(100)},
		{"P2", metrics.NewP2PercentileCalculator(0.5)},
	}

	for _, testCase := range testCases {
		for _, alg := range algorithms {
			b.Run(fmt.Sprintf("%s_%s", testCase.name, alg.name), func(b *testing.B) {
				for i := 0; i < b.N; i++ {
					calc := alg.calc
					calc.Reset()

					for _, value := range testCase.data {
						calc.Add(value)
					}

					// Try to calculate median, ignore errors for edge cases
					calc.Quantile(0.5)
				}
			})
		}
	}
}

// Load testing scenario benchmark
func BenchmarkLoadTestingScenario(b *testing.B) {
	// Simulate realistic load testing metrics (response times in milliseconds)
	generateResponseTimes := func(n int) []float64 {
		rand.Seed(time.Now().UnixNano())
		data := make([]float64, n)

		for i := 0; i < n; i++ {
			if rand.Float64() < 0.8 { // 80% normal responses (50-200ms)
				data[i] = 50 + rand.Float64()*150
			} else if rand.Float64() < 0.95 { // 15% slower responses (200-500ms)
				data[i] = 200 + rand.Float64()*300
			} else { // 5% very slow responses/timeouts (500-5000ms)
				data[i] = 500 + rand.Float64()*4500
			}
		}
		return data
	}

	data := generateResponseTimes(10000)

	// Test different algorithms for load testing scenario
	algorithms := []struct {
		name string
		calc metrics.PercentileCalculator
	}{
		{"TDigest_LoadTest", metrics.NewTDigestPercentileCalculator(200)}, // Higher compression for better accuracy
		{"P2_P95", metrics.NewP2PercentileCalculator(0.95)},               // Focus on 95th percentile
		{"P2_P99", metrics.NewP2PercentileCalculator(0.99)},               // Focus on 99th percentile
	}

	loadTestQuantiles := []float64{0.5, 0.95, 0.99, 0.999} // Common load testing percentiles

	for _, alg := range algorithms {
		b.Run(alg.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				calc := alg.calc
				calc.Reset()

				// Add all data points
				for _, value := range data {
					calc.Add(value)
				}

				// Query all relevant percentiles
				for _, q := range loadTestQuantiles {
					if alg.name == "P2_P95" && q != 0.95 {
						continue // P2 only calculates its target quantile
					}
					if alg.name == "P2_P99" && q != 0.99 {
						continue
					}
					calc.Quantile(q)
				}
			}
		})
	}
}

// Report generation for algorithm comparison
func BenchmarkReportGeneration(b *testing.B) {
	size := 50000
	data := generateNormalData(size, 100, 15)

	b.Run("AlgorithmComparison", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			// Initialize all algorithms
			exact := metrics.NewExactPercentileCalculator()
			tdigest := metrics.NewTDigestPercentileCalculator(100)
			p2_50 := metrics.NewP2PercentileCalculator(0.5)
			p2_95 := metrics.NewP2PercentileCalculator(0.95)

			// Add data to all calculators
			for _, value := range data {
				exact.Add(value)
				tdigest.Add(value)
				p2_50.Add(value)
				p2_95.Add(value)
			}

			// Generate comparison report
			report := make(map[string]map[string]float64)

			algorithms := []struct {
				name string
				calc metrics.PercentileCalculator
			}{
				{"Exact", exact},
				{"TDigest", tdigest},
				{"P2_Median", p2_50},
				{"P2_P95", p2_95},
			}

			for _, alg := range algorithms {
				report[alg.name] = map[string]float64{
					"count":     float64(alg.calc.Count()),
					"memory_kb": float64(alg.calc.MemoryUsage()) / 1024,
					"median":    0, // Will be filled if available
					"p95":       0, // Will be filled if available
				}

				// Calculate percentiles if supported
				if median, err := alg.calc.Quantile(0.5); err == nil {
					report[alg.name]["median"] = median
				}
				if p95, err := alg.calc.Quantile(0.95); err == nil {
					report[alg.name]["p95"] = p95
				}
			}

			// Report could be JSON serialized here in real usage
			_ = report
		}
	})
}
