package test

import (
	"testing"

	"neuralmetergo/internal/client"
	"neuralmetergo/internal/dashboard"
	"neuralmetergo/internal/metrics"
	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/worker"
)

// TestProjectStructure validates that all packages can be imported and basic structures exist
func TestProjectStructure(t *testing.T) {
	t.Run("Client Package", func(t *testing.T) {
		config := client.DefaultConfig()
		if config == nil {
			t.Error("DefaultConfig should not return nil")
		}
		
		httpClient := client.NewHTTPClient(config)
		if httpClient == nil {
			t.Error("NewHTTPClient should not return nil")
		}
	})

	t.<PERSON>("Worker Package", func(t *testing.T) {
		jobQueue := worker.NewJobQueue(100)
		if jobQueue == nil {
			t.Error("NewJobQueue should not return nil")
		}
		
		workerPool := worker.NewWorkerPool(10, jobQueue)
		if workerPool == nil {
			t.<PERSON>r("NewWorkerPool should not return nil")
		}
	})

	t.Run("Metrics Package", func(t *testing.T) {
		collector := metrics.NewCollector()
		if collector == nil {
			t.Error("NewCollector should not return nil")
		}
		
		// Test that GetMetrics returns valid data
		metrics := collector.GetMetrics()
		if metrics.StartTime.IsZero() {
			t.Error("StartTime should be set")
		}
	})

	t.Run("Parser Package", func(t *testing.T) {
		parser := parser.NewParser()
		if parser == nil {
			t.Error("NewParser should not return nil")
		}
	})

	t.Run("Dashboard Package", func(t *testing.T) {
		config := dashboard.DefaultConfig()
		if config == nil {
			t.Error("DefaultConfig should not return nil")
		}
		
		if config.Port != 8080 {
			t.Errorf("Expected default port 8080, got %d", config.Port)
		}
		
		dash := dashboard.NewDashboard(config)
		if dash == nil {
			t.Error("NewDashboard should not return nil")
		}
	})
} 