package unit

import (
	"fmt"
	"neuralmetergo/internal/config"
	"os"
	"testing"
)

func TestDebugValidation(t *testing.T) {
	// Create valid config
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
dashboard:
  enabled: true
  host: "localhost"
  port: 8081  # Different port initially
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "debug_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Print initial config
	cfg := cm.Get()
	fmt.Printf("Initial config - Server Port: %d, Dashboard Enabled: %v, Dashboard Port: %d\n",
		cfg.Server.Port, cfg.Dashboard.Enabled, cfg.Dashboard.Port)

	// Create the conflict - we need to access the actual config pointer
	config := cm.Get()
	config.Dashboard.Port = 8080
	fmt.Printf("After change - Server Port: %d, Dashboard Enabled: %v, Dashboard Port: %d\n",
		config.Server.Port, config.Dashboard.Enabled, config.Dashboard.Port)

	// Run enhanced validation
	result := cm.EnhancedValidate()

	fmt.Printf("Validation result - Errors: %d, Warnings: %d, Info: %d\n",
		len(result.Errors), len(result.Warnings), len(result.Info))

	for i, err := range result.Errors {
		fmt.Printf("Error %d: Field=%s, Rule=%s, Message=%s\n", i+1, err.Field, err.Rule, err.Message)
	}

	for i, warn := range result.Warnings {
		fmt.Printf("Warning %d: Field=%s, Rule=%s, Message=%s\n", i+1, warn.Field, warn.Rule, warn.Message)
	}

	for i, info := range result.Info {
		fmt.Printf("Info %d: Field=%s, Rule=%s, Message=%s\n", i+1, info.Field, info.Rule, info.Message)
	}
}
