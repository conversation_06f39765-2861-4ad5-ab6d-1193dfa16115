package unit

import (
	"math"
	"sync"
	"testing"

	"neuralmetergo/internal/metrics"
)

func TestCounter_NewCounter(t *testing.T) {
	counter := metrics.NewCounter()
	if counter == nil {
		t.Fatal("NewCounter() returned nil")
	}
	if counter.Value() != 0 {
		t.<PERSON><PERSON><PERSON>("NewCounter() initial value = %d, want 0", counter.Value())
	}
}

func TestCounter_Add(t *testing.T) {
	counter := metrics.NewCounter()

	// Test positive addition
	counter.Add(5)
	if got := counter.Value(); got != 5 {
		t.<PERSON>rrorf("Add(5) = %d, want 5", got)
	}

	// Test additional addition
	counter.Add(3)
	if got := counter.Value(); got != 8 {
		t.<PERSON><PERSON>rf("Add(3) after Add(5) = %d, want 8", got)
	}

	// Test negative addition
	counter.Add(-2)
	if got := counter.Value(); got != 6 {
		t.<PERSON><PERSON><PERSON>("Add(-2) after previous operations = %d, want 6", got)
	}
}

func TestCounter_Inc(t *testing.T) {
	counter := metrics.NewCounter()

	// Test single increment
	counter.Inc()
	if got := counter.Value(); got != 1 {
		t.Errorf("Inc() = %d, want 1", got)
	}

	// Test multiple increments
	counter.Inc()
	counter.Inc()
	if got := counter.Value(); got != 3 {
		t.Errorf("Inc() after 3 calls = %d, want 3", got)
	}
}

func TestCounter_Value(t *testing.T) {
	counter := metrics.NewCounter()

	// Test initial value
	if got := counter.Value(); got != 0 {
		t.Errorf("Value() initial = %d, want 0", got)
	}

	// Test value after operations
	counter.Add(42)
	if got := counter.Value(); got != 42 {
		t.Errorf("Value() after Add(42) = %d, want 42", got)
	}
}

func TestCounter_Reset(t *testing.T) {
	counter := metrics.NewCounter()

	// Add some value
	counter.Add(100)
	if got := counter.Value(); got != 100 {
		t.Errorf("Value() before reset = %d, want 100", got)
	}

	// Reset and check
	counter.Reset()
	if got := counter.Value(); got != 0 {
		t.Errorf("Value() after Reset() = %d, want 0", got)
	}
}

func TestCounter_ConcurrentAccess(t *testing.T) {
	counter := metrics.NewCounter()
	const numGoroutines = 100
	const incrementsPerGoroutine = 1000

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	// Start multiple goroutines that increment the counter
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < incrementsPerGoroutine; j++ {
				counter.Inc()
			}
		}()
	}

	wg.Wait()

	expected := int64(numGoroutines * incrementsPerGoroutine)
	if got := counter.Value(); got != expected {
		t.Errorf("Concurrent Inc() = %d, want %d", got, expected)
	}
}

func TestCounter_ConcurrentMixedOperations(t *testing.T) {
	counter := metrics.NewCounter()
	const numGoroutines = 50
	const operationsPerGoroutine = 500

	var wg sync.WaitGroup
	wg.Add(numGoroutines * 3) // 3 types of operations

	// Goroutines that increment
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				counter.Inc()
			}
		}()
	}

	// Goroutines that add positive values
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				counter.Add(2)
			}
		}()
	}

	// Goroutines that read values (should not affect final result)
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				_ = counter.Value() // Just read, don't use the value
			}
		}()
	}

	wg.Wait()

	// Expected: numGoroutines * operationsPerGoroutine * 1 (Inc) + numGoroutines * operationsPerGoroutine * 2 (Add)
	expected := int64(numGoroutines * operationsPerGoroutine * 3)
	if got := counter.Value(); got != expected {
		t.Errorf("Concurrent mixed operations = %d, want %d", got, expected)
	}
}

func TestCounter_LargeValues(t *testing.T) {
	counter := metrics.NewCounter()

	// Test with large positive value
	largeValue := int64(1 << 60) // Large but not overflow
	counter.Add(largeValue)
	if got := counter.Value(); got != largeValue {
		t.Errorf("Add(large value) = %d, want %d", got, largeValue)
	}

	// Reset and test with large negative value
	counter.Reset()
	counter.Add(-largeValue)
	if got := counter.Value(); got != -largeValue {
		t.Errorf("Add(large negative value) = %d, want %d", got, -largeValue)
	}
}

func BenchmarkCounter_Inc(b *testing.B) {
	counter := metrics.NewCounter()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		counter.Inc()
	}
}

func BenchmarkCounter_Add(b *testing.B) {
	counter := metrics.NewCounter()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		counter.Add(1)
	}
}

func BenchmarkCounter_Value(b *testing.B) {
	counter := metrics.NewCounter()
	counter.Add(100) // Set some value
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = counter.Value()
	}
}

func BenchmarkCounter_ConcurrentInc(b *testing.B) {
	counter := metrics.NewCounter()
	b.ResetTimer()

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			counter.Inc()
		}
	})
}

// ============================================================================
// Gauge Tests
// ============================================================================

func TestGauge_NewGauge(t *testing.T) {
	gauge := metrics.NewGauge()
	if gauge == nil {
		t.Fatal("NewGauge() returned nil")
	}
	if gauge.Value() != 0.0 {
		t.Errorf("NewGauge() initial value = %f, want 0.0", gauge.Value())
	}
}

func TestGauge_Set(t *testing.T) {
	gauge := metrics.NewGauge()

	// Test positive value
	err := gauge.Set(42.5)
	if err != nil {
		t.Errorf("Set(42.5) returned error: %v", err)
	}
	if got := gauge.Value(); got != 42.5 {
		t.Errorf("Set(42.5) = %f, want 42.5", got)
	}

	// Test negative value
	err = gauge.Set(-17.3)
	if err != nil {
		t.Errorf("Set(-17.3) returned error: %v", err)
	}
	if got := gauge.Value(); got != -17.3 {
		t.Errorf("Set(-17.3) = %f, want -17.3", got)
	}

	// Test zero
	err = gauge.Set(0.0)
	if err != nil {
		t.Errorf("Set(0.0) returned error: %v", err)
	}
	if got := gauge.Value(); got != 0.0 {
		t.Errorf("Set(0.0) = %f, want 0.0", got)
	}
}

func TestGauge_SetNaNInf(t *testing.T) {
	gauge := metrics.NewGauge()

	// Test NaN
	err := gauge.Set(math.NaN())
	if err != metrics.ErrNaNValue {
		t.Errorf("Set(NaN) = %v, want %v", err, metrics.ErrNaNValue)
	}
	// Value should remain unchanged (0.0)
	if got := gauge.Value(); got != 0.0 {
		t.Errorf("After Set(NaN), Value() = %f, want 0.0", got)
	}

	// Test positive infinity
	err = gauge.Set(math.Inf(1))
	if err != metrics.ErrInfValue {
		t.Errorf("Set(+Inf) = %v, want %v", err, metrics.ErrInfValue)
	}

	// Test negative infinity
	err = gauge.Set(math.Inf(-1))
	if err != metrics.ErrInfValue {
		t.Errorf("Set(-Inf) = %v, want %v", err, metrics.ErrInfValue)
	}
}

func TestGauge_Add(t *testing.T) {
	gauge := metrics.NewGauge()

	// Test positive addition
	err := gauge.Add(10.5)
	if err != nil {
		t.Errorf("Add(10.5) returned error: %v", err)
	}
	if got := gauge.Value(); got != 10.5 {
		t.Errorf("Add(10.5) = %f, want 10.5", got)
	}

	// Test additional addition
	err = gauge.Add(5.3)
	if err != nil {
		t.Errorf("Add(5.3) returned error: %v", err)
	}
	if got := gauge.Value(); got != 15.8 {
		t.Errorf("Add(5.3) after Add(10.5) = %f, want 15.8", got)
	}

	// Test negative addition
	err = gauge.Add(-3.8)
	if err != nil {
		t.Errorf("Add(-3.8) returned error: %v", err)
	}
	if got := gauge.Value(); got != 12.0 {
		t.Errorf("Add(-3.8) after previous operations = %f, want 12.0", got)
	}
}

func TestGauge_AddNaNInf(t *testing.T) {
	gauge := metrics.NewGauge()
	gauge.Set(10.0) // Set initial value

	// Test NaN addition
	err := gauge.Add(math.NaN())
	if err != metrics.ErrNaNValue {
		t.Errorf("Add(NaN) = %v, want %v", err, metrics.ErrNaNValue)
	}
	// Value should remain unchanged
	if got := gauge.Value(); got != 10.0 {
		t.Errorf("After Add(NaN), Value() = %f, want 10.0", got)
	}

	// Test infinity addition
	err = gauge.Add(math.Inf(1))
	if err != metrics.ErrInfValue {
		t.Errorf("Add(+Inf) = %v, want %v", err, metrics.ErrInfValue)
	}

	// Test result that would be infinity
	gauge.Set(math.MaxFloat64)
	err = gauge.Add(math.MaxFloat64)
	if err != metrics.ErrInfValue {
		t.Errorf("Add(MaxFloat64) to MaxFloat64 = %v, want %v", err, metrics.ErrInfValue)
	}
}

func TestGauge_Sub(t *testing.T) {
	gauge := metrics.NewGauge()
	gauge.Set(20.0)

	// Test subtraction
	err := gauge.Sub(5.5)
	if err != nil {
		t.Errorf("Sub(5.5) returned error: %v", err)
	}
	if got := gauge.Value(); got != 14.5 {
		t.Errorf("Sub(5.5) from 20.0 = %f, want 14.5", got)
	}

	// Test negative subtraction (should add)
	err = gauge.Sub(-3.5)
	if err != nil {
		t.Errorf("Sub(-3.5) returned error: %v", err)
	}
	if got := gauge.Value(); got != 18.0 {
		t.Errorf("Sub(-3.5) from 14.5 = %f, want 18.0", got)
	}
}

func TestGauge_Reset(t *testing.T) {
	gauge := metrics.NewGauge()
	gauge.Set(42.7)

	gauge.Reset()
	if got := gauge.Value(); got != 0.0 {
		t.Errorf("Reset() = %f, want 0.0", got)
	}
}

func TestGauge_ConcurrentOperations(t *testing.T) {
	gauge := metrics.NewGauge()
	const numGoroutines = 100
	const operationsPerGoroutine = 1000

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	// Test concurrent Add operations
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				gauge.Add(1.0)
			}
		}()
	}

	wg.Wait()

	expected := float64(numGoroutines * operationsPerGoroutine)
	if got := gauge.Value(); got != expected {
		t.Errorf("Concurrent Add operations: got %f, want %f", got, expected)
	}
}

func TestGauge_ConcurrentMixedOperations(t *testing.T) {
	gauge := metrics.NewGauge()
	const numGoroutines = 50
	const operationsPerGoroutine = 100

	var wg sync.WaitGroup
	wg.Add(numGoroutines * 3) // 3 types of operations

	// Concurrent Set operations
	for i := 0; i < numGoroutines; i++ {
		go func(val float64) {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				gauge.Set(val)
			}
		}(float64(i))
	}

	// Concurrent Add operations
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				gauge.Add(0.1)
			}
		}()
	}

	// Concurrent Value reads
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				_ = gauge.Value()
			}
		}()
	}

	wg.Wait()

	// Just verify it doesn't crash and final value is a valid float64
	finalValue := gauge.Value()
	if math.IsNaN(finalValue) || math.IsInf(finalValue, 0) {
		t.Errorf("Concurrent mixed operations resulted in invalid value: %f", finalValue)
	}
}

func TestGauge_FloatPrecision(t *testing.T) {
	gauge := metrics.NewGauge()

	// Test very small values
	smallVal := 1e-10
	err := gauge.Set(smallVal)
	if err != nil {
		t.Errorf("Set(%e) returned error: %v", smallVal, err)
	}
	if got := gauge.Value(); got != smallVal {
		t.Errorf("Set(%e) = %e, want %e", smallVal, got, smallVal)
	}

	// Test very large values
	largeVal := 1e10
	err = gauge.Set(largeVal)
	if err != nil {
		t.Errorf("Set(%e) returned error: %v", largeVal, err)
	}
	if got := gauge.Value(); got != largeVal {
		t.Errorf("Set(%e) = %e, want %e", largeVal, got, largeVal)
	}
}

// ============================================================================
// Gauge Benchmarks
// ============================================================================

func BenchmarkGauge_Set(b *testing.B) {
	gauge := metrics.NewGauge()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		gauge.Set(float64(i))
	}
}

func BenchmarkGauge_Add(b *testing.B) {
	gauge := metrics.NewGauge()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		gauge.Add(1.0)
	}
}

func BenchmarkGauge_Value(b *testing.B) {
	gauge := metrics.NewGauge()
	gauge.Set(42.0)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = gauge.Value()
	}
}

func BenchmarkGauge_ConcurrentOperations(b *testing.B) {
	gauge := metrics.NewGauge()
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			gauge.Add(1.0)
		}
	})
}

// ============================================================================
// Histogram Tests
// ============================================================================

func TestHistogram_NewHistogram(t *testing.T) {
	hist := metrics.NewHistogram()
	if hist == nil {
		t.Fatal("NewHistogram() returned nil")
	}
	if hist.Count() != 0 {
		t.Errorf("NewHistogram() initial count = %d, want 0", hist.Count())
	}
	if hist.Sum() != 0.0 {
		t.Errorf("NewHistogram() initial sum = %f, want 0.0", hist.Sum())
	}
}

func TestHistogram_NewHistogramWithBuckets(t *testing.T) {
	customBuckets := []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 50.0, 100.0, 200.0, 500.0, 1000.0, 2000.0, 5000.0, math.Inf(1)}
	hist := metrics.NewHistogramWithBuckets(customBuckets)
	if hist == nil {
		t.Fatal("NewHistogramWithBuckets() returned nil")
	}

	// Test that buckets work by observing values
	hist.Observe(0.05) // Should go in first bucket (0.1)
	hist.Observe(1.5)  // Should go in fourth bucket (2.0)

	if hist.Count() != 2 {
		t.Errorf("After observing 2 values, count = %d, want 2", hist.Count())
	}
}

func TestHistogram_Observe(t *testing.T) {
	hist := metrics.NewHistogram()

	// Test basic observation
	hist.Observe(0.001) // 1ms
	if hist.Count() != 1 {
		t.Errorf("After Observe(0.001), count = %d, want 1", hist.Count())
	}
	if hist.Sum() != 0.001 {
		t.Errorf("After Observe(0.001), sum = %f, want 0.001", hist.Sum())
	}

	// Test multiple observations
	hist.Observe(0.005) // 5ms
	hist.Observe(0.1)   // 100ms

	if hist.Count() != 3 {
		t.Errorf("After 3 observations, count = %d, want 3", hist.Count())
	}
	expectedSum := 0.001 + 0.005 + 0.1
	if math.Abs(hist.Sum()-expectedSum) > 1e-9 {
		t.Errorf("After 3 observations, sum = %f, want %f", hist.Sum(), expectedSum)
	}
}

func TestHistogram_ObserveInvalidValues(t *testing.T) {
	hist := metrics.NewHistogram()

	// Test NaN observation (should be ignored)
	hist.Observe(math.NaN())
	if hist.Count() != 0 {
		t.Errorf("After Observe(NaN), count = %d, want 0", hist.Count())
	}

	// Test infinity observation (should be ignored)
	hist.Observe(math.Inf(1))
	if hist.Count() != 0 {
		t.Errorf("After Observe(+Inf), count = %d, want 0", hist.Count())
	}

	// Test that valid observations still work
	hist.Observe(1.0)
	if hist.Count() != 1 {
		t.Errorf("After valid observation, count = %d, want 1", hist.Count())
	}
}

func TestHistogram_Bucket(t *testing.T) {
	hist := metrics.NewHistogram()

	// Observe values in different buckets
	hist.Observe(0.0005) // 500μs bucket
	hist.Observe(0.002)  // 5ms bucket
	hist.Observe(0.05)   // 50ms bucket
	hist.Observe(0.5)    // 500ms bucket
	hist.Observe(2.0)    // 2.5s bucket

	// Test cumulative bucket counts
	if count := hist.Bucket(0.001); count != 1 { // Should include 500μs
		t.Errorf("Bucket(0.001) = %d, want 1", count)
	}
	if count := hist.Bucket(0.01); count != 2 { // Should include 500μs + 5ms
		t.Errorf("Bucket(0.01) = %d, want 2", count)
	}
	if count := hist.Bucket(0.1); count != 3 { // Should include 500μs + 5ms + 50ms
		t.Errorf("Bucket(0.1) = %d, want 3", count)
	}
	if count := hist.Bucket(1.0); count != 4 { // Should include all except 2s
		t.Errorf("Bucket(1.0) = %d, want 4", count)
	}
	if count := hist.Bucket(10.0); count != 5 { // Should include all
		t.Errorf("Bucket(10.0) = %d, want 5", count)
	}
}

func TestHistogram_Quantile(t *testing.T) {
	hist := metrics.NewHistogram()

	// Test empty histogram
	if q := hist.Quantile(0.5); q != 0.0 {
		t.Errorf("Quantile(0.5) on empty histogram = %f, want 0.0", q)
	}

	// Add observations: 1ms, 5ms, 10ms, 50ms, 100ms
	observations := []float64{0.001, 0.005, 0.01, 0.05, 0.1}
	for _, obs := range observations {
		hist.Observe(obs)
	}

	// Test median (50th percentile)
	median := hist.Quantile(0.5)
	if median <= 0.005 || median >= 0.05 { // Should be between 5ms and 50ms
		t.Errorf("Quantile(0.5) = %f, expected between 0.005 and 0.05", median)
	}

	// Test 95th percentile
	p95 := hist.Quantile(0.95)
	if p95 <= 0.05 { // Should be >= 50ms
		t.Errorf("Quantile(0.95) = %f, expected >= 0.05", p95)
	}

	// Test invalid quantiles
	if q := hist.Quantile(-0.1); !math.IsNaN(q) {
		t.Errorf("Quantile(-0.1) = %f, want NaN", q)
	}
	if q := hist.Quantile(1.1); !math.IsNaN(q) {
		t.Errorf("Quantile(1.1) = %f, want NaN", q)
	}
}

func TestHistogram_Reset(t *testing.T) {
	hist := metrics.NewHistogram()

	// Add some observations
	hist.Observe(0.001)
	hist.Observe(0.1)
	hist.Observe(1.0)

	// Verify data exists
	if hist.Count() == 0 {
		t.Fatal("Expected non-zero count before reset")
	}
	if hist.Sum() == 0.0 {
		t.Fatal("Expected non-zero sum before reset")
	}

	// Reset and verify
	hist.Reset()
	if hist.Count() != 0 {
		t.Errorf("After Reset(), count = %d, want 0", hist.Count())
	}
	if hist.Sum() != 0.0 {
		t.Errorf("After Reset(), sum = %f, want 0.0", hist.Sum())
	}

	// Verify buckets are reset
	if count := hist.Bucket(math.Inf(1)); count != 0 {
		t.Errorf("After Reset(), total bucket count = %d, want 0", count)
	}
}

func TestHistogram_ConcurrentObservations(t *testing.T) {
	hist := metrics.NewHistogram()
	const numGoroutines = 100
	const observationsPerGoroutine = 1000

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	// Concurrent observations
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer wg.Done()
			value := float64(id%10) * 0.001 // Values from 0ms to 9ms
			for j := 0; j < observationsPerGoroutine; j++ {
				hist.Observe(value)
			}
		}(i)
	}

	wg.Wait()

	expectedCount := uint64(numGoroutines * observationsPerGoroutine)
	if hist.Count() != expectedCount {
		t.Errorf("Concurrent observations: got count %d, want %d", hist.Count(), expectedCount)
	}

	// Verify sum is reasonable (should be > 0)
	if hist.Sum() <= 0 {
		t.Errorf("Concurrent observations: sum = %f, want > 0", hist.Sum())
	}
}

func TestHistogram_ConcurrentMixedOperations(t *testing.T) {
	hist := metrics.NewHistogram()
	const numGoroutines = 50
	const operationsPerGoroutine = 100

	var wg sync.WaitGroup
	wg.Add(numGoroutines * 3) // 3 types of operations

	// Concurrent observations
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer wg.Done()
			value := float64(id%5) * 0.01 // Values from 0ms to 40ms
			for j := 0; j < operationsPerGoroutine; j++ {
				hist.Observe(value)
			}
		}(i)
	}

	// Concurrent reads
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				_ = hist.Count()
				_ = hist.Sum()
				_ = hist.Bucket(1.0)
			}
		}()
	}

	// Concurrent quantile calculations
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				_ = hist.Quantile(0.5)
				_ = hist.Quantile(0.95)
			}
		}()
	}

	wg.Wait()

	// Just verify it doesn't crash and final values are reasonable
	finalCount := hist.Count()
	finalSum := hist.Sum()

	if finalCount == 0 {
		t.Error("Expected non-zero count after concurrent operations")
	}
	if finalSum < 0 || math.IsNaN(finalSum) || math.IsInf(finalSum, 0) {
		t.Errorf("Invalid sum after concurrent operations: %f", finalSum)
	}
}

func TestHistogram_OutlierHandling(t *testing.T) {
	hist := metrics.NewHistogram()

	// Observe values within normal range
	hist.Observe(0.001) // 1ms
	hist.Observe(0.1)   // 100ms

	// Observe outliers (very large values)
	hist.Observe(100.0)  // 100s (much larger than max bucket of 10s)
	hist.Observe(1000.0) // 1000s

	if hist.Count() != 4 {
		t.Errorf("After observing outliers, count = %d, want 4", hist.Count())
	}

	expectedSum := 0.001 + 0.1 + 100.0 + 1000.0
	if math.Abs(hist.Sum()-expectedSum) > 1e-6 {
		t.Errorf("After observing outliers, sum = %f, want %f", hist.Sum(), expectedSum)
	}

	// Test that bucket counts work with outliers
	normalCount := hist.Bucket(10.0) // Should include normal values but not outliers
	if normalCount != 2 {
		t.Errorf("Bucket(10.0) with outliers = %d, want 2", normalCount)
	}

	totalCount := hist.Bucket(math.Inf(1)) // Should include everything
	if totalCount != 4 {
		t.Errorf("Bucket(+Inf) with outliers = %d, want 4", totalCount)
	}
}

// ============================================================================
// Histogram Benchmarks
// ============================================================================

func BenchmarkHistogram_Observe(b *testing.B) {
	hist := metrics.NewHistogram()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		hist.Observe(float64(i%1000) * 0.001) // Values from 0ms to 999ms
	}
}

func BenchmarkHistogram_Count(b *testing.B) {
	hist := metrics.NewHistogram()
	hist.Observe(0.1)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = hist.Count()
	}
}

func BenchmarkHistogram_Sum(b *testing.B) {
	hist := metrics.NewHistogram()
	hist.Observe(0.1)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = hist.Sum()
	}
}

func BenchmarkHistogram_Quantile(b *testing.B) {
	hist := metrics.NewHistogram()
	// Pre-populate with data
	for i := 0; i < 1000; i++ {
		hist.Observe(float64(i) * 0.001)
	}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = hist.Quantile(0.95)
	}
}

func BenchmarkHistogram_ConcurrentObserve(b *testing.B) {
	hist := metrics.NewHistogram()
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			hist.Observe(0.001)
		}
	})
}
