package unit

import (
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/parser"
)

func TestParser_ParseBytes_ValidYAML(t *testing.T) {
	yamlData := `
version: "1.0"
name: "Test Plan"
description: "A simple test plan"
duration: "2m"
concurrency: 10
ramp_up: "30s"

global:
  base_url: "https://api.example.com"
  timeout: "15s"
  headers:
    User-Agent: "NeuralMeter/1.0"
  variables:
    api_key: "test-key"

variables:
  - name: "username"
    type: "static"
    value: "testuser"

scenarios:
  - name: "Login Test"
    description: "Test user login"
    weight: 100
    requests:
      - name: "Login"
        method: "POST"
        url: "/auth/login"
        headers:
          Content-Type: "application/json"
        body:
          username: "{{.username}}"
          password: "password123"
        timeout: "10s"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
            description: "Should return success"
        extract:
          - name: "token"
            type: "json_path"
            path: "$.token"

output:
  format: ["json"]
  metrics: ["response_time", "error_rate"]
`

	p := parser.NewParser()
	plan, err := p.ParseBytes([]byte(yamlData))

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	// Test basic fields
	if plan.Version != "1.0" {
		t.Errorf("Expected version '1.0', got '%s'", plan.Version)
	}

	if plan.Name != "Test Plan" {
		t.Errorf("Expected name 'Test Plan', got '%s'", plan.Name)
	}

	if plan.Duration.Duration != 2*time.Minute {
		t.Errorf("Expected duration 2m, got %v", plan.Duration.Duration)
	}

	if plan.Concurrency != 10 {
		t.Errorf("Expected concurrency 10, got %d", plan.Concurrency)
	}

	if plan.RampUp.Duration != 30*time.Second {
		t.Errorf("Expected ramp_up 30s, got %v", plan.RampUp.Duration)
	}

	// Test global settings
	if plan.Global.BaseURL != "https://api.example.com" {
		t.Errorf("Expected base_url 'https://api.example.com', got '%s'", plan.Global.BaseURL)
	}

	if plan.Global.Timeout.Duration != 15*time.Second {
		t.Errorf("Expected global timeout 15s, got %v", plan.Global.Timeout.Duration)
	}

	// Test scenarios
	if len(plan.Scenarios) != 1 {
		t.Fatalf("Expected 1 scenario, got %d", len(plan.Scenarios))
	}

	scenario := plan.Scenarios[0]
	if scenario.Name != "Login Test" {
		t.Errorf("Expected scenario name 'Login Test', got '%s'", scenario.Name)
	}

	if scenario.Weight != 100 {
		t.Errorf("Expected scenario weight 100, got %d", scenario.Weight)
	}

	// Test requests
	if len(scenario.Requests) != 1 {
		t.Fatalf("Expected 1 request, got %d", len(scenario.Requests))
	}

	request := scenario.Requests[0]
	if request.Method != "POST" {
		t.Errorf("Expected method 'POST', got '%s'", request.Method)
	}

	if request.URL != "/auth/login" {
		t.Errorf("Expected URL '/auth/login', got '%s'", request.URL)
	}

	// Test assertions
	if len(request.Assertions) != 1 {
		t.Fatalf("Expected 1 assertion, got %d", len(request.Assertions))
	}

	assertion := request.Assertions[0]
	if assertion.Type != "status_code" {
		t.Errorf("Expected assertion type 'status_code', got '%s'", assertion.Type)
	}

	if assertion.Operator != "eq" {
		t.Errorf("Expected assertion operator 'eq', got '%s'", assertion.Operator)
	}

	// Test extraction
	if len(request.Extract) != 1 {
		t.Fatalf("Expected 1 extract, got %d", len(request.Extract))
	}

	extract := request.Extract[0]
	if extract.Name != "token" {
		t.Errorf("Expected extract name 'token', got '%s'", extract.Name)
	}

	if extract.Type != "json_path" {
		t.Errorf("Expected extract type 'json_path', got '%s'", extract.Type)
	}

	// Test variables
	if len(plan.Variables) != 1 {
		t.Fatalf("Expected 1 variable, got %d", len(plan.Variables))
	}

	variable := plan.Variables[0]
	if variable.Name != "username" {
		t.Errorf("Expected variable name 'username', got '%s'", variable.Name)
	}

	if variable.Type != "static" {
		t.Errorf("Expected variable type 'static', got '%s'", variable.Type)
	}
}

func TestParser_ParseBytes_InvalidYAML(t *testing.T) {
	invalidYAML := `
invalid: yaml: content:
  - missing
    - proper
  indentation
`

	p := parser.NewParser()
	_, err := p.ParseBytes([]byte(invalidYAML))

	if err == nil {
		t.Fatal("Expected error for invalid YAML, got nil")
	}

	if !strings.Contains(err.Error(), "failed to parse YAML") {
		t.Errorf("Expected 'failed to parse YAML' error, got %v", err)
	}
}

func TestParser_ParseBytes_ValidationErrors(t *testing.T) {
	tests := []struct {
		name        string
		yaml        string
		expectedErr string
	}{
		{
			name: "missing required version",
			yaml: `
name: "Test"
duration: "1m"
concurrency: 1
scenarios:
  - name: "test"
    requests:
      - method: "GET"
        url: "/test"
`,
			expectedErr: "validation failed",
		},
		{
			name: "missing required name",
			yaml: `
version: "1.0"
duration: "1m"
concurrency: 1
scenarios:
  - name: "test"
    requests:
      - method: "GET"
        url: "/test"
`,
			expectedErr: "validation failed",
		},
		{
			name: "invalid concurrency",
			yaml: `
version: "1.0"
name: "Test"
duration: "1m"
concurrency: 0
scenarios:
  - name: "test"
    requests:
      - method: "GET"
        url: "/test"
`,
			expectedErr: "validation failed",
		},
		{
			name: "invalid HTTP method",
			yaml: `
version: "1.0"
name: "Test"
duration: "1m"
concurrency: 1
scenarios:
  - name: "test"
    requests:
      - method: "INVALID"
        url: "/test"
`,
			expectedErr: "validation failed",
		},
		{
			name: "invalid assertion type",
			yaml: `
version: "1.0"
name: "Test"
duration: "1m"
concurrency: 1
scenarios:
  - name: "test"
    requests:
      - method: "GET"
        url: "/test"
        assertions:
          - type: "invalid_type"
            value: 200
`,
			expectedErr: "validation failed",
		},
	}

	p := parser.NewParser()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := p.ParseBytes([]byte(tt.yaml))

			if err == nil {
				t.Fatal("Expected validation error, got nil")
			}

			if !strings.Contains(err.Error(), tt.expectedErr) {
				t.Errorf("Expected error containing '%s', got %v", tt.expectedErr, err)
			}
		})
	}
}

func TestParser_ParseFile(t *testing.T) {
	p := parser.NewParser()
	
	// Test parsing the example file we created
	plan, err := p.ParseFile("../fixtures/example_test_plan.yaml")
	
	if err != nil {
		t.Fatalf("Expected no error parsing example file, got %v", err)
	}
	
	if plan.Name != "API Load Test Suite" {
		t.Errorf("Expected name 'API Load Test Suite', got '%s'", plan.Name)
	}
	
	if len(plan.Scenarios) != 3 {
		t.Errorf("Expected 3 scenarios, got %d", len(plan.Scenarios))
	}
	
	// Test that weights are normalized
	totalWeight := 0
	for _, scenario := range plan.Scenarios {
		totalWeight += scenario.Weight
	}
	
	if totalWeight != 100 {
		t.Errorf("Expected total weight to be normalized to 100, got %d", totalWeight)
	}
}

func TestParser_ParseFile_NonExistentFile(t *testing.T) {
	p := parser.NewParser()
	
	_, err := p.ParseFile("non_existent_file.yaml")
	
	if err == nil {
		t.Fatal("Expected error for non-existent file, got nil")
	}
	
	if !strings.Contains(err.Error(), "failed to read file") {
		t.Errorf("Expected 'failed to read file' error, got %v", err)
	}
}

func TestDuration_UnmarshalYAML(t *testing.T) {
	tests := []struct {
		name     string
		yaml     string
		expected time.Duration
		hasError bool
	}{
		{
			name:     "seconds",
			yaml:     "duration: \"30s\"",
			expected: 30 * time.Second,
			hasError: false,
		},
		{
			name:     "minutes",
			yaml:     "duration: \"5m\"",
			expected: 5 * time.Minute,
			hasError: false,
		},
		{
			name:     "hours",
			yaml:     "duration: \"2h\"",
			expected: 2 * time.Hour,
			hasError: false,
		},
		{
			name:     "complex duration",
			yaml:     "duration: \"1h30m45s\"",
			expected: 1*time.Hour + 30*time.Minute + 45*time.Second,
			hasError: false,
		},
		{
			name:     "invalid duration",
			yaml:     "duration: \"invalid\"",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			yamlData := `
version: "1.0"
name: "Test"
` + tt.yaml + `
concurrency: 1
scenarios:
  - name: "test"
    requests:
      - method: "GET"
        url: "/test"
`

			p := parser.NewParser()
			plan, err := p.ParseBytes([]byte(yamlData))

			if tt.hasError {
				if err == nil {
					t.Fatal("Expected error, got nil")
				}
				return
			}

			if err != nil {
				t.Fatalf("Expected no error, got %v", err)
			}

			if plan.Duration.Duration != tt.expected {
				t.Errorf("Expected duration %v, got %v", tt.expected, plan.Duration.Duration)
			}
		})
	}
}

func TestParser_SetDefaults(t *testing.T) {
	yamlData := `
version: "1.0"
name: "Test Plan"
duration: "1m"
concurrency: 1
scenarios:
  - name: "test"
    requests:
      - method: "GET"
        url: "/test"
`

	p := parser.NewParser()
	plan, err := p.ParseBytes([]byte(yamlData))

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	// Test default output format
	if len(plan.Output.Format) == 0 || plan.Output.Format[0] != "json" {
		t.Errorf("Expected default output format to be ['json'], got %v", plan.Output.Format)
	}

	// Test default metrics
	expectedMetrics := []string{"response_time", "throughput", "error_rate"}
	if len(plan.Output.Metrics) != len(expectedMetrics) {
		t.Errorf("Expected %d default metrics, got %d", len(expectedMetrics), len(plan.Output.Metrics))
	}

	// Test default timeout
	if plan.Global.Timeout.Duration != 30*time.Second {
		t.Errorf("Expected default global timeout 30s, got %v", plan.Global.Timeout.Duration)
	}

	// Test default scenario weight
	if plan.Scenarios[0].Weight != 100 {
		t.Errorf("Expected default scenario weight 100, got %d", plan.Scenarios[0].Weight)
	}
} 