package unit

import (
	"neuralmetergo/internal/worker"
	"testing"
	"time"
)

func TestWorkerPoolCreation(t *testing.T) {
	jobQueue := worker.NewJobQueue(100)
	pool := worker.NewWorkerPool(5, jobQueue)

	if pool == nil {
		t.Fatal("Expected worker pool to be created, got nil")
	}

	if !pool.IsInitialized() == false {
		t.Fatal("Expected pool to not be initialized initially")
	}

	if pool.IsRunning() {
		t.<PERSON>("Expected pool to not be running initially")
	}
}

func TestWorkerPoolWithConfig(t *testing.T) {
	config := worker.DefaultPoolConfig()
	config.MinWorkers = 2
	config.MaxWorkers = 8
	config.HealthCheckInterval = 10 * time.Second

	jobQueue := worker.NewJobQueue(100)
	pool := worker.NewWorkerPoolWithConfig(config, jobQueue)

	if pool == nil {
		t.Fatal("Expected worker pool to be created, got nil")
	}

	poolConfig := pool.GetConfig()
	if poolConfig.MinWorkers != 2 {
		t.<PERSON><PERSON>("Expected MinWorkers to be 2, got %d", poolConfig.MinWorkers)
	}

	if poolConfig.MaxWorkers != 8 {
		t.Errorf("Expected MaxWorkers to be 8, got %d", poolConfig.MaxWorkers)
	}

	if poolConfig.HealthCheckInterval != 10*time.Second {
		t.Errorf("Expected HealthCheckInterval to be 10s, got %v", poolConfig.HealthCheckInterval)
	}
}

func TestWorkerPoolInitialization(t *testing.T) {
	config := worker.DefaultPoolConfig()
	config.MinWorkers = 3
	config.MaxWorkers = 10

	jobQueue := worker.NewJobQueue(100)
	pool := worker.NewWorkerPoolWithConfig(config, jobQueue)

	// Initialize the pool
	err := pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize pool: %v", err)
	}

	if !pool.IsInitialized() {
		t.Fatal("Expected pool to be initialized")
	}

	// Verify minimum workers were created
	workerCount := pool.GetWorkerCount()
	if workerCount != 3 {
		t.Errorf("Expected 3 workers, got %d", workerCount)
	}

	// Test double initialization should fail
	err = pool.Initialize()
	if err == nil {
		t.Fatal("Expected error on double initialization, got nil")
	}
}

func TestWorkerPoolStartStop(t *testing.T) {
	config := worker.DefaultPoolConfig()
	config.MinWorkers = 2
	config.MaxWorkers = 5
	config.HealthCheckInterval = 100 * time.Millisecond // Short interval for testing
	config.ShutdownTimeout = 2 * time.Second            // Shorter timeout for testing

	jobQueue := worker.NewJobQueue(100)
	pool := worker.NewWorkerPoolWithConfig(config, jobQueue)

	// Start the pool
	err := pool.Start()
	if err != nil {
		t.Fatalf("Failed to start pool: %v", err)
	}

	if !pool.IsRunning() {
		t.Fatal("Expected pool to be running")
	}

	if !pool.IsInitialized() {
		t.Fatal("Expected pool to be initialized after start")
	}

	// Let it run for a short time to ensure health monitoring starts
	time.Sleep(200 * time.Millisecond)

	// Stop the pool
	err = pool.Stop()
	if err != nil {
		t.Fatalf("Failed to stop pool: %v", err)
	}

	if pool.IsRunning() {
		t.Fatal("Expected pool to not be running after stop")
	}

	// Test double start should fail
	pool2 := worker.NewWorkerPool(3, jobQueue)
	err = pool2.Start()
	if err != nil {
		t.Fatalf("Failed to start second pool: %v", err)
	}

	err = pool2.Start()
	if err == nil {
		t.Fatal("Expected error on double start, got nil")
	}

	pool2.Stop()
}

func TestWorkerPoolJobProcessing(t *testing.T) {
	config := worker.DefaultPoolConfig()
	config.MinWorkers = 2
	config.MaxWorkers = 4
	config.HealthCheckInterval = 0 // Disable health monitoring for this test

	jobQueue := worker.NewJobQueue(100)
	pool := worker.NewWorkerPoolWithConfig(config, jobQueue)

	// Start the pool
	err := pool.Start()
	if err != nil {
		t.Fatalf("Failed to start pool: %v", err)
	}
	defer pool.Stop()

	// Add some test jobs
	testJobs := []worker.Job{
		{
			ID:      "test-1",
			Type:    "delay",
			Payload: map[string]interface{}{"delay_ms": float64(50)},
		},
		{
			ID:      "test-2",
			Type:    "delay",
			Payload: map[string]interface{}{"delay_ms": float64(30)},
		},
		{
			ID:      "test-3",
			Type:    "delay",
			Payload: map[string]interface{}{"delay_ms": float64(40)},
		},
	}

	// Enqueue jobs
	for _, job := range testJobs {
		err := jobQueue.Enqueue(job)
		if err != nil {
			t.Fatalf("Failed to enqueue job %s: %v", job.ID, err)
		}
	}

	// Wait for jobs to be processed
	processed := 0
	timeout := time.After(5 * time.Second)

	for processed < len(testJobs) {
		select {
		case <-timeout:
			t.Fatalf("Timeout waiting for jobs to complete, processed %d/%d", processed, len(testJobs))
		default:
			result, err := jobQueue.DequeueResult()
			if err != nil {
				// No result available yet, continue polling
				time.Sleep(10 * time.Millisecond)
				continue
			}
			if result.Success {
				processed++
				t.Logf("Job %s processed successfully in %dms by worker %d",
					result.JobID, result.Duration, result.WorkerID)
			} else {
				t.Errorf("Job %s failed: %s", result.JobID, result.Error)
			}
		}
	}

	// Verify metrics
	metrics := pool.GetMetrics()
	if metrics.TotalJobsProcessed < int64(len(testJobs)) {
		t.Errorf("Expected at least %d jobs processed, got %d", len(testJobs), metrics.TotalJobsProcessed)
	}
}

func TestWorkerPoolMetrics(t *testing.T) {
	config := worker.DefaultPoolConfig()
	config.MinWorkers = 1
	config.MaxWorkers = 3
	config.ShutdownTimeout = 2 * time.Second // Shorter timeout for testing

	jobQueue := worker.NewJobQueue(50)
	pool := worker.NewWorkerPoolWithConfig(config, jobQueue)

	// Start the pool
	err := pool.Start()
	if err != nil {
		t.Fatalf("Failed to start pool: %v", err)
	}
	defer pool.Stop()

	// Get initial metrics
	metrics := pool.GetMetrics()
	if metrics.StartTime.IsZero() {
		t.Error("Expected StartTime to be set in metrics")
	}

	if metrics.ActiveWorkers == 0 {
		t.Error("Expected some active workers in metrics")
	}

	// Process a job to update metrics
	job := worker.Job{
		ID:      "metrics-test",
		Type:    "delay",
		Payload: map[string]interface{}{"delay_ms": float64(10)},
	}

	err = jobQueue.Enqueue(job)
	if err != nil {
		t.Fatalf("Failed to enqueue job: %v", err)
	}

	// Wait for job to be processed
	timeout := time.After(2 * time.Second)
	for {
		select {
		case <-timeout:
			t.Fatal("Timeout waiting for job to complete")
		default:
			result, err := jobQueue.DequeueResult()
			if err != nil {
				// No result available yet, continue polling
				time.Sleep(10 * time.Millisecond)
				continue
			}
			if !result.Success {
				t.Errorf("Job failed: %s", result.Error)
			}
			goto testComplete
		}
	}
testComplete:

	// Check updated metrics
	metrics = pool.GetMetrics()
	if metrics.TotalJobsProcessed == 0 {
		t.Error("Expected job count to be updated in metrics")
	}

	if metrics.PoolUptime == 0 {
		t.Error("Expected pool uptime to be positive")
	}
}

func TestWorkerPoolConfiguration(t *testing.T) {
	jobQueue := worker.NewJobQueue(100)
	pool := worker.NewWorkerPool(5, jobQueue)

	// Test configuration update
	newConfig := worker.DefaultPoolConfig()
	newConfig.MinWorkers = 3
	newConfig.MaxWorkers = 8
	newConfig.AutoScale = false

	err := pool.UpdateConfig(newConfig)
	if err != nil {
		t.Fatalf("Failed to update config: %v", err)
	}

	config := pool.GetConfig()
	if config.MinWorkers != 3 {
		t.Errorf("Expected MinWorkers to be 3, got %d", config.MinWorkers)
	}

	if config.MaxWorkers != 8 {
		t.Errorf("Expected MaxWorkers to be 8, got %d", config.MaxWorkers)
	}

	if config.AutoScale != false {
		t.Errorf("Expected AutoScale to be false, got %v", config.AutoScale)
	}

	// Test invalid configuration
	invalidConfig := worker.DefaultPoolConfig()
	invalidConfig.MinWorkers = 0

	err = pool.UpdateConfig(invalidConfig)
	if err == nil {
		t.Fatal("Expected error for invalid min workers, got nil")
	}

	invalidConfig2 := worker.DefaultPoolConfig()
	invalidConfig2.MinWorkers = 5
	invalidConfig2.MaxWorkers = 3

	err = pool.UpdateConfig(invalidConfig2)
	if err == nil {
		t.Fatal("Expected error when max < min workers, got nil")
	}
}

func TestDefaultPoolConfig(t *testing.T) {
	config := worker.DefaultPoolConfig()

	if config.MinWorkers != 1 {
		t.Errorf("Expected default MinWorkers to be 1, got %d", config.MinWorkers)
	}

	if config.MaxWorkers != 10 {
		t.Errorf("Expected default MaxWorkers to be 10, got %d", config.MaxWorkers)
	}

	if config.HealthCheckInterval != 30*time.Second {
		t.Errorf("Expected default HealthCheckInterval to be 30s, got %v", config.HealthCheckInterval)
	}

	if !config.AutoScale {
		t.Error("Expected default AutoScale to be true")
	}

	if config.ScaleUpThreshold != 0.8 {
		t.Errorf("Expected default ScaleUpThreshold to be 0.8, got %f", config.ScaleUpThreshold)
	}

	if config.ScaleDownThreshold != 0.3 {
		t.Errorf("Expected default ScaleDownThreshold to be 0.3, got %f", config.ScaleDownThreshold)
	}
}
