package unit

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/config"
)

func TestConfigManager_LoadYAML(t *testing.T) {
	configPath := filepath.Join("..", "fixtures", "config.yaml")

	// Test loading YAML configuration
	cm, err := config.NewConfigManager(configPath)
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	cfg := cm.Get()
	if cfg == nil {
		t.Fatal("Config is nil")
	}

	// Test basic structure
	if cfg.Server.Host != "localhost" {
		t.Errorf("Expected server host 'localhost', got '%s'", cfg.Server.Host)
	}
	if cfg.Server.Port != 8080 {
		t.Errorf("Expected server port 8080, got %d", cfg.Server.Port)
	}
	if cfg.LoadTest.DefaultConcurrency != 10 {
		t.<PERSON><PERSON>rf("Expected default concurrency 10, got %d", cfg.LoadTest.DefaultConcurrency)
	}
	if cfg.Global.LogLevel != "info" {
		t.Errorf("Expected log level 'info', got '%s'", cfg.Global.LogLevel)
	}
}

func TestConfigManager_LoadJSON(t *testing.T) {
	configPath := filepath.Join("..", "fixtures", "config.json")

	// Test loading JSON configuration
	cm, err := config.NewConfigManager(configPath)
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	cfg := cm.Get()
	if cfg == nil {
		t.Fatal("Config is nil")
	}

	// Test basic structure
	if cfg.Server.Host != "localhost" {
		t.Errorf("Expected server host 'localhost', got '%s'", cfg.Server.Host)
	}
	if cfg.Server.Port != 8080 {
		t.Errorf("Expected server port 8080, got %d", cfg.Server.Port)
	}
	if cfg.LoadTest.DefaultConcurrency != 10 {
		t.Errorf("Expected default concurrency 10, got %d", cfg.LoadTest.DefaultConcurrency)
	}
}

func TestConfigManager_EnvironmentVariableSubstitution(t *testing.T) {
	// Set environment variables for testing
	os.Setenv("TEST_HOST", "testhost")
	os.Setenv("TEST_PORT", "9090")
	defer func() {
		os.Unsetenv("TEST_HOST")
		os.Unsetenv("TEST_PORT")
	}()

	// Create a temporary config file with environment variables
	configContent := `
server:
  host: "${TEST_HOST:localhost}"
  port: ${TEST_PORT:8080}
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	// Create temporary file
	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	// Test loading with environment variable substitution
	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	cfg := cm.Get()
	if cfg.Server.Host != "testhost" {
		t.Errorf("Expected server host 'testhost', got '%s'", cfg.Server.Host)
	}
	if cfg.Server.Port != 9090 {
		t.Errorf("Expected server port 9090, got %d", cfg.Server.Port)
	}
}

func TestConfigManager_DefaultValues(t *testing.T) {
	// Create minimal config
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	cfg := cm.Get()

	// Test default values are set
	if cfg.Server.IdleTimeout.ToDuration() != 60*time.Second {
		t.Errorf("Expected idle timeout 60s, got %v", cfg.Server.IdleTimeout)
	}
	if cfg.LoadTest.RampUpDuration.ToDuration() != 10*time.Second {
		t.Errorf("Expected ramp up duration 10s, got %v", cfg.LoadTest.RampUpDuration)
	}
	if cfg.Metrics.CollectionInterval.ToDuration() != 1*time.Second {
		t.Errorf("Expected collection interval 1s, got %v", cfg.Metrics.CollectionInterval)
	}
	if cfg.Worker.RetryDelay.ToDuration() != 1*time.Second {
		t.Errorf("Expected retry delay 1s, got %v", cfg.Worker.RetryDelay)
	}
	if cfg.Global.ConfigDir != "./config" {
		t.Errorf("Expected config dir './config', got '%s'", cfg.Global.ConfigDir)
	}
}

func TestConfigManager_Validation(t *testing.T) {
	tests := []struct {
		name          string
		configContent string
		expectedError string
	}{
		{
			name: "Invalid server port",
			configContent: `
server:
  host: "localhost"
  port: 99999
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`,
			expectedError: "validation failed",
		},
		{
			name: "Invalid output format",
			configContent: `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "invalid"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`,
			expectedError: "validation failed",
		},
		{
			name: "Invalid log level",
			configContent: `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "invalid"
  environment: "development"
`,
			expectedError: "validation failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
			if err != nil {
				t.Fatalf("Failed to create temp file: %v", err)
			}
			defer os.Remove(tmpFile.Name())

			if _, err := tmpFile.Write([]byte(tt.configContent)); err != nil {
				t.Fatalf("Failed to write temp file: %v", err)
			}
			tmpFile.Close()

			_, err = config.NewConfigManager(tmpFile.Name())
			if err == nil {
				t.Errorf("Expected error containing '%s', got nil", tt.expectedError)
			} else if err.Error() == "" {
				t.Errorf("Expected error containing '%s', got empty error", tt.expectedError)
			}
		})
	}
}

func TestConfigManager_CustomValidation(t *testing.T) {
	// Test basic custom validation (TLS validation)
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  tls:
    enabled: true
    cert_file: ""  # Empty cert file should cause error
    key_file: ""   # Empty key file should cause error
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	_, err = config.NewConfigManager(tmpFile.Name())
	if err == nil {
		t.Error("Expected error for TLS misconfiguration, got nil")
	}
}

func TestConfigManager_LoadFromBytes(t *testing.T) {
	yamlContent := []byte(`
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`)

	cfg, err := config.LoadFromBytes(yamlContent, config.FormatYAML)
	if err != nil {
		t.Fatalf("Failed to load config from bytes: %v", err)
	}

	if cfg.Server.Host != "localhost" {
		t.Errorf("Expected server host 'localhost', got '%s'", cfg.Server.Host)
	}
	if cfg.Server.Port != 8080 {
		t.Errorf("Expected server port 8080, got %d", cfg.Server.Port)
	}
}

func TestConfigManager_LoadFromFile(t *testing.T) {
	configPath := filepath.Join("..", "fixtures", "config.yaml")

	cfg, err := config.LoadFromFile(configPath)
	if err != nil {
		t.Fatalf("Failed to load config from file: %v", err)
	}

	if cfg.Server.Host != "localhost" {
		t.Errorf("Expected server host 'localhost', got '%s'", cfg.Server.Host)
	}
	if cfg.LoadTest.DefaultConcurrency != 10 {
		t.Errorf("Expected default concurrency 10, got %d", cfg.LoadTest.DefaultConcurrency)
	}
}

func TestConfigManager_Reload(t *testing.T) {
	// Create initial config
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	// Create config manager
	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Verify initial config
	cfg := cm.Get()
	if cfg.LoadTest.DefaultConcurrency != 10 {
		t.Errorf("Expected initial concurrency 10, got %d", cfg.LoadTest.DefaultConcurrency)
	}

	// Update config file
	updatedContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 20
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	if err := os.WriteFile(tmpFile.Name(), []byte(updatedContent), 0644); err != nil {
		t.Fatalf("Failed to update config file: %v", err)
	}

	// Reload config
	if err := cm.Reload(); err != nil {
		t.Fatalf("Failed to reload config: %v", err)
	}

	// Verify updated config
	cfg = cm.Get()
	if cfg.LoadTest.DefaultConcurrency != 20 {
		t.Errorf("Expected updated concurrency 20, got %d", cfg.LoadTest.DefaultConcurrency)
	}
}

func TestConfigManager_InvalidPath(t *testing.T) {
	_, err := config.NewConfigManager("/nonexistent/path/config.yaml")
	if err == nil {
		t.Error("Expected error for nonexistent path, got nil")
	}
}

func TestConfigManager_EmptyPath(t *testing.T) {
	_, err := config.NewConfigManager("")
	if err == nil {
		t.Error("Expected error for empty path, got nil")
	}
}

func TestConfigManager_UnsupportedFormat(t *testing.T) {
	tmpFile, err := os.CreateTemp("", "test_config_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	_, err = config.NewConfigManager(tmpFile.Name())
	if err == nil {
		t.Error("Expected error for unsupported format, got nil")
	}
}

func TestConfigManager_ConfigMerging(t *testing.T) {
	// Create base configuration
	baseConfig := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	// Create override configuration
	overrideConfig := `
server:
  host: "override-host"
  port: 9090
load_test:
  default_concurrency: 20
metrics:
  buffer_size: 2000
global:
  log_level: "debug"
  variables:
    custom_var: "custom_value"
`

	// Create temporary files
	baseFile, err := os.CreateTemp("", "base_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create base config file: %v", err)
	}
	defer os.Remove(baseFile.Name())

	overrideFile, err := os.CreateTemp("", "override_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create override config file: %v", err)
	}
	defer os.Remove(overrideFile.Name())

	// Write configurations
	if _, err := baseFile.Write([]byte(baseConfig)); err != nil {
		t.Fatalf("Failed to write base config: %v", err)
	}
	baseFile.Close()

	if _, err := overrideFile.Write([]byte(overrideConfig)); err != nil {
		t.Fatalf("Failed to write override config: %v", err)
	}
	overrideFile.Close()

	// Create config sources
	sources := []config.ConfigSource{
		{Path: baseFile.Name(), Priority: 1, Format: config.FormatYAML},
		{Path: overrideFile.Name(), Priority: 2, Format: config.FormatYAML},
	}

	// Test merging
	cm, err := config.NewConfigManagerWithSources(sources)
	if err != nil {
		t.Fatalf("Failed to create config manager with sources: %v", err)
	}

	cfg := cm.Get()

	// Test that override values took precedence
	if cfg.Server.Host != "override-host" {
		t.Errorf("Expected server host 'override-host', got '%s'", cfg.Server.Host)
	}
	if cfg.Server.Port != 9090 {
		t.Errorf("Expected server port 9090, got %d", cfg.Server.Port)
	}
	if cfg.LoadTest.DefaultConcurrency != 20 {
		t.Errorf("Expected default concurrency 20, got %d", cfg.LoadTest.DefaultConcurrency)
	}
	if cfg.Metrics.BufferSize != 2000 {
		t.Errorf("Expected buffer size 2000, got %d", cfg.Metrics.BufferSize)
	}
	if cfg.Global.LogLevel != "debug" {
		t.Errorf("Expected log level 'debug', got '%s'", cfg.Global.LogLevel)
	}

	// Test that base values were preserved where not overridden
	if cfg.LoadTest.DefaultDuration.ToDuration() != 60*time.Second {
		t.Errorf("Expected default duration 60s, got %v", cfg.LoadTest.DefaultDuration)
	}
	if cfg.LoadTest.MaxConcurrency != 1000 {
		t.Errorf("Expected max concurrency 1000, got %d", cfg.LoadTest.MaxConcurrency)
	}
	if cfg.Output.Format != "json" {
		t.Errorf("Expected output format 'json', got '%s'", cfg.Output.Format)
	}

	// Test that merged variables work
	if cfg.Global.Variables["custom_var"] != "custom_value" {
		t.Errorf("Expected custom_var 'custom_value', got '%s'", cfg.Global.Variables["custom_var"])
	}
}

func TestConfigManager_MultipleSourcePriorities(t *testing.T) {
	// Create three configuration files with different priorities
	config1 := `
server:
  host: "host1"
  port: 8081
global:
  log_level: "info"
`

	config2 := `
server:
  host: "host2"
  port: 8082
global:
  log_level: "warn"
`

	config3 := `
server:
  host: "host3"
global:
  log_level: "debug"
`

	// Create temporary files
	file1, err := os.CreateTemp("", "config1_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create config1 file: %v", err)
	}
	defer os.Remove(file1.Name())

	file2, err := os.CreateTemp("", "config2_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create config2 file: %v", err)
	}
	defer os.Remove(file2.Name())

	file3, err := os.CreateTemp("", "config3_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create config3 file: %v", err)
	}
	defer os.Remove(file3.Name())

	// Write configurations
	configs := []struct {
		file    *os.File
		content string
	}{
		{file1, config1},
		{file2, config2},
		{file3, config3},
	}

	for _, cfg := range configs {
		if _, err := cfg.file.Write([]byte(cfg.content)); err != nil {
			t.Fatalf("Failed to write config: %v", err)
		}
		cfg.file.Close()
	}

	// Test with different priority orders
	sources := []config.ConfigSource{
		{Path: file1.Name(), Priority: 3, Format: config.FormatYAML}, // Highest priority
		{Path: file2.Name(), Priority: 1, Format: config.FormatYAML}, // Lowest priority
		{Path: file3.Name(), Priority: 2, Format: config.FormatYAML}, // Medium priority
	}

	cm, err := config.NewConfigManagerWithSources(sources)
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	cfg := cm.Get()

	// Highest priority (file1) should win for all values
	if cfg.Server.Host != "host1" {
		t.Errorf("Expected server host 'host1', got '%s'", cfg.Server.Host)
	}
	if cfg.Server.Port != 8081 {
		t.Errorf("Expected server port 8081, got %d", cfg.Server.Port)
	}
	if cfg.Global.LogLevel != "info" {
		t.Errorf("Expected log level 'info', got '%s'", cfg.Global.LogLevel)
	}
}

func TestConfigManager_ReloadWithCallback(t *testing.T) {
	// Create initial config
	initialConfig := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "reload_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(initialConfig)); err != nil {
		t.Fatalf("Failed to write initial config: %v", err)
	}
	tmpFile.Close()

	// Create config manager
	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Verify initial config
	cfg := cm.Get()
	if cfg.LoadTest.DefaultConcurrency != 10 {
		t.Errorf("Expected initial concurrency 10, got %d", cfg.LoadTest.DefaultConcurrency)
	}

	// Update config file
	updatedConfig := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 25
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	if err := os.WriteFile(tmpFile.Name(), []byte(updatedConfig), 0644); err != nil {
		t.Fatalf("Failed to update config file: %v", err)
	}

	// Test successful callback
	callbackCalled := false
	err = cm.ReloadWithCallback(func(newConfig *config.Config) error {
		callbackCalled = true
		if newConfig.LoadTest.DefaultConcurrency != 25 {
			t.Errorf("Expected callback config concurrency 25, got %d", newConfig.LoadTest.DefaultConcurrency)
		}
		return nil
	})

	if err != nil {
		t.Fatalf("ReloadWithCallback failed: %v", err)
	}

	if !callbackCalled {
		t.Error("Callback was not called")
	}

	// Verify config was updated
	cfg = cm.Get()
	if cfg.LoadTest.DefaultConcurrency != 25 {
		t.Errorf("Expected updated concurrency 25, got %d", cfg.LoadTest.DefaultConcurrency)
	}

	// Test failing callback (should rollback)
	failingConfig := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 50
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	if err := os.WriteFile(tmpFile.Name(), []byte(failingConfig), 0644); err != nil {
		t.Fatalf("Failed to update config file: %v", err)
	}

	err = cm.ReloadWithCallback(func(newConfig *config.Config) error {
		return fmt.Errorf("callback failed")
	})

	if err == nil {
		t.Error("Expected callback failure error, got nil")
	}

	// Verify config was rolled back
	cfg = cm.Get()
	if cfg.LoadTest.DefaultConcurrency != 25 {
		t.Errorf("Expected rolled back concurrency 25, got %d", cfg.LoadTest.DefaultConcurrency)
	}
}

func TestConfigManager_AddSource(t *testing.T) {
	// Create initial config
	initialConfig := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "initial_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(initialConfig)); err != nil {
		t.Fatalf("Failed to write initial config: %v", err)
	}
	tmpFile.Close()

	// Create config manager
	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Create additional source
	additionalConfig := `
server:
  host: "new-host"
global:
  log_level: "debug"
`

	additionalFile, err := os.CreateTemp("", "additional_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create additional config file: %v", err)
	}
	defer os.Remove(additionalFile.Name())

	if _, err := additionalFile.Write([]byte(additionalConfig)); err != nil {
		t.Fatalf("Failed to write additional config: %v", err)
	}
	additionalFile.Close()

	// Add source
	err = cm.AddSource(additionalFile.Name(), 2)
	if err != nil {
		t.Fatalf("Failed to add source: %v", err)
	}

	// Test adding non-existent source
	err = cm.AddSource("/nonexistent/path/config.yaml", 1)
	if err == nil {
		t.Error("Expected error for non-existent source, got nil")
	}
}

func TestConfigManager_EmptySourcesList(t *testing.T) {
	sources := []config.ConfigSource{}
	_, err := config.NewConfigManagerWithSources(sources)
	if err == nil {
		t.Error("Expected error for empty sources list, got nil")
	}
}

// TestConfigManager_DirectEnvironmentVariableMapping tests the new direct environment variable mapping
func TestConfigManager_DirectEnvironmentVariableMapping(t *testing.T) {
	// Set environment variables for testing
	envVars := map[string]string{
		"NEURALMETER_SERVER_HOST":                  "envhost",
		"NEURALMETER_SERVER_PORT":                  "9999",
		"NEURALMETER_SERVER_READ_TIMEOUT":          "45s",
		"NEURALMETER_LOADTEST_DEFAULT_CONCURRENCY": "25",
		"NEURALMETER_METRICS_ENABLED":              "true",
		"NEURALMETER_OUTPUT_VERBOSE":               "false",
		"NEURALMETER_GLOBAL_DEBUG":                 "true",
		"NEURALMETER_GLOBAL_LOG_LEVEL":             "debug",
	}

	// Set environment variables
	for key, value := range envVars {
		os.Setenv(key, value)
	}
	defer func() {
		for key := range envVars {
			os.Unsetenv(key)
		}
	}()

	// Create config manager from environment only
	cm, err := config.NewConfigManagerFromEnvironment()
	if err != nil {
		t.Fatalf("Failed to create config manager from environment: %v", err)
	}

	cfg := cm.Get()

	// Test that environment variables override defaults
	if cfg.Server.Host != "envhost" {
		t.Errorf("Expected server host 'envhost', got '%s'", cfg.Server.Host)
	}
	if cfg.Server.Port != 9999 {
		t.Errorf("Expected server port 9999, got %d", cfg.Server.Port)
	}
	if cfg.Server.ReadTimeout.ToDuration() != 45*time.Second {
		t.Errorf("Expected read timeout 45s, got %v", cfg.Server.ReadTimeout)
	}
	if cfg.LoadTest.DefaultConcurrency != 25 {
		t.Errorf("Expected default concurrency 25, got %d", cfg.LoadTest.DefaultConcurrency)
	}
	if !cfg.Metrics.Enabled {
		t.Error("Expected metrics enabled to be true")
	}
	if cfg.Output.Verbose {
		t.Error("Expected output verbose to be false")
	}
	if !cfg.Global.Debug {
		t.Error("Expected global debug to be true")
	}
	if cfg.Global.LogLevel != "debug" {
		t.Errorf("Expected log level 'debug', got '%s'", cfg.Global.LogLevel)
	}
}

// TestConfigManager_EnvironmentVariablePrecedence tests precedence handling
func TestConfigManager_EnvironmentVariablePrecedence(t *testing.T) {
	// Create a temporary config file
	configContent := `
server:
  host: "filehost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: false
  buffer_size: 1000
output:
  format: "json"
  verbose: false
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
  debug: false
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	// Set environment variables that should override file values
	envVars := map[string]string{
		"NEURALMETER_SERVER_HOST":      "envhost",
		"NEURALMETER_SERVER_PORT":      "9999",
		"NEURALMETER_METRICS_ENABLED":  "true",
		"NEURALMETER_OUTPUT_VERBOSE":   "true",
		"NEURALMETER_GLOBAL_DEBUG":     "true",
		"NEURALMETER_GLOBAL_LOG_LEVEL": "debug",
	}

	for key, value := range envVars {
		os.Setenv(key, value)
	}
	defer func() {
		for key := range envVars {
			os.Unsetenv(key)
		}
	}()

	// Create config manager and load with precedence
	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	if err := cm.LoadWithEnvironmentPrecedence(); err != nil {
		t.Fatalf("Failed to load with environment precedence: %v", err)
	}

	cfg := cm.Get()

	// Test that environment variables override file values
	if cfg.Server.Host != "envhost" {
		t.Errorf("Expected server host 'envhost' (from env), got '%s'", cfg.Server.Host)
	}
	if cfg.Server.Port != 9999 {
		t.Errorf("Expected server port 9999 (from env), got %d", cfg.Server.Port)
	}
	if !cfg.Metrics.Enabled {
		t.Error("Expected metrics enabled true (from env)")
	}
	if !cfg.Output.Verbose {
		t.Error("Expected output verbose true (from env)")
	}
	if !cfg.Global.Debug {
		t.Error("Expected global debug true (from env)")
	}
	if cfg.Global.LogLevel != "debug" {
		t.Errorf("Expected log level 'debug' (from env), got '%s'", cfg.Global.LogLevel)
	}

	// Test that non-overridden values come from file
	if cfg.Server.ReadTimeout.ToDuration() != 30*time.Second {
		t.Errorf("Expected read timeout 30s (from file), got %v", cfg.Server.ReadTimeout)
	}
	if cfg.LoadTest.DefaultConcurrency != 10 {
		t.Errorf("Expected default concurrency 10 (from file), got %d", cfg.LoadTest.DefaultConcurrency)
	}
}

// TestConfigManager_EnvironmentVariableTypeConversion tests type conversion
func TestConfigManager_EnvironmentVariableTypeConversion(t *testing.T) {
	testCases := []struct {
		name     string
		envKey   string
		envValue string
		testFunc func(*config.Config) error
	}{
		{
			name:     "String conversion",
			envKey:   "NEURALMETER_SERVER_HOST",
			envValue: "testhost",
			testFunc: func(cfg *config.Config) error {
				if cfg.Server.Host != "testhost" {
					return fmt.Errorf("expected 'testhost', got '%s'", cfg.Server.Host)
				}
				return nil
			},
		},
		{
			name:     "Integer conversion",
			envKey:   "NEURALMETER_SERVER_PORT",
			envValue: "8888",
			testFunc: func(cfg *config.Config) error {
				if cfg.Server.Port != 8888 {
					return fmt.Errorf("expected 8888, got %d", cfg.Server.Port)
				}
				return nil
			},
		},
		{
			name:     "Boolean true conversion",
			envKey:   "NEURALMETER_METRICS_ENABLED",
			envValue: "true",
			testFunc: func(cfg *config.Config) error {
				if !cfg.Metrics.Enabled {
					return fmt.Errorf("expected true, got false")
				}
				return nil
			},
		},
		{
			name:     "Boolean false conversion",
			envKey:   "NEURALMETER_OUTPUT_VERBOSE",
			envValue: "false",
			testFunc: func(cfg *config.Config) error {
				if cfg.Output.Verbose {
					return fmt.Errorf("expected false, got true")
				}
				return nil
			},
		},
		{
			name:     "Duration conversion",
			envKey:   "NEURALMETER_SERVER_READ_TIMEOUT",
			envValue: "45s",
			testFunc: func(cfg *config.Config) error {
				if cfg.Server.ReadTimeout.ToDuration() != 45*time.Second {
					return fmt.Errorf("expected 45s, got %v", cfg.Server.ReadTimeout)
				}
				return nil
			},
		},
		{
			name:     "JSON object conversion",
			envKey:   "NEURALMETER_OUTPUT_TEMPLATES",
			envValue: `{"html":"template.html","text":"template.txt"}`,
			testFunc: func(cfg *config.Config) error {
				if len(cfg.Output.Templates) != 2 {
					return fmt.Errorf("expected 2 templates, got %d", len(cfg.Output.Templates))
				}
				if cfg.Output.Templates["html"] != "template.html" {
					return fmt.Errorf("expected 'template.html', got '%s'", cfg.Output.Templates["html"])
				}
				if cfg.Output.Templates["text"] != "template.txt" {
					return fmt.Errorf("expected 'template.txt', got '%s'", cfg.Output.Templates["text"])
				}
				return nil
			},
		},
		{
			name:     "JSON map conversion for global variables",
			envKey:   "NEURALMETER_GLOBAL_VARIABLES",
			envValue: `{"var1":"value1","var2":"value2"}`,
			testFunc: func(cfg *config.Config) error {
				if len(cfg.Global.Variables) != 2 {
					return fmt.Errorf("expected 2 variables, got %d", len(cfg.Global.Variables))
				}
				if cfg.Global.Variables["var1"] != "value1" {
					return fmt.Errorf("expected 'value1', got '%s'", cfg.Global.Variables["var1"])
				}
				if cfg.Global.Variables["var2"] != "value2" {
					return fmt.Errorf("expected 'value2', got '%s'", cfg.Global.Variables["var2"])
				}
				return nil
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Set environment variable
			os.Setenv(tc.envKey, tc.envValue)
			defer os.Unsetenv(tc.envKey)

			// Create config manager from environment
			cm, err := config.NewConfigManagerFromEnvironment()
			if err != nil {
				t.Fatalf("Failed to create config manager: %v", err)
			}

			cfg := cm.Get()

			// Test the conversion
			if err := tc.testFunc(cfg); err != nil {
				t.Errorf("Type conversion test failed: %v", err)
			}
		})
	}
}

// TestConfigManager_EnvironmentVariableErrors tests error handling
func TestConfigManager_EnvironmentVariableErrors(t *testing.T) {
	testCases := []struct {
		name        string
		envKey      string
		envValue    string
		expectError bool
		errorText   string
	}{
		{
			name:        "Invalid integer",
			envKey:      "NEURALMETER_SERVER_PORT",
			envValue:    "invalid",
			expectError: true,
			errorText:   "invalid syntax",
		},
		{
			name:        "Invalid boolean",
			envKey:      "NEURALMETER_METRICS_ENABLED",
			envValue:    "maybe",
			expectError: true,
			errorText:   "invalid syntax",
		},
		{
			name:        "Invalid duration",
			envKey:      "NEURALMETER_SERVER_READ_TIMEOUT",
			envValue:    "invalid",
			expectError: true,
			errorText:   "invalid duration",
		},
		{
			name:        "Invalid JSON",
			envKey:      "NEURALMETER_OUTPUT_TEMPLATES",
			envValue:    `{"invalid": json}`,
			expectError: true,
			errorText:   "invalid JSON",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Set invalid environment variable
			os.Setenv(tc.envKey, tc.envValue)
			defer os.Unsetenv(tc.envKey)

			// Try to create config manager - should fail
			_, err := config.NewConfigManagerFromEnvironment()

			if tc.expectError {
				if err == nil {
					t.Error("Expected error but got none")
				} else if !strings.Contains(err.Error(), tc.errorText) {
					t.Errorf("Expected error containing '%s', got '%s'", tc.errorText, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

// TestConfigManager_EnvironmentVariableValidation tests validation with environment variables
func TestConfigManager_EnvironmentVariableValidation(t *testing.T) {
	// Set environment variables that would fail validation
	envVars := map[string]string{
		"NEURALMETER_SERVER_PORT":                  "80",  // Below minimum
		"NEURALMETER_LOADTEST_DEFAULT_CONCURRENCY": "0",   // Below minimum
		"NEURALMETER_OUTPUT_FORMAT":                "xml", // Invalid format
	}

	for key, value := range envVars {
		os.Setenv(key, value)
	}
	defer func() {
		for key := range envVars {
			os.Unsetenv(key)
		}
	}()

	// Try to create config manager - should fail validation
	_, err := config.NewConfigManagerFromEnvironment()
	if err == nil {
		t.Error("Expected validation error but got none")
	}

	if !strings.Contains(err.Error(), "validation failed") {
		t.Errorf("Expected validation error, got: %v", err)
	}
}

// TestConfigManager_TLSEnvironmentVariables tests TLS-specific environment variables
func TestConfigManager_TLSEnvironmentVariables(t *testing.T) {
	envVars := map[string]string{
		"NEURALMETER_SERVER_TLS_ENABLED":   "true",
		"NEURALMETER_SERVER_TLS_CERT_FILE": "/path/to/cert.pem",
		"NEURALMETER_SERVER_TLS_KEY_FILE":  "/path/to/key.pem",
	}

	for key, value := range envVars {
		os.Setenv(key, value)
	}
	defer func() {
		for key := range envVars {
			os.Unsetenv(key)
		}
	}()

	// Create config manager with a basic config to avoid nil pointer errors
	// We'll use a minimal config that passes basic validation
	basicConfig := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(basicConfig)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Load environment variables
	if err := cm.LoadFromEnvironment(); err != nil {
		t.Fatalf("Failed to load environment variables: %v", err)
	}

	cfg := cm.Get()

	// Test TLS configuration
	if !cfg.Server.TLS.Enabled {
		t.Error("Expected TLS enabled to be true")
	}
	if cfg.Server.TLS.CertFile != "/path/to/cert.pem" {
		t.Errorf("Expected cert file '/path/to/cert.pem', got '%s'", cfg.Server.TLS.CertFile)
	}
	if cfg.Server.TLS.KeyFile != "/path/to/key.pem" {
		t.Errorf("Expected key file '/path/to/key.pem', got '%s'", cfg.Server.TLS.KeyFile)
	}
}

// Test Enhanced Validation Features (Task 44)

func TestConfigManager_EnhancedValidation(t *testing.T) {
	// Test basic enhanced validation
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
  console: true
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()
	if result == nil {
		t.Fatal("Expected validation result, got nil")
	}

	// Should be valid with info messages
	if result.HasErrors() {
		t.Errorf("Expected no errors, got %d errors", len(result.Errors))
		for _, err := range result.Errors {
			t.Logf("Error: %s", err.Message)
		}
	}

	// Should have some info/warning messages for default config
	if !result.HasWarnings() && len(result.Info) == 0 {
		t.Log("No warnings or info messages - this might be expected for a basic config")
	}
}

func TestConfigManager_PortConflictValidation(t *testing.T) {
	// Test port conflict detection - create config with port conflicts from start
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
dashboard:
  enabled: true
  host: "localhost"
  port: 8080  # Same port as server - conflict!
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()

	// Should detect port conflict
	if !result.HasErrors() {
		t.Error("Expected port conflict error, got none")
	}

	foundPortConflict := false
	for _, err := range result.Errors {
		if err.Rule == "port_conflict" {
			foundPortConflict = true
			if !strings.Contains(err.Message, "8080") {
				t.Errorf("Expected port conflict error to mention port 8080, got: %s", err.Message)
			}
			break
		}
	}

	if !foundPortConflict {
		t.Error("Expected to find port conflict error")
	}
}

func TestConfigManager_PrivilegedPortWarning(t *testing.T) {
	// Test privileged port warning - create config with privileged port from start
	configContent := `
server:
  host: "localhost"
  port: 1023  # Privileged port (below 1024) but above struct validation minimum
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()

	// Should warn about privileged port
	foundPrivilegedPortWarning := false
	for _, warning := range result.Warnings {
		if warning.Rule == "privileged_port" {
			foundPrivilegedPortWarning = true
			if !strings.Contains(warning.Message, "1023") {
				t.Errorf("Expected privileged port warning to mention port 1023, got: %s", warning.Message)
			}
			break
		}
	}

	if !foundPrivilegedPortWarning {
		t.Error("Expected to find privileged port warning")
	}
}

func TestConfigManager_LoadTestValidation(t *testing.T) {
	// Test load test configuration validation - create config with invalid settings from start
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "100ms"   # Very short duration
  default_concurrency: 1500   # Exceeds max concurrency  
  max_concurrency: 1000
  ramp_up_duration: "30s"     # Too long relative to duration (30s vs 100ms)
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()

	// Should have errors and warnings
	expectedChecks := map[string]bool{
		"exceeds_max":        false,
		"duration_too_short": false,
		"ramp_duration":      false,
	}

	for _, err := range result.Errors {
		if err.Rule == "exceeds_max" {
			expectedChecks["exceeds_max"] = true
		}
	}

	for _, warning := range result.Warnings {
		if warning.Rule == "duration_too_short" {
			expectedChecks["duration_too_short"] = true
		}
		if warning.Rule == "ramp_duration" {
			expectedChecks["ramp_duration"] = true
		}
	}

	for rule, found := range expectedChecks {
		if !found {
			t.Errorf("Expected to find validation rule: %s", rule)
		}
	}
}

func TestConfigManager_WorkerConfigValidation(t *testing.T) {
	// Test worker configuration validation - create config with invalid settings from start
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 150       # Greater than queue size
  queue_size: 100
  max_retries: 15      # Very high retries
  shutdown_timeout: "1s"  # Too short timeout
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()

	// Check for expected warnings and errors
	expectedWarnings := map[string]bool{
		"pool_queue_ratio":   false,
		"excessive_retries":  false,
		"shutdown_too_short": false,
	}

	for _, warning := range result.Warnings {
		if warning.Rule == "pool_queue_ratio" {
			expectedWarnings["pool_queue_ratio"] = true
		}
		if warning.Rule == "excessive_retries" {
			expectedWarnings["excessive_retries"] = true
		}
		if warning.Rule == "shutdown_too_short" {
			expectedWarnings["shutdown_too_short"] = true
		}
	}

	for rule, found := range expectedWarnings {
		if !found {
			t.Errorf("Expected to find warning rule: %s", rule)
		}
	}
}

func TestConfigManager_SecurityLinting(t *testing.T) {
	// Test security linting in production environment
	configContent := `
server:
  host: "0.0.0.0"  # Binding to all interfaces
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  tls:
    enabled: false  # TLS disabled in production
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 1000
output:
  format: "json"
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "debug"    # Debug logs in production
  environment: "production"
  debug: true           # Debug mode in production
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()

	// Check for security warnings
	securityWarnings := 0
	for _, warning := range result.Warnings {
		if warning.Rule == "security" {
			securityWarnings++
		}
	}

	for _, info := range result.Info {
		if info.Rule == "security" {
			securityWarnings++
		}
	}

	if securityWarnings == 0 {
		t.Error("Expected security warnings/info for production configuration")
	}
}

func TestConfigManager_PerformanceValidation(t *testing.T) {
	// Test performance validation with high resource usage
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "25s"  # Write timeout less than read timeout
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 15000  # Very high concurrency
metrics:
  enabled: true
  buffer_size: 500        # Small buffer size
output:
  format: "json"
worker:
  pool_size: 150          # High pool size
  queue_size: 300
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()

	// Check for performance-related warnings
	expectedPerformanceChecks := map[string]bool{
		"performance":       false,
		"network_resources": false,
		"cpu_usage":         false,
	}

	for _, warning := range result.Warnings {
		if warning.Rule == "performance" {
			expectedPerformanceChecks["performance"] = true
		}
		if warning.Rule == "network_resources" {
			expectedPerformanceChecks["network_resources"] = true
		}
	}

	for _, info := range result.Info {
		if info.Rule == "performance" {
			expectedPerformanceChecks["performance"] = true
		}
		if info.Rule == "cpu_usage" {
			expectedPerformanceChecks["cpu_usage"] = true
		}
	}

	foundAny := false
	for _, found := range expectedPerformanceChecks {
		if found {
			foundAny = true
			break
		}
	}

	if !foundAny {
		t.Error("Expected to find at least one performance-related validation message")
	}
}

func TestConfigManager_CrossFieldDependencies(t *testing.T) {
	// Test cross-field dependency validation
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  collection_interval: "10s"  # Greater than export interval
  export_interval: "5s"
  buffer_size: 50             # Small relative to worker pool
output:
  format: "json"
  console: true  # Only console output
dashboard:
  enabled: true  # Enabled but missing required fields
worker:
  pool_size: 10
  queue_size: 100
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()

	// Check for dependency validation errors
	foundRequiredErrors := 0
	for _, err := range result.Errors {
		if err.Rule == "required_when_enabled" {
			foundRequiredErrors++
		}
	}

	if foundRequiredErrors == 0 {
		t.Error("Expected required_when_enabled errors for dashboard configuration")
	}

	// Check for collection/export interval warning
	foundIntervalWarning := false
	for _, warning := range result.Warnings {
		if warning.Rule == "collection_export_mismatch" {
			foundIntervalWarning = true
			break
		}
	}

	if !foundIntervalWarning {
		t.Error("Expected collection_export_mismatch warning")
	}
}

func TestConfigManager_MemoryEstimation(t *testing.T) {
	// Test memory usage estimation
	configContent := `
server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
metrics:
  enabled: true
  buffer_size: 100000    # Large buffer
output:
  format: "json"
dashboard:
  enabled: true
  host: "localhost"
  port: 8081
  history_limit: 50000   # Large history
worker:
  pool_size: 500         # Large pool
  queue_size: 1000
global:
  log_level: "info"
  environment: "development"
`

	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(configContent)); err != nil {
		t.Fatalf("Failed to write temp file: %v", err)
	}
	tmpFile.Close()

	cm, err := config.NewConfigManager(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	result := cm.EnhancedValidate()

	// Check for memory usage warning
	foundMemoryWarning := false
	for _, warning := range result.Warnings {
		if warning.Rule == "memory_usage" {
			foundMemoryWarning = true
			if !strings.Contains(warning.Message, "MB") {
				t.Errorf("Expected memory warning to include MB, got: %s", warning.Message)
			}
			break
		}
	}

	if !foundMemoryWarning {
		t.Error("Expected memory usage warning for high-resource configuration")
	}
}

func TestConfigManager_ValidationResult_Methods(t *testing.T) {
	// Test ValidationResult methods
	result := &config.ValidationResult{Valid: true}

	// Test AddError
	result.AddError("test.field", "test_rule", "test error message", "test_value")
	if result.Valid {
		t.Error("Expected Valid to be false after adding error")
	}
	if !result.HasErrors() {
		t.Error("Expected HasErrors to return true")
	}
	if len(result.Errors) != 1 {
		t.Errorf("Expected 1 error, got %d", len(result.Errors))
	}

	// Test AddWarning
	result.AddWarning("test.field2", "test_rule2", "test warning message", "test_value2")
	if !result.HasWarnings() {
		t.Error("Expected HasWarnings to return true")
	}
	if len(result.Warnings) != 1 {
		t.Errorf("Expected 1 warning, got %d", len(result.Warnings))
	}

	// Test AddInfo
	result.AddInfo("test.field3", "test_rule3", "test info message", "test_value3")
	if len(result.Info) != 1 {
		t.Errorf("Expected 1 info message, got %d", len(result.Info))
	}

	// Test error formatting
	err := result.Errors[0]
	if !strings.Contains(err.Error(), "test.field") {
		t.Errorf("Expected error string to contain field name, got: %s", err.Error())
	}
}
