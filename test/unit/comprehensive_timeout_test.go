package unit

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

// TestComprehensiveTimeoutIntegration tests the complete timeout system end-to-end
func TestComprehensiveTimeoutIntegration(t *testing.T) {
	log.Println("Starting TestComprehensiveTimeoutIntegration")

	// Create test server with configurable delay
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Delay for 300ms to trigger timeout
		time.Sleep(300 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("delayed response"))
	}))
	defer server.Close()

	// Create HTTP client with default config
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.DisableRetries()       // Disable retries for predictable timeout testing
	httpClient.ResetHTTPMethodStats() // Reset metrics before test

	ctx := context.Background()

	// Create request with 100ms timeout (shorter than server delay)
	req := &client.Request{
		Method:  "GET",
		URL:     server.URL,
		Timeout: 100 * time.Millisecond,
	}

	// Perform request and expect timeout
	log.Printf("Making request to %s with 100ms timeout (server delays 300ms)", server.URL)
	resp, err := httpClient.Execute(ctx, req)

	log.Printf("Request completed. Error: %v", err)
	if err != nil {
		log.Printf("Error type: %T", err)
		log.Printf("Error string contains 'timeout': %v", strings.Contains(err.Error(), "timeout"))
		log.Printf("Error string contains 'deadline exceeded': %v", strings.Contains(err.Error(), "context deadline exceeded"))
	}
	if resp != nil {
		log.Printf("Response received (unexpected): %v", resp.Status)
	}

	// Check that request timed out
	if err == nil {
		t.Error("Expected timeout error, got nil")
		return
	}

	if !strings.Contains(err.Error(), "timeout") && !strings.Contains(err.Error(), "deadline exceeded") {
		t.Errorf("Expected timeout error, got: %v", err)
		return
	}

	// Give a moment for metrics to be recorded
	time.Sleep(10 * time.Millisecond)

	// Check metrics were recorded correctly
	stats := httpClient.GetHTTPMethodStats()
	log.Printf("HTTP Stats after timeout: GetRequests=%d, TimeoutErrors=%d",
		stats.GetRequests, stats.TimeoutErrors)

	if stats.GetRequests != 1 {
		t.Errorf("Expected 1 GET request, got %d", stats.GetRequests)
	}

	if stats.TimeoutErrors == 0 {
		t.Error("Expected timeout error to be recorded in metrics, got 0")
	}

	log.Println("TestComprehensiveTimeoutIntegration completed")
}

// TestTimeoutMetricsAccuracy tests the accuracy of timeout metrics collection
func TestTimeoutMetricsAccuracy(t *testing.T) {
	// Create multiple test scenarios with different timeout behaviors
	testCases := []struct {
		name           string
		serverDelay    time.Duration
		requestTimeout time.Duration
		expectTimeout  bool
	}{
		{"Fast Response", 50 * time.Millisecond, 200 * time.Millisecond, false},
		{"Timeout Case", 300 * time.Millisecond, 100 * time.Millisecond, true},
		{"Edge Case", 100 * time.Millisecond, 200 * time.Millisecond, false}, // Adjusted for reliability
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				time.Sleep(tc.serverDelay)
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("response"))
			}))
			defer server.Close()

			// Create client
			httpClient := client.NewHTTPClient(client.DefaultConfig())
			httpClient.DisableRetries()
			httpClient.ResetHTTPMethodStats()

			// Make request
			req := &client.Request{
				Method:  "GET",
				URL:     server.URL,
				Timeout: tc.requestTimeout,
			}

			resp, err := httpClient.Execute(context.Background(), req)

			// Check metrics
			stats := httpClient.GetHTTPMethodStats()

			if tc.expectTimeout {
				if err == nil {
					t.Error("Expected timeout error, got nil")
				} else if !strings.Contains(err.Error(), "timeout") && !strings.Contains(err.Error(), "deadline exceeded") {
					t.Errorf("Expected timeout error, got: %v", err)
				}

				// Give time for metrics recording
				time.Sleep(5 * time.Millisecond)
				stats = httpClient.GetHTTPMethodStats()

				if stats.TimeoutErrors == 0 {
					t.Error("Expected timeout to be recorded in metrics")
				}
			} else {
				if err != nil && (strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "deadline exceeded")) {
					t.Errorf("Unexpected timeout error: %v", err)
				}
				if resp != nil && resp.StatusCode != 200 {
					t.Errorf("Expected status 200, got %d", resp.StatusCode)
				}
			}
		})
	}
}

// TestTimeoutRecoveryScenarios tests timeout recovery and resilience
func TestTimeoutRecoveryScenarios(t *testing.T) {
	responseDelay := 200 * time.Millisecond

	// Create server that can switch between fast and slow responses
	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		if requestCount <= 2 {
			// First two requests timeout
			time.Sleep(responseDelay)
		}
		// Subsequent requests are fast
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(fmt.Sprintf("response %d", requestCount)))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.DisableRetries()
	httpClient.ResetHTTPMethodStats()

	// Make multiple requests
	for i := 0; i < 4; i++ {
		req := &client.Request{
			Method:  "GET",
			URL:     server.URL,
			Timeout: 100 * time.Millisecond,
		}

		resp, err := httpClient.Execute(context.Background(), req)

		if i < 2 {
			// First two should timeout
			if err == nil {
				t.Errorf("Request %d: Expected timeout, got success", i+1)
			}
		} else {
			// Last two should succeed
			if err != nil {
				t.Errorf("Request %d: Expected success, got error: %v", i+1, err)
			}
			if resp != nil && resp.StatusCode != 200 {
				t.Errorf("Request %d: Expected status 200, got %d", i+1, resp.StatusCode)
			}
		}
	}

	// Check final metrics
	time.Sleep(10 * time.Millisecond)
	stats := httpClient.GetHTTPMethodStats()

	if stats.GetRequests != 4 {
		t.Errorf("Expected 4 total requests, got %d", stats.GetRequests)
	}

	// Should have some timeouts recorded
	if stats.TimeoutErrors == 0 {
		t.Error("Expected some timeout errors to be recorded")
	}
}

// TestTimeoutDistributionTracking tests timeout value distribution tracking
func TestTimeoutDistributionTracking(t *testing.T) {
	cfg := client.DefaultConfig()

	// Enable timeout monitoring with distribution tracking
	cfg.TimeoutMonitoring.Enabled = true
	cfg.TimeoutMonitoring.DistributionBuckets = []time.Duration{
		50 * time.Millisecond,
		100 * time.Millisecond,
		200 * time.Millisecond,
		500 * time.Millisecond,
	}

	httpClient := client.NewHTTPClient(cfg)
	httpClient.DisableRetries()

	// Test that distribution tracking is enabled
	if httpClient == nil {
		t.Error("HTTP client should not be nil with distribution tracking enabled")
	}

	// Verify timeout monitoring configuration
	monitoring := httpClient.GetTimeoutMonitoringReport()
	if monitoring == nil {
		t.Error("Expected timeout monitoring report to be available")
	}
}

// TestTimeoutEdgeCases tests various edge cases
func TestTimeoutEdgeCases(t *testing.T) {
	t.Run("Zero Timeout", func(t *testing.T) {
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("fast response"))
		}))
		defer server.Close()

		httpClient := client.NewHTTPClient(client.DefaultConfig())
		httpClient.DisableRetries()

		req := &client.Request{
			Method:  "GET",
			URL:     server.URL,
			Timeout: 0, // Zero timeout should use client defaults
		}

		resp, err := httpClient.Execute(context.Background(), req)
		if err != nil {
			t.Errorf("Unexpected error with zero timeout: %v", err)
		}
		if resp == nil || resp.StatusCode != 200 {
			t.Error("Expected successful response with zero timeout")
		}
	})

	t.Run("Very Large Timeout", func(t *testing.T) {
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("response"))
		}))
		defer server.Close()

		httpClient := client.NewHTTPClient(client.DefaultConfig())
		httpClient.DisableRetries()

		req := &client.Request{
			Method:  "GET",
			URL:     server.URL,
			Timeout: time.Hour, // Very large timeout
		}

		resp, err := httpClient.Execute(context.Background(), req)
		if err != nil {
			t.Errorf("Unexpected error with large timeout: %v", err)
		}
		if resp == nil || resp.StatusCode != 200 {
			t.Error("Expected successful response with large timeout")
		}
	})

	t.Run("Negative Timeout", func(t *testing.T) {
		httpClient := client.NewHTTPClient(client.DefaultConfig())

		req := &client.Request{
			Method:  "GET",
			URL:     "http://example.com",
			Timeout: -1 * time.Second, // Negative timeout
		}

		_, err := httpClient.Execute(context.Background(), req)
		if err == nil {
			t.Error("Expected validation error for negative timeout")
		}
		if !strings.Contains(err.Error(), "request timeout must be non-negative") {
			t.Errorf("Expected validation error message, got: %v", err)
		}
	})
}

// TestTimeoutWithRetryInteraction tests timeout behavior with retry logic
func TestTimeoutWithRetryInteraction(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(150 * time.Millisecond) // Always timeout
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// Create client with retries enabled
	retryConfig := client.DefaultRetryConfig()
	retryConfig.MaxRetries = 2
	retryConfig.InitialDelay = 50 * time.Millisecond

	cfg := client.DefaultConfig()
	httpClient := client.NewHTTPClientWithRetry(cfg, retryConfig)
	httpClient.ResetHTTPMethodStats()

	req := &client.Request{
		Method:  "GET",
		URL:     server.URL,
		Timeout: 100 * time.Millisecond,
	}

	_, err := httpClient.Execute(context.Background(), req)

	// Should timeout even with retries
	if err == nil {
		t.Error("Expected timeout error with retries enabled")
	}

	// Check that multiple attempts were made
	time.Sleep(10 * time.Millisecond)
	stats := httpClient.GetHTTPMethodStats()

	// Should have at least one request
	if stats.GetRequests < 1 {
		t.Errorf("Expected at least 1 request, got %d", stats.GetRequests)
	}

	// Should have timeout error recorded
	if stats.TimeoutErrors == 0 {
		t.Error("Expected timeout errors to be recorded")
	}
}

// TestConcurrentTimeoutHandling tests timeout handling under concurrent load
func TestConcurrentTimeoutHandling(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(200 * time.Millisecond) // Always timeout
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.DisableRetries()
	httpClient.ResetHTTPMethodStats()

	// Make concurrent requests
	const numRequests = 10
	results := make(chan error, numRequests)

	for i := 0; i < numRequests; i++ {
		go func() {
			req := &client.Request{
				Method:  "GET",
				URL:     server.URL,
				Timeout: 100 * time.Millisecond,
			}

			_, err := httpClient.Execute(context.Background(), req)
			results <- err
		}()
	}

	// Collect results
	timeoutCount := 0
	for i := 0; i < numRequests; i++ {
		err := <-results
		if err != nil && (strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "deadline exceeded")) {
			timeoutCount++
		}
	}

	// Should have multiple timeouts
	if timeoutCount == 0 {
		t.Error("Expected some timeout errors in concurrent requests")
	}

	// Check metrics
	time.Sleep(50 * time.Millisecond)
	stats := httpClient.GetHTTPMethodStats()

	if stats.GetRequests != numRequests {
		t.Errorf("Expected %d total requests, got %d", numRequests, stats.GetRequests)
	}

	// Should have timeout errors recorded
	if stats.TimeoutErrors == 0 {
		t.Error("Expected timeout errors to be recorded in concurrent test")
	}
}

// BenchmarkTimeoutOverhead benchmarks the performance overhead of timeout handling
func BenchmarkTimeoutOverhead(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("fast response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.DisableRetries()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := &client.Request{
			Method:  "GET",
			URL:     server.URL,
			Timeout: 1 * time.Second,
		}

		resp, err := httpClient.Execute(context.Background(), req)
		if err != nil {
			b.Errorf("Unexpected error: %v", err)
		}
		if resp == nil || resp.StatusCode != 200 {
			b.Error("Expected successful response")
		}
	}
}

// TestTimeoutBreakdownAnalysis tests detailed timeout type analysis
func TestTimeoutBreakdownAnalysis(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.ResetHTTPMethodStats()

	// Get initial timeout breakdown summary
	summary := httpClient.GetTimeoutBreakdownSummary()
	if summary == nil {
		t.Error("Expected timeout breakdown summary to be available")
	}

	// Check that summary contains expected fields
	if _, exists := summary["breakdown"]; !exists {
		t.Error("Expected 'breakdown' field in summary")
	}

	// Check the breakdown structure
	breakdown, ok := summary["breakdown"].(map[string]int64)
	if !ok {
		t.Error("Expected breakdown to be map[string]int64")
	}

	// Check that breakdown contains expected timeout types
	expectedKeys := []string{"total", "dial", "tls", "response_header", "total_request"}
	for _, key := range expectedKeys {
		if _, exists := breakdown[key]; !exists {
			t.Errorf("Expected '%s' key in breakdown", key)
		}
	}
}

// TestDynamicTimeoutAdjustment tests dynamic timeout adjustment functionality
func TestDynamicTimeoutAdjustment(t *testing.T) {
	// Create config with dynamic timeout adjustment enabled
	cfg := client.DefaultConfig()
	cfg.TimeoutStrategy = client.NewAdaptiveTimeoutStrategy(client.TimeoutStrategyBalanced)

	httpClient := client.NewHTTPClient(cfg)
	httpClient.DisableRetries()

	// Get dynamic timeout stats
	dynamicStats := httpClient.GetDynamicTimeoutStats()
	if dynamicStats == nil {
		t.Error("Expected dynamic timeout stats to be available")
	}

	// Verify dynamic adjustment is working
	if _, exists := dynamicStats["current_multiplier"]; !exists {
		t.Error("Expected current_multiplier in dynamic stats")
	}
}
