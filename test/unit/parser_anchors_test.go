package unit

import (
	"testing"

	"neuralmetergo/internal/parser"
)

func TestParser_YAMLAnchorsAndAliases(t *testing.T) {
	p := parser.NewParser()

	// Test parsing the anchors test plan
	plan, err := p.ParseFile("../fixtures/anchors_test_plan.yaml")
	if err != nil {
		t.Fatalf("Expected no error parsing anchors test plan, got %v", err)
	}

	// Verify basic plan properties
	if plan.Name != "Anchors and Aliases Test Plan" {
		t.<PERSON><PERSON><PERSON>("Expected name 'Anchors and Aliases Test Plan', got '%s'", plan.Name)
	}

	if plan.Concurrency != 20 {
		t.<PERSON><PERSON><PERSON>("Expected concurrency 20, got %d", plan.Concurrency)
	}

	// Verify global headers were merged from anchor
	expectedHeaders := map[string]string{
		"User-Agent":   "NeuralMeter/1.0",
		"Accept":       "application/json",
		"Content-Type": "application/json",
		"X-Test-Suite": "anchors-test",
	}

	for key, expectedValue := range expectedHeaders {
		if actualValue, exists := plan.Global.Headers[key]; !exists {
			t.<PERSON>("Expected global header '%s' not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected global header '%s' = '%s', got '%s'", key, expectedValue, actualValue)
		}
	}

	// Verify scenarios
	if len(plan.Scenarios) != 2 {
		t.Fatalf("Expected 2 scenarios, got %d", len(plan.Scenarios))
	}

	// Test first scenario with anchors
	authScenario := plan.Scenarios[0]
	if authScenario.Name != "Authentication with Anchors" {
		t.Errorf("Expected first scenario name 'Authentication with Anchors', got '%s'", authScenario.Name)
	}

	if authScenario.Weight != 70 {
		t.Errorf("Expected first scenario weight 70, got %d", authScenario.Weight)
	}

	// Test request with merged anchor config
	if len(authScenario.Requests) != 1 {
		t.Fatalf("Expected 1 request in auth scenario, got %d", len(authScenario.Requests))
	}

	loginRequest := authScenario.Requests[0]
	if loginRequest.Method != "POST" {
		t.Errorf("Expected login request method 'POST', got '%s'", loginRequest.Method)
	}

	// Verify headers were merged from anchor
	for key, expectedValue := range map[string]string{
		"User-Agent":   "NeuralMeter/1.0",
		"Accept":       "application/json",
		"Content-Type": "application/json",
	} {
		if actualValue, exists := loginRequest.Headers[key]; !exists {
			t.Errorf("Expected login request header '%s' not found", key)
		} else if actualValue != expectedValue {
			t.Errorf("Expected login request header '%s' = '%s', got '%s'", key, expectedValue, actualValue)
		}
	}

	// Verify timeout from auth_config anchor
	if loginRequest.Timeout.String() != "10s" {
		t.Errorf("Expected login request timeout '10s', got '%s'", loginRequest.Timeout.String())
	}

	// Verify assertions from auth_config anchor
	if len(loginRequest.Assertions) != 2 {
		t.Fatalf("Expected 2 assertions in login request, got %d", len(loginRequest.Assertions))
	}

	statusAssertion := loginRequest.Assertions[0]
	if statusAssertion.Type != "status_code" || statusAssertion.Operator != "eq" {
		t.Errorf("Expected status_code assertion with eq operator")
	}

	responseTimeAssertion := loginRequest.Assertions[1]
	if responseTimeAssertion.Type != "response_time" || responseTimeAssertion.Operator != "lt" {
		t.Errorf("Expected response_time assertion with lt operator")
	}

	// Test second scenario with different anchor config
	apiScenario := plan.Scenarios[1]
	if apiScenario.Name != "API Calls with Anchors" {
		t.Errorf("Expected second scenario name 'API Calls with Anchors', got '%s'", apiScenario.Name)
	}

	if len(apiScenario.Requests) != 2 {
		t.Fatalf("Expected 2 requests in API scenario, got %d", len(apiScenario.Requests))
	}

	// Test GET request with api_config anchor
	getRequest := apiScenario.Requests[0]
	if getRequest.Method != "GET" {
		t.Errorf("Expected GET request method 'GET', got '%s'", getRequest.Method)
	}

	// Verify timeout from api_config anchor (different from auth_config)
	if getRequest.Timeout.String() != "15s" {
		t.Errorf("Expected GET request timeout '15s', got '%s'", getRequest.Timeout.String())
	}

	// Verify assertions from api_config anchor
	if len(getRequest.Assertions) != 2 {
		t.Fatalf("Expected 2 assertions in GET request, got %d", len(getRequest.Assertions))
	}

	headerAssertion := getRequest.Assertions[1]
	if headerAssertion.Type != "header_exists" || headerAssertion.Field != "Content-Type" {
		t.Errorf("Expected header_exists assertion for Content-Type")
	}
}

func TestParser_YAMLAnchorsSimple(t *testing.T) {
	yamlContent := `
version: "1.0"
name: "Simple Anchors Test"
duration: "1m"
concurrency: 5

global:
  timeout: &timeout "5s"

scenarios:
  - name: "Test Scenario"
    requests:
      - name: "Test Request"
        method: "GET"
        url: "/test"
        timeout: *timeout
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
`

	p := parser.NewParser()
	plan, err := p.ParseBytes([]byte(yamlContent))
	if err != nil {
		t.Fatalf("Expected no error parsing simple anchors YAML, got %v", err)
	}

	// Verify timeout was properly resolved from anchor
	if plan.Global.Timeout.String() != "5s" {
		t.Errorf("Expected global timeout '5s', got '%s'", plan.Global.Timeout.String())
	}

	if len(plan.Scenarios) != 1 || len(plan.Scenarios[0].Requests) != 1 {
		t.Fatal("Expected 1 scenario with 1 request")
	}

	request := plan.Scenarios[0].Requests[0]
	if request.Timeout.String() != "5s" {
		t.Errorf("Expected request timeout '5s', got '%s'", request.Timeout.String())
	}
}

func TestParser_YAMLAnchorsInvalidReference(t *testing.T) {
	yamlContent := `
version: "1.0"
name: "Invalid Anchor Test"
duration: "1m"
concurrency: 1

scenarios:
  - name: "Test Scenario"
    requests:
      - name: "Test Request"
        method: "GET"
        url: "/test"
        timeout: *nonexistent_anchor
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
`

	p := parser.NewParser()
	_, err := p.ParseBytes([]byte(yamlContent))

	if err == nil {
		t.Fatal("Expected error for invalid anchor reference, got nil")
	}

	// The YAML library should catch this as an undefined alias
	if err.Error() == "" {
		t.Error("Expected non-empty error message for invalid anchor reference")
	}
}
