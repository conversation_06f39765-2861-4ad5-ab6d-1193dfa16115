package unit

import (
	"context"
	"log"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

// TestDebugTimeoutWithRetry debugs timeout behavior with retry logic
func TestDebugTimeoutWithRetry(t *testing.T) {
	log.Println("Starting TestDebugTimeoutWithRetry")

	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		log.Printf("Server received request #%d", requestCount)
		time.Sleep(150 * time.Millisecond) // Always timeout
		w.WriteHeader(http.StatusOK)
		log.Printf("Server response sent for request #%d", requestCount)
	}))
	defer server.Close()

	// Create client with retries enabled
	retryConfig := client.DefaultRetryConfig()
	retryConfig.MaxRetries = 2
	retryConfig.InitialDelay = 50 * time.Millisecond
	log.Printf("Retry config: MaxRetries=%d, InitialDelay=%v", retryConfig.MaxRetries, retryConfig.InitialDelay)

	cfg := client.DefaultConfig()
	httpClient := client.NewHTTPClientWithRetry(cfg, retryConfig)
	httpClient.ResetHTTPMethodStats()

	req := &client.Request{
		Method:  "GET",
		URL:     server.URL,
		Timeout: 100 * time.Millisecond,
	}

	log.Printf("Making request to %s with 100ms timeout (server delays 150ms)", server.URL)
	start := time.Now()
	resp, err := httpClient.Execute(context.Background(), req)
	elapsed := time.Since(start)
	log.Printf("Request completed in %v. Error: %v", elapsed, err)

	if resp != nil {
		log.Printf("Unexpected response received: %v", resp.Status)
	}

	// Check error details
	if err != nil {
		log.Printf("Error type: %T", err)
		log.Printf("Error message: %s", err.Error())
		log.Printf("Error contains 'timeout': %v", strings.Contains(err.Error(), "timeout"))
		log.Printf("Error contains 'deadline exceeded': %v", strings.Contains(err.Error(), "context deadline exceeded"))

		// Check if it's an HTTPError and examine its properties
		if httpErr, ok := err.(*client.HTTPError); ok {
			log.Printf("HTTPError Type: %d", httpErr.Type)
			log.Printf("HTTPError Retryable: %v", httpErr.Retryable)
			log.Printf("HTTPError StatusCode: %d", httpErr.StatusCode)
		}
	}

	// Check metrics immediately
	stats := httpClient.GetHTTPMethodStats()
	log.Printf("Immediate stats: GetRequests=%d, TimeoutErrors=%d, RetryAttempts=%d",
		stats.GetRequests, stats.TimeoutErrors, stats.RetryAttempts)

	// Wait a bit and check again
	time.Sleep(20 * time.Millisecond)
	stats = httpClient.GetHTTPMethodStats()
	log.Printf("Final stats: GetRequests=%d, TimeoutErrors=%d, RetryAttempts=%d, NetworkErrors=%d",
		stats.GetRequests, stats.TimeoutErrors, stats.RetryAttempts, stats.NetworkErrors)

	// Get breakdown for more detail
	breakdown := httpClient.GetTimeoutBreakdownSummary()
	log.Printf("Timeout breakdown: %+v", breakdown)

	log.Printf("Server request count: %d", requestCount)

	// Test assertions
	if err == nil {
		t.Error("Expected timeout error with retries enabled")
	}

	if stats.GetRequests < 1 {
		t.Errorf("Expected at least 1 request, got %d", stats.GetRequests)
	}

	// Don't fail on timeout errors for now, just log for debugging
	if stats.TimeoutErrors == 0 {
		t.Logf("Warning: Expected timeout errors to be recorded, got %d", stats.TimeoutErrors)
	} else {
		t.Logf("Success: Timeout errors recorded: %d", stats.TimeoutErrors)
	}

	log.Println("TestDebugTimeoutWithRetry completed")
}
