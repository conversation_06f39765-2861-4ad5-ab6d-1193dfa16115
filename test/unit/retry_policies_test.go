package unit

import (
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

// Test ErrorSpecificRetryPolicy basic functionality
func TestErrorSpecificRetryPolicy_ShouldRetry(t *testing.T) {
	config := client.DefaultRetryConfig()
	policy := client.NewErrorSpecificRetryPolicy(config)

	tests := []struct {
		name          string
		error         error
		statusCode    int
		attempt       int
		expectedRetry bool
		description   string
	}{
		{
			name:          "Network error should retry within limits",
			error:         &client.NetworkError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeNetwork, Retriable: true}},
			statusCode:    0,
			attempt:       2,
			expectedRetry: true,
			description:   "Network errors should be retried aggressively up to 5 attempts",
		},
		{
			name:          "Network error should not retry beyond limits",
			error:         &client.NetworkError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeNetwork, Retriable: true}},
			statusCode:    0,
			attempt:       6,
			expectedRetry: false,
			description:   "Network errors should not retry beyond 5 attempts",
		},
		{
			name:          "TLS error should have limited retries",
			error:         &client.TLSError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeTLS, Retriable: true}},
			statusCode:    0,
			attempt:       2,
			expectedRetry: false,
			description:   "TLS errors should only retry once (max 1 retry)",
		},
		{
			name:          "DNS error should have moderate retries",
			error:         &client.DNSError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeDNS, Retriable: true}},
			statusCode:    0,
			attempt:       1,
			expectedRetry: true,
			description:   "DNS errors should allow up to 2 retries",
		},
		{
			name:          "DNS error should not exceed limit",
			error:         &client.DNSError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeDNS, Retriable: true}},
			statusCode:    0,
			attempt:       3,
			expectedRetry: false,
			description:   "DNS errors should not retry beyond 2 attempts",
		},
		{
			name:          "Rate limit error should allow many retries",
			error:         &client.StatusCodeError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeStatusCode, Retriable: true}},
			statusCode:    429,
			attempt:       4,
			expectedRetry: true,
			description:   "Rate limit errors (429) should allow up to 6 retries",
		},
		{
			name:          "Server error should allow moderate retries",
			error:         &client.StatusCodeError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeStatusCode, Retriable: true}},
			statusCode:    503,
			attempt:       3,
			expectedRetry: true,
			description:   "Server errors (5xx) should allow up to 4 retries",
		},
		{
			name:          "Non-retryable error should not retry",
			error:         &client.BaseHTTPError{Type: client.EnhancedErrorTypeConfiguration, Retriable: false},
			statusCode:    0,
			attempt:       1,
			expectedRetry: false,
			description:   "Non-retryable errors should not be retried",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := &client.RetryContext{
				Attempt:        tt.attempt,
				LastError:      tt.error,
				LastStatusCode: tt.statusCode,
			}

			result := policy.ShouldRetry(ctx)
			if result != tt.expectedRetry {
				t.Errorf("ShouldRetry() = %v, want %v. %s", result, tt.expectedRetry, tt.description)
			}
		})
	}
}

func TestErrorSpecificRetryPolicy_CalculateDelay(t *testing.T) {
	config := client.DefaultRetryConfig()
	policy := client.NewErrorSpecificRetryPolicy(config)

	tests := []struct {
		name          string
		error         error
		statusCode    int
		attempt       int
		expectMinimum time.Duration
		expectMaximum time.Duration
		description   string
	}{
		{
			name:          "Network error should have exponential delay for attempt 1",
			error:         &client.NetworkError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeNetwork}},
			statusCode:    0,
			attempt:       1,
			expectMinimum: 850 * time.Millisecond,  // 500ms * 2.0 = 1000ms, with 15% jitter reduction
			expectMaximum: 1150 * time.Millisecond, // 500ms * 2.0 = 1000ms, with 15% jitter increase
			description:   "Network errors should use exponential backoff: 500ms * 2^1 = 1000ms",
		},
		{
			name:          "DNS error should have exponential delay for attempt 1",
			error:         &client.DNSError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeDNS}},
			statusCode:    0,
			attempt:       1,
			expectMinimum: 5100 * time.Millisecond, // 2s * 3.0 = 6s, with 15% jitter reduction
			expectMaximum: 6900 * time.Millisecond, // 2s * 3.0 = 6s, with 15% jitter increase
			description:   "DNS errors should use exponential backoff: 2s * 3^1 = 6s",
		},
		{
			name:          "Rate limit should have exponential delay for attempt 1",
			error:         &client.StatusCodeError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeStatusCode}},
			statusCode:    429,
			attempt:       1,
			expectMinimum: 6375 * time.Millisecond, // 5s * 1.5 = 7.5s, with 15% jitter reduction
			expectMaximum: 8625 * time.Millisecond, // 5s * 1.5 = 7.5s, with 15% jitter increase
			description:   "Rate limit errors should use exponential backoff: 5s * 1.5^1 = 7.5s",
		},
		{
			name:          "Exponential backoff should increase delay significantly",
			error:         &client.NetworkError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeNetwork}},
			statusCode:    0,
			attempt:       3,
			expectMinimum: 3400 * time.Millisecond, // 500ms * 2^3 = 4s, with 15% jitter reduction
			expectMaximum: 4600 * time.Millisecond, // 500ms * 2^3 = 4s, with 15% jitter increase
			description:   "Multiple attempts should result in exponential backoff: 500ms * 2^3 = 4s",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := &client.RetryContext{
				Attempt:        tt.attempt,
				LastError:      tt.error,
				LastStatusCode: tt.statusCode,
			}

			delay := policy.CalculateDelay(ctx)
			if delay < tt.expectMinimum || delay > tt.expectMaximum {
				t.Errorf("CalculateDelay() = %v, want between %v and %v. %s",
					delay, tt.expectMinimum, tt.expectMaximum, tt.description)
			}
		})
	}
}

func TestErrorSpecificRetryPolicy_RespectRetryAfterHeader(t *testing.T) {
	config := client.DefaultRetryConfig()
	policy := client.NewErrorSpecificRetryPolicy(config)

	// Create an error that should respect Retry-After headers (rate limit)
	retryAfterDelay := 10 * time.Second
	rateLimitError := &client.StatusCodeError{
		BaseHTTPError: &client.BaseHTTPError{
			Type:       client.EnhancedErrorTypeStatusCode,
			Retriable:  true,
			RetryDelay: retryAfterDelay,
		},
	}

	ctx := &client.RetryContext{
		Attempt:        1,
		LastError:      rateLimitError,
		LastStatusCode: 429,
	}

	delay := policy.CalculateDelay(ctx)

	// For rate limiting, it should respect the retry delay from the error
	// Allow some jitter variance
	expectedMin := 9 * time.Second  // 10s with 10% jitter reduction
	expectedMax := 11 * time.Second // 10s with 10% jitter increase

	if delay < expectedMin || delay > expectedMax {
		t.Errorf("Rate limit error should respect Retry-After header. Got %v, expected between %v and %v",
			delay, expectedMin, expectedMax)
	}
}

func TestAdaptiveRetryPolicy_Learning(t *testing.T) {
	config := client.DefaultRetryConfig()
	policy := client.NewAdaptiveRetryPolicy(config)

	// Create a network error
	networkError := &client.NetworkError{
		BaseHTTPError: &client.BaseHTTPError{
			Type:      client.EnhancedErrorTypeNetwork,
			Retriable: true,
		},
	}

	ctx := &client.RetryContext{
		Attempt:        2,
		LastError:      networkError,
		LastStatusCode: 0,
	}

	// Test with no learning data (should use base policy)
	initialShouldRetry := policy.ShouldRetry(ctx)
	if !initialShouldRetry {
		t.Error("AdaptiveRetryPolicy should retry network errors initially")
	}

	// Test that adaptive policy calculates reasonable delays
	delay := policy.CalculateDelay(ctx)
	if delay <= 0 {
		t.Error("AdaptiveRetryPolicy should calculate positive delay")
	}

	// Test policy name
	if policy.Name() != "AdaptiveRetryPolicy" {
		t.Errorf("Expected name 'AdaptiveRetryPolicy', got '%s'", policy.Name())
	}
}

func TestCircuitBreakerAwarePolicy_Integration(t *testing.T) {
	config := client.DefaultRetryConfig()
	basePolicy := client.NewErrorSpecificRetryPolicy(config)
	circuitBreaker := client.NewCircuitBreaker()

	policy := client.NewCircuitBreakerAwarePolicy(basePolicy, circuitBreaker)

	networkError := &client.NetworkError{
		BaseHTTPError: &client.BaseHTTPError{
			Type:      client.EnhancedErrorTypeNetwork,
			Retriable: true,
		},
	}

	ctx := &client.RetryContext{
		Attempt:        1,
		LastError:      networkError,
		LastStatusCode: 0,
	}

	// Should retry when circuit is closed
	if !policy.ShouldRetry(ctx) {
		t.Error("CircuitBreakerAwarePolicy should retry when circuit is closed")
	}

	// Simulate circuit breaker opening by recording failures
	for i := 0; i < 10; i++ {
		circuitBreaker.RecordFailure()
	}

	// Should not retry when circuit is open
	if policy.ShouldRetry(ctx) {
		t.Error("CircuitBreakerAwarePolicy should not retry when circuit is open")
	}
}

func TestRetryPolicyFactory_CreatePolicies(t *testing.T) {
	factory := &client.RetryPolicyFactory{}
	config := client.DefaultRetryConfig()

	tests := []struct {
		name         string
		createPolicy func() client.RetryPolicy
		expectedName string
	}{
		{
			name:         "StandardPolicy",
			createPolicy: func() client.RetryPolicy { return factory.CreateStandardPolicy(config) },
			expectedName: "standard",
		},
		{
			name:         "ErrorSpecificPolicy",
			createPolicy: func() client.RetryPolicy { return factory.CreateErrorSpecificPolicy(config) },
			expectedName: "ErrorSpecificRetryPolicy",
		},
		{
			name:         "AdaptivePolicy",
			createPolicy: func() client.RetryPolicy { return factory.CreateAdaptivePolicy(config) },
			expectedName: "AdaptiveRetryPolicy",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			policy := tt.createPolicy()
			if policy == nil {
				t.Errorf("Factory should create non-nil policy")
				return
			}

			if policy.Name() != tt.expectedName {
				t.Errorf("Policy name = %v, want %v", policy.Name(), tt.expectedName)
			}
		})
	}
}

func TestCreateConfiguredPolicy_CustomConfiguration(t *testing.T) {
	config := client.DefaultRetryConfig()
	policyConfig := client.NewPolicyConfiguration()

	// Enable all features
	policyConfig.EnableAdaptiveLearning = true
	policyConfig.EnableCircuitBreaker = true
	policyConfig.GlobalTimeout = 2 * time.Minute

	policy := client.CreateConfiguredPolicy(config, policyConfig)
	if policy == nil {
		t.Fatal("CreateConfiguredPolicy should return non-nil policy")
	}

	// Should have both adaptive and circuit breaker in the name
	name := policy.Name()
	if name == "" {
		t.Error("Policy should have a name")
	}

	// Test basic functionality
	networkError := &client.NetworkError{
		BaseHTTPError: &client.BaseHTTPError{
			Type:      client.EnhancedErrorTypeNetwork,
			Retriable: true,
		},
	}

	ctx := &client.RetryContext{
		Attempt:        1,
		LastError:      networkError,
		LastStatusCode: 0,
	}

	if !policy.ShouldRetry(ctx) {
		t.Error("Configured policy should retry network errors")
	}

	delay := policy.CalculateDelay(ctx)
	if delay <= 0 {
		t.Error("Configured policy should calculate positive delay")
	}
}

func TestErrorSpecificRetryPolicy_PolicySelection(t *testing.T) {
	config := client.DefaultRetryConfig()
	policy := client.NewErrorSpecificRetryPolicy(config).(*client.ErrorSpecificRetryPolicy)

	tests := []struct {
		name           string
		error          error
		statusCode     int
		expectedPolicy string
		maxRetries     int
	}{
		{
			name:           "Network error selects network policy",
			error:          &client.NetworkError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeNetwork, Retriable: true}},
			statusCode:     0,
			expectedPolicy: "network",
			maxRetries:     5,
		},
		{
			name:           "DNS error selects DNS policy",
			error:          &client.DNSError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeDNS, Retriable: true}},
			statusCode:     0,
			expectedPolicy: "dns",
			maxRetries:     2,
		},
		{
			name:           "429 status selects rate limit policy",
			error:          &client.StatusCodeError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeStatusCode, Retriable: true}},
			statusCode:     429,
			expectedPolicy: "rate_limit",
			maxRetries:     6,
		},
		{
			name:           "503 status selects server error policy",
			error:          &client.StatusCodeError{BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeStatusCode, Retriable: true}},
			statusCode:     503,
			expectedPolicy: "server",
			maxRetries:     4,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that the policy correctly limits retries based on error type
			// Attempt numbers are 0-based, MaxRetries is the limit (exclusive)
			// So if MaxRetries=5, valid attempts are 0,1,2,3,4 and attempt 5 should be rejected

			ctx := &client.RetryContext{
				Attempt:        tt.maxRetries - 1, // Last valid attempt
				LastError:      tt.error,
				LastStatusCode: tt.statusCode,
			}

			// Should still retry at the last valid attempt (maxRetries - 1)
			shouldRetry := policy.ShouldRetry(ctx)
			t.Logf("Attempt %d for %s (max: %d): ShouldRetry=%v", tt.maxRetries-1, tt.expectedPolicy, tt.maxRetries, shouldRetry)
			if !shouldRetry {
				t.Errorf("Should retry at attempt %d for %s (max: %d)", tt.maxRetries-1, tt.expectedPolicy, tt.maxRetries)
			}

			// Should not retry at max attempts
			ctx.Attempt = tt.maxRetries
			shouldNotRetry := policy.ShouldRetry(ctx)
			if shouldNotRetry {
				t.Errorf("Should not retry at attempt %d for %s (max: %d)", tt.maxRetries, tt.expectedPolicy, tt.maxRetries)
			}
		})
	}
}

func TestErrorSpecificRetryPolicy_JitterBehavior(t *testing.T) {
	config := client.DefaultRetryConfig()
	policy := client.NewErrorSpecificRetryPolicy(config)

	networkError := &client.NetworkError{
		BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeNetwork},
	}

	ctx := &client.RetryContext{
		Attempt:        1,
		LastError:      networkError,
		LastStatusCode: 0,
	}

	// Calculate delay multiple times to verify jitter is applied
	delays := make([]time.Duration, 10)
	for i := 0; i < 10; i++ {
		delays[i] = policy.CalculateDelay(ctx)
	}

	// Check that at least some delays are different (indicating jitter)
	allSame := true
	for i := 1; i < len(delays); i++ {
		if delays[i] != delays[0] {
			allSame = false
			break
		}
	}

	if allSame {
		t.Error("Jitter should cause delay variations, but all delays are identical")
	}

	// Verify all delays are within reasonable bounds for exponential backoff
	// Network error with attempt 1: 500ms * 2^1 = 1000ms, with 15% jitter
	expectedDelay := 1000 * time.Millisecond
	minExpected := time.Duration(float64(expectedDelay) * 0.85) // Allow for 15% jitter reduction
	maxExpected := time.Duration(float64(expectedDelay) * 1.15) // Allow for 15% jitter increase

	for i, delay := range delays {
		if delay < minExpected || delay > maxExpected {
			t.Errorf("Delay %d (%v) is outside expected range [%v, %v]", i, delay, minExpected, maxExpected)
		}
	}
}

// Benchmark to ensure performance is acceptable
func BenchmarkErrorSpecificRetryPolicy_ShouldRetry(b *testing.B) {
	config := client.DefaultRetryConfig()
	policy := client.NewErrorSpecificRetryPolicy(config)

	networkError := &client.NetworkError{
		BaseHTTPError: &client.BaseHTTPError{
			Type:      client.EnhancedErrorTypeNetwork,
			Retriable: true,
		},
	}

	ctx := &client.RetryContext{
		Attempt:        2,
		LastError:      networkError,
		LastStatusCode: 0,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		policy.ShouldRetry(ctx)
	}
}

func BenchmarkErrorSpecificRetryPolicy_CalculateDelay(b *testing.B) {
	config := client.DefaultRetryConfig()
	policy := client.NewErrorSpecificRetryPolicy(config)

	networkError := &client.NetworkError{
		BaseHTTPError: &client.BaseHTTPError{Type: client.EnhancedErrorTypeNetwork},
	}

	ctx := &client.RetryContext{
		Attempt:        2,
		LastError:      networkError,
		LastStatusCode: 0,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		policy.CalculateDelay(ctx)
	}
}
