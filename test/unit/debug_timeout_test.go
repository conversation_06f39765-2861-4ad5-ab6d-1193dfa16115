package unit

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

// TestDebugTimeoutErrorTypes helps understand what error types are produced in timeout scenarios
func TestDebugTimeoutErrorTypes(t *testing.T) {
	// Create test server with delay
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(300 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("delayed response"))
	}))
	defer server.Close()

	// Test 1: HTTP client timeout using context
	t.Run("Context Timeout", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
		defer cancel()

		req, _ := http.NewRequestWithContext(ctx, "GET", server.URL, nil)
		resp, err := http.DefaultClient.Do(req)
		if resp != nil {
			resp.Body.Close()
		}

		fmt.Printf("Context timeout error: %v\n", err)
		fmt.Printf("Error type: %T\n", err)

		if err != nil {
			// Check if it's a net.Error
			if netErr, ok := err.(net.Error); ok {
				fmt.Printf("Is net.Error: true, Timeout(): %v, Temporary(): %v\n", netErr.Timeout(), netErr.Temporary())
			} else {
				fmt.Printf("Is net.Error: false\n")
			}
		}
	})

	// Test 2: Our client with timeout
	t.Run("Our Client Timeout", func(t *testing.T) {
		httpClient := client.NewHTTPClient(client.DefaultConfig())
		httpClient.DisableRetries()
		httpClient.ResetHTTPMethodStats()

		req := &client.Request{
			Method:  "GET",
			URL:     server.URL,
			Timeout: 100 * time.Millisecond,
		}

		resp, err := httpClient.Execute(context.Background(), req)
		_ = resp

		fmt.Printf("Our client timeout error: %v\n", err)
		fmt.Printf("Error type: %T\n", err)

		if err != nil {
			// Check the error string patterns
			errStr := strings.ToLower(err.Error())
			fmt.Printf("Contains 'timeout': %v\n", strings.Contains(errStr, "timeout"))
			fmt.Printf("Contains 'deadline exceeded': %v\n", strings.Contains(errStr, "context deadline exceeded"))

			// Check if it's our HTTPError type
			if httpErr, ok := err.(*client.HTTPError); ok {
				fmt.Printf("Is HTTPError: true, Type: %v, Retryable: %v\n", httpErr.Type, httpErr.Retryable)
				if httpErr.Underlying != nil {
					fmt.Printf("Underlying error: %v, type: %T\n", httpErr.Underlying, httpErr.Underlying)

					// Check if underlying error is net.Error
					if netErr, ok := httpErr.Underlying.(net.Error); ok {
						fmt.Printf("Underlying is net.Error: true, Timeout(): %v\n", netErr.Timeout())
					} else {
						fmt.Printf("Underlying is net.Error: false\n")
					}
				}
			} else {
				fmt.Printf("Is HTTPError: false\n")
			}
		}

		// Check what our metrics recorded
		stats := httpClient.GetHTTPMethodStats()
		fmt.Printf("Timeout errors recorded: %d\n", stats.TimeoutErrors)
		fmt.Printf("Network errors recorded: %d\n", stats.NetworkErrors)
		fmt.Printf("Total requests: %d\n", stats.GetRequests)
	})
}
