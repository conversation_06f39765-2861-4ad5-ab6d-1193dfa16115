package unit

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"neuralmetergo/internal/parser"
)

func TestParser_ParseWithIncludes(t *testing.T) {
	p := parser.NewParser()

	// Test parsing the base plan with includes
	plan, err := p.<PERSON>rse<PERSON>("../fixtures/base_plan.yaml")
	if err != nil {
		t.Fatalf("Expected no error parsing base plan with includes, got %v", err)
	}

	// Verify base plan properties
	if plan.Name != "Base Test Plan with Includes" {
		t.Errorf("Expected name 'Base Test Plan with Includes', got '%s'", plan.Name)
	}

	// Verify scenarios were merged from includes
	expectedScenarios := 3 // 1 base + 1 auth + 1 api
	if len(plan.Scenarios) != expectedScenarios {
		t.<PERSON>rf("Expected %d scenarios after includes, got %d", expectedScenarios, len(plan.Scenarios))
	}

	// Verify specific scenarios exist
	scenarioNames := make(map[string]bool)
	for _, scenario := range plan.Scenarios {
		scenarioNames[scenario.Name] = true
	}

	expectedNames := []string{"Base Scenario", "User Login", "API Data Retrieval"}
	for _, name := range expectedNames {
		if !scenarioNames[name] {
			t.Errorf("Expected scenario '%s' not found", name)
		}
	}

	// Verify variables were merged
	expectedVariables := 3 // 1 base + 1 auth + 1 api
	if len(plan.Variables) != expectedVariables {
		t.Errorf("Expected %d variables after includes, got %d", expectedVariables, len(plan.Variables))
	}

	// Verify global headers were merged
	if plan.Global.Headers["User-Agent"] != "NeuralMeter/1.0" {
		t.Errorf("Expected base global header preserved")
	}
	if plan.Global.Headers["X-Auth-Source"] != "test-suite" {
		t.Errorf("Expected auth global header merged")
	}

	// Verify global variables were merged
	if plan.Global.Variables["api_version"] != "v1" {
		t.Errorf("Expected base global variable preserved")
	}
	if plan.Global.Variables["api_retries"] != "3" {
		t.Errorf("Expected api global variable merged")
	}
}

func TestParser_ParseWithContext(t *testing.T) {
	basePath := filepath.Join("../fixtures")
	p := parser.NewParserWithContext(basePath)

	plan, err := p.ParseFile(filepath.Join(basePath, "base_plan.yaml"))
	if err != nil {
		t.Fatalf("Expected no error with context parser, got %v", err)
	}

	if plan.Name != "Base Test Plan with Includes" {
		t.Errorf("Expected correct plan name with context parser")
	}
}

func TestParser_CircularIncludeDetection(t *testing.T) {
	// Create temporary files for circular include test
	tmpDir := t.TempDir()

	// File A includes file B
	fileA := filepath.Join(tmpDir, "a.yaml")
	fileAContent := `
version: "1.0"
name: "File A"
duration: "1m"
concurrency: 1
includes:
  - "b.yaml"
scenarios:
  - name: "Scenario A"
    requests:
      - method: "GET"
        url: "/a"
`

	// File B includes file A (circular)
	fileB := filepath.Join(tmpDir, "b.yaml")
	fileBContent := `
includes:
  - "a.yaml"
scenarios:
  - name: "Scenario B"
    requests:
      - method: "GET"
        url: "/b"
`

	if err := os.WriteFile(fileA, []byte(fileAContent), 0644); err != nil {
		t.Fatalf("Failed to create test file A: %v", err)
	}
	if err := os.WriteFile(fileB, []byte(fileBContent), 0644); err != nil {
		t.Fatalf("Failed to create test file B: %v", err)
	}

	p := parser.NewParser()
	_, err := p.ParseFile(fileA)

	if err == nil {
		t.Fatal("Expected error for circular include, got nil")
	}

	if !strings.Contains(err.Error(), "circular include detected") {
		t.Errorf("Expected 'circular include detected' error, got %v", err)
	}
}

func TestParser_MaxDepthExceeded(t *testing.T) {
	// Create a deep include chain that exceeds max depth
	tmpDir := t.TempDir()

	// Create a chain of 12 files (exceeds default max depth of 10)
	for i := 0; i < 12; i++ {
		filename := filepath.Join(tmpDir, fmt.Sprintf("file%d.yaml", i))
		var content string

		if i == 0 {
			// First file
			content = fmt.Sprintf(`
version: "1.0"
name: "File %d"
duration: "1m"
concurrency: 1
includes:
  - "file%d.yaml"
scenarios:
  - name: "Scenario %d"
    requests:
      - method: "GET"
        url: "/test%d"
`, i, i+1, i, i)
		} else if i == 11 {
			// Last file (no includes)
			content = fmt.Sprintf(`
scenarios:
  - name: "Scenario %d"
    requests:
      - method: "GET"
        url: "/test%d"
`, i, i)
		} else {
			// Middle files
			content = fmt.Sprintf(`
includes:
  - "file%d.yaml"
scenarios:
  - name: "Scenario %d"
    requests:
      - method: "GET"
        url: "/test%d"
`, i+1, i, i)
		}

		if err := os.WriteFile(filename, []byte(content), 0644); err != nil {
			t.Fatalf("Failed to create test file %d: %v", i, err)
		}
	}

	p := parser.NewParser()
	_, err := p.ParseFile(filepath.Join(tmpDir, "file0.yaml"))

	if err == nil {
		t.Fatal("Expected error for max depth exceeded, got nil")
	}

	if !strings.Contains(err.Error(), "maximum include depth exceeded") {
		t.Errorf("Expected 'maximum include depth exceeded' error, got %v", err)
	}
}

func TestParser_EnhancedErrorHandling(t *testing.T) {
	tests := []struct {
		name        string
		yaml        string
		expectError string
	}{
		{
			name: "invalid duration with line info",
			yaml: `
version: "1.0"
name: "Test"
duration: "invalid-duration"
concurrency: 1
scenarios:
  - name: "Test"
    requests:
      - method: "GET"
        url: "/test"
`,
			expectError: "invalid duration format",
		},
		{
			name: "missing required field",
			yaml: `
name: "Test"
# missing version and duration
concurrency: 1
scenarios:
  - name: "Test"
    requests:
      - method: "GET"
        url: "/test"
`,
			expectError: "validation failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := parser.NewParser()
			_, err := p.ParseBytes([]byte(tt.yaml))

			if err == nil {
				t.Fatal("Expected error, got nil")
			}

			if !strings.Contains(err.Error(), tt.expectError) {
				t.Errorf("Expected error containing '%s', got %v", tt.expectError, err)
			}
		})
	}
}

func TestParser_NonExistentInclude(t *testing.T) {
	yamlContent := `
version: "1.0"
name: "Test"
duration: "1m"
concurrency: 1
includes:
  - "nonexistent.yaml"
scenarios:
  - name: "Test"
    requests:
      - method: "GET"
        url: "/test"
`

	p := parser.NewParser()
	_, err := p.ParseBytes([]byte(yamlContent))

	if err == nil {
		t.Fatal("Expected error for non-existent include, got nil")
	}

	if !strings.Contains(err.Error(), "failed to process includes") {
		t.Errorf("Expected 'failed to process includes' error, got %v", err)
	}
}

func TestParseError_ErrorMessage(t *testing.T) {
	tests := []struct {
		name     string
		parseErr *parser.ParseError
		expected string
	}{
		{
			name: "with filename",
			parseErr: &parser.ParseError{
				Message:  "test error",
				Line:     10,
				Column:   5,
				Filename: "test.yaml",
			},
			expected: "test.yaml:10:5: test error",
		},
		{
			name: "without filename",
			parseErr: &parser.ParseError{
				Message: "test error",
				Line:    10,
				Column:  5,
			},
			expected: "line 10, column 5: test error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.parseErr.Error() != tt.expected {
				t.Errorf("Expected error message '%s', got '%s'", tt.expected, tt.parseErr.Error())
			}
		})
	}
}
