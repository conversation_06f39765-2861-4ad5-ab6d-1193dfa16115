package unit

import (
	"neuralmetergo/internal/client"
	"testing"
	"time"
)

func TestLinearBackoffStrategy(t *testing.T) {
	config := client.DefaultBackoffConfig()
	config.BaseDelay = 100 * time.Millisecond
	config.MaxDelay = 1 * time.Second
	config.JitterStrategy = client.NoJitter // Disable jitter for predictable testing

	strategy := client.NewLinearBackoffStrategy(config)

	tests := []struct {
		attempt  int
		expected time.Duration
	}{
		{0, 100 * time.Millisecond}, // 100ms * (0 + 1) = 100ms
		{1, 200 * time.Millisecond}, // 100ms * (1 + 1) = 200ms
		{2, 300 * time.Millisecond}, // 100ms * (2 + 1) = 300ms
		{3, 400 * time.Millisecond}, // 100ms * (3 + 1) = 400ms
		{10, 1 * time.Second},       // Should be capped at MaxDelay
	}

	for _, test := range tests {
		result := strategy.CalculateBackoff(test.attempt, config.BaseDelay)
		if result != test.expected {
			t.<PERSON><PERSON>rf("Linear backoff attempt %d: expected %v, got %v",
				test.attempt, test.expected, result)
		}
	}
}

func TestExponentialBackoffStrategy(t *testing.T) {
	config := client.DefaultBackoffConfig()
	config.BaseDelay = 100 * time.Millisecond
	config.MaxDelay = 2 * time.Second
	config.Multiplier = 2.0
	config.JitterStrategy = client.NoJitter // Disable jitter for predictable testing

	strategy := client.NewExponentialBackoffStrategy(config)

	tests := []struct {
		attempt  int
		expected time.Duration
	}{
		{0, 100 * time.Millisecond},  // 100ms * 2^0 = 100ms
		{1, 200 * time.Millisecond},  // 100ms * 2^1 = 200ms
		{2, 400 * time.Millisecond},  // 100ms * 2^2 = 400ms
		{3, 800 * time.Millisecond},  // 100ms * 2^3 = 800ms
		{4, 1600 * time.Millisecond}, // 100ms * 2^4 = 1600ms
		{5, 2 * time.Second},         // Should be capped at MaxDelay
	}

	for _, test := range tests {
		result := strategy.CalculateBackoff(test.attempt, config.BaseDelay)
		if result != test.expected {
			t.Errorf("Exponential backoff attempt %d: expected %v, got %v",
				test.attempt, test.expected, result)
		}
	}
}

func TestPolynomialBackoffStrategy(t *testing.T) {
	config := client.DefaultBackoffConfig()
	config.BaseDelay = 100 * time.Millisecond
	config.MaxDelay = 5 * time.Second
	config.JitterStrategy = client.NoJitter // Disable jitter for predictable testing

	strategy := client.NewPolynomialBackoffStrategy(config, 2.0) // Quadratic

	tests := []struct {
		attempt  int
		expected time.Duration
	}{
		{0, 100 * time.Millisecond},  // 100ms * (0+1)^2 = 100ms
		{1, 400 * time.Millisecond},  // 100ms * (1+1)^2 = 400ms
		{2, 900 * time.Millisecond},  // 100ms * (2+1)^2 = 900ms
		{3, 1600 * time.Millisecond}, // 100ms * (3+1)^2 = 1600ms
		{4, 2500 * time.Millisecond}, // 100ms * (4+1)^2 = 2500ms
		{5, 3600 * time.Millisecond}, // 100ms * (5+1)^2 = 3600ms
		{10, 5 * time.Second},        // Should be capped at MaxDelay
	}

	for _, test := range tests {
		result := strategy.CalculateBackoff(test.attempt, config.BaseDelay)
		if result != test.expected {
			t.Errorf("Polynomial backoff attempt %d: expected %v, got %v",
				test.attempt, test.expected, result)
		}
	}
}

func TestJitterStrategies(t *testing.T) {
	config := client.DefaultBackoffConfig()
	config.BaseDelay = 1000 * time.Millisecond
	config.MaxDelay = 10 * time.Second

	t.Run("FullJitter", func(t *testing.T) {
		config.JitterStrategy = client.FullJitter
		strategy := client.NewLinearBackoffStrategy(config)

		// Test multiple times since jitter is random
		for i := 0; i < 10; i++ {
			result := strategy.CalculateBackoff(1, config.BaseDelay)
			// Full jitter should be between 0 and the calculated delay (2000ms for attempt 1)
			if result < 0 || result > 2000*time.Millisecond {
				t.Errorf("Full jitter result %v out of expected range [0, 2000ms]", result)
			}
		}
	})

	t.Run("EqualJitter", func(t *testing.T) {
		config.JitterStrategy = client.EqualJitter
		strategy := client.NewLinearBackoffStrategy(config)

		// Test multiple times since jitter is random
		for i := 0; i < 10; i++ {
			result := strategy.CalculateBackoff(1, config.BaseDelay)
			// Equal jitter should be between 50% and 100% of calculated delay (1000ms to 2000ms for attempt 1)
			if result < 1000*time.Millisecond || result > 2000*time.Millisecond {
				t.Errorf("Equal jitter result %v out of expected range [1000ms, 2000ms]", result)
			}
		}
	})

	t.Run("DecorrelatedJitter", func(t *testing.T) {
		config.JitterStrategy = client.DecorrelatedJitter
		strategy := client.NewLinearBackoffStrategy(config)

		// Test multiple sequential calls to verify decorrelation
		results := make([]time.Duration, 5)
		for i := 0; i < 5; i++ {
			results[i] = strategy.CalculateBackoff(1, config.BaseDelay)
		}

		// Verify that results vary (decorrelated)
		allSame := true
		for i := 1; i < len(results); i++ {
			if results[i] != results[0] {
				allSame = false
				break
			}
		}
		if allSame {
			t.Error("Decorrelated jitter produced identical results, should vary")
		}
	})
}

func TestAdaptiveBackoffStrategy(t *testing.T) {
	config := client.DefaultBackoffConfig()
	config.BaseDelay = 100 * time.Millisecond
	config.MaxDelay = 5 * time.Second
	config.JitterStrategy = client.NoJitter
	config.AdaptiveFactor = 0.5
	config.AdaptiveWindow = 5
	config.AdaptiveMinFactor = 0.5
	config.AdaptiveMaxFactor = 2.0

	baseStrategy := client.NewLinearBackoffStrategy(config)
	strategy := client.NewAdaptiveBackoffStrategy(config, baseStrategy)

	// Adaptive strategy should use type assertion to access RecordAttempt
	adaptiveStrategy, ok := strategy.(*client.AdaptiveBackoffStrategy)
	if !ok {
		t.Fatal("Expected AdaptiveBackoffStrategy type")
	}

	// Initial calculation should be same as base strategy
	initial := strategy.CalculateBackoff(1, config.BaseDelay)
	expected := baseStrategy.CalculateBackoff(1, config.BaseDelay)
	if initial != expected {
		t.Errorf("Initial adaptive calculation: expected %v, got %v", expected, initial)
	}

	// Record several failures - should increase delays
	for i := 0; i < 5; i++ {
		adaptiveStrategy.RecordAttempt(false, 100*time.Millisecond)
	}

	// After failures, delay should be higher than base
	afterFailures := strategy.CalculateBackoff(1, config.BaseDelay)
	if afterFailures <= initial {
		t.Errorf("Adaptive backoff should increase after failures: initial=%v, after=%v",
			initial, afterFailures)
	}

	// Record several successes - should decrease delays
	for i := 0; i < 8; i++ {
		adaptiveStrategy.RecordAttempt(true, 100*time.Millisecond)
	}

	// After successes, delay should be lower
	afterSuccesses := strategy.CalculateBackoff(1, config.BaseDelay)
	if afterSuccesses >= afterFailures {
		t.Errorf("Adaptive backoff should decrease after successes: after_failures=%v, after_successes=%v",
			afterFailures, afterSuccesses)
	}
}

func TestTemperatureBackoffStrategy(t *testing.T) {
	config := client.DefaultBackoffConfig()
	config.BaseDelay = 100 * time.Millisecond
	config.MaxDelay = 5 * time.Second
	config.JitterStrategy = client.NoJitter
	config.TemperatureFactor = 0.5

	baseStrategy := client.NewLinearBackoffStrategy(config)
	strategy := client.NewTemperatureBackoffStrategy(config, baseStrategy)

	// Temperature strategy should use type assertion to access RecordFailure
	temperatureStrategy, ok := strategy.(*client.TemperatureBackoffStrategy)
	if !ok {
		t.Fatal("Expected TemperatureBackoffStrategy type")
	}

	// Initial calculation should be same as base strategy (temperature starts at 0)
	initial := strategy.CalculateBackoff(1, config.BaseDelay)
	expected := baseStrategy.CalculateBackoff(1, config.BaseDelay)
	if initial != expected {
		t.Errorf("Initial temperature calculation: expected %v, got %v", expected, initial)
	}

	// Record failures to increase temperature
	for i := 0; i < 5; i++ {
		temperatureStrategy.RecordFailure()
	}

	// After failures, delay should be higher due to increased temperature
	afterFailures := strategy.CalculateBackoff(1, config.BaseDelay)
	if afterFailures <= initial {
		t.Errorf("Temperature backoff should increase after failures: initial=%v, after=%v",
			initial, afterFailures)
	}
}

func TestBackoffStrategyFactory(t *testing.T) {
	config := client.DefaultBackoffConfig()

	tests := []struct {
		strategyType client.BackoffStrategyType
		expectedName string
	}{
		{client.LinearBackoff, "linear"},
		{client.ExponentialBackoff, "exponential"},
		{client.PolynomialBackoff, "polynomial"},
		{client.AdaptiveLinearBackoff, "adaptive_linear"},
		{client.AdaptiveExponentialBackoff, "adaptive_exponential"},
		{client.TemperatureLinearBackoff, "temperature_linear"},
		{client.TemperatureExponentialBackoff, "temperature_exponential"},
	}

	for _, test := range tests {
		strategy := client.CreateBackoffStrategy(test.strategyType, config)
		if strategy == nil {
			t.Errorf("Factory returned nil for strategy type %v", test.strategyType)
			continue
		}

		name := strategy.Name()
		if name != test.expectedName {
			t.Errorf("Strategy type %v: expected name %s, got %s",
				test.strategyType, test.expectedName, name)
		}

		// Test that strategy can calculate backoff
		delay := strategy.CalculateBackoff(1, 100*time.Millisecond)
		if delay <= 0 {
			t.Errorf("Strategy %s produced invalid delay: %v", name, delay)
		}
	}
}

func TestBackoffStrategyReset(t *testing.T) {
	config := client.DefaultBackoffConfig()
	config.JitterStrategy = client.DecorrelatedJitter

	strategy := client.NewLinearBackoffStrategy(config)

	// Make some calculations to establish state
	strategy.CalculateBackoff(1, config.BaseDelay)
	strategy.CalculateBackoff(2, config.BaseDelay)

	// Reset should clear internal state
	strategy.Reset()

	// After reset, calculations should behave as if starting fresh
	result := strategy.CalculateBackoff(0, config.BaseDelay)
	if result <= 0 {
		t.Errorf("Strategy calculation after reset produced invalid result: %v", result)
	}
}

func TestBackoffConfigDefaults(t *testing.T) {
	config := client.DefaultBackoffConfig()

	// Verify sensible defaults
	if config.BaseDelay != 100*time.Millisecond {
		t.Errorf("Expected BaseDelay 100ms, got %v", config.BaseDelay)
	}

	if config.MaxDelay != 30*time.Second {
		t.Errorf("Expected MaxDelay 30s, got %v", config.MaxDelay)
	}

	if config.Multiplier != 2.0 {
		t.Errorf("Expected Multiplier 2.0, got %v", config.Multiplier)
	}

	if config.JitterStrategy != client.EqualJitter {
		t.Errorf("Expected EqualJitter, got %v", config.JitterStrategy)
	}

	if !config.EnableAdaptive {
		t.Error("Expected EnableAdaptive to be true")
	}

	if !config.EnableTemperature {
		t.Error("Expected EnableTemperature to be true")
	}
}

func BenchmarkBackoffStrategies(b *testing.B) {
	config := client.DefaultBackoffConfig()
	config.JitterStrategy = client.NoJitter // Disable jitter for consistent benchmarking

	strategies := map[string]client.BackoffStrategy{
		"linear":      client.NewLinearBackoffStrategy(config),
		"exponential": client.NewExponentialBackoffStrategy(config),
		"polynomial":  client.NewPolynomialBackoffStrategy(config, 2.0),
	}

	for name, strategy := range strategies {
		b.Run(name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				strategy.CalculateBackoff(i%10, config.BaseDelay)
			}
		})
	}
}
