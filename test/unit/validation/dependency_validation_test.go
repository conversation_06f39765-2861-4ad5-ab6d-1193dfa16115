// Package validation provides tests for the DependencyValidator
package validation

import (
	"testing"
	"time"

	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"
)

func TestDependencyValidator_VariableDependencies(t *testing.T) {
	tests := []struct {
		name           string
		testPlan       *parser.TestPlan
		expectedIssues []string // List of expected issue codes
	}{
		{
			name: "Valid variable flow",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Global: parser.Global{
					Variables: map[string]string{
						"api_key": "secret123",
					},
				},
				Scenarios: []parser.Scenario{
					{
						Name: "Auth Flow",
						Requests: []parser.Request{
							{
								Method: "POST",
								URL:    "/auth/login",
								Headers: map[string]string{
									"Authorization": "Bearer {{.api_key}}",
								},
								Extract: []parser.Extract{
									{Name: "auth_token", Type: "json_path", Path: "$.token"},
								},
								Assertions: []parser.Assertion{
									{Type: "status_code", Operator: "eq", Value: 200},
								},
							},
							{
								Method: "GET",
								URL:    "/user/profile",
								Headers: map[string]string{
									"Authorization": "Bearer {{.auth_token}}",
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"AUTH_BEFORE_EXTRACTION"}, // Expect warning about using auth before extraction
		},
		{
			name: "Undefined variable usage",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Invalid Flow",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/{{.undefined_var}}/data",
							},
						},
					},
				},
			},
			expectedIssues: []string{"UNDEFINED_VARIABLE"},
		},
		{
			name: "Forward dependency error",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Invalid Order",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/user/{{.user_id}}", // Using variable before extraction
							},
							{
								Method: "POST",
								URL:    "/auth/login",
								Extract: []parser.Extract{
									{Name: "user_id", Type: "json_path", Path: "$.user.id"}, // Extracted after usage
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"UNDEFINED_VARIABLE"},
		},
		{
			name: "Duplicate variable extraction",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Duplicate Extraction",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/data1",
								Extract: []parser.Extract{
									{Name: "token", Type: "json_path", Path: "$.token"},
								},
							},
							{
								Method: "GET",
								URL:    "/api/data2",
								Extract: []parser.Extract{
									{Name: "token", Type: "json_path", Path: "$.access_token"}, // Duplicate name
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"DUPLICATE_EXTRACTION"},
		},
		{
			name: "Empty extraction name",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Invalid Extraction",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/data",
								Extract: []parser.Extract{
									{Name: "", Type: "json_path", Path: "$.token"}, // Empty name
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"EMPTY_EXTRACT_NAME"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			validator := validation.NewDependencyValidator()
			result := &validation.ValidationResult{}

			validator.Validate(tt.testPlan, result)

			// Check that expected issues are present
			issueCodeMap := make(map[string]bool)
			for _, issue := range result.Issues {
				issueCodeMap[issue.Code] = true
			}

			for _, expectedCode := range tt.expectedIssues {
				if !issueCodeMap[expectedCode] {
					t.Errorf("Expected issue code %s not found in results", expectedCode)
				}
			}

			// Check that no unexpected errors occurred
			if len(tt.expectedIssues) == 0 && len(result.Issues) > 0 {
				t.Errorf("Expected no issues but got %d issues: %v", len(result.Issues), result.Issues)
			}
		})
	}
}

func TestDependencyValidator_AssertionLogic(t *testing.T) {
	tests := []struct {
		name           string
		testPlan       *parser.TestPlan
		expectedIssues []string
	}{
		{
			name: "Valid assertions",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Valid Assertions",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/test",
								Assertions: []parser.Assertion{
									{Type: "status_code", Operator: "eq", Value: 200},
									{Type: "response_time", Operator: "lt", Value: "5s"},
									{Type: "json_path", Field: "$.data.id", Operator: "ne", Value: ""},
									{Type: "header_exists", Field: "Content-Type"},
									{Type: "contains", Operator: "contains", Value: "success"},
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{}, // No issues expected
		},
		{
			name: "Missing assertion type",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Invalid Assertion",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/test",
								Assertions: []parser.Assertion{
									{Type: "", Value: 200}, // Missing type
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"MISSING_ASSERTION_TYPE"},
		},
		{
			name: "Missing status code value",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Invalid Status Code",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/test",
								Assertions: []parser.Assertion{
									{Type: "status_code", Value: nil}, // Missing value
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"MISSING_STATUS_CODE_VALUE"},
		},
		{
			name: "Invalid status code range",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Invalid Status Code Range",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/test",
								Assertions: []parser.Assertion{
									{Type: "status_code", Value: 999}, // Invalid range
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"INVALID_STATUS_CODE_RANGE"},
		},
		{
			name: "Invalid duration format",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Invalid Duration",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/test",
								Assertions: []parser.Assertion{
									{Type: "response_time", Value: "invalid_duration"}, // Invalid format
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"INVALID_DURATION_FORMAT"},
		},
		{
			name: "Invalid JSONPath syntax",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Invalid JSONPath",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/test",
								Assertions: []parser.Assertion{
									{Type: "json_path", Field: "data.id"}, // Missing $ prefix
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"INVALID_JSON_PATH_SYNTAX"},
		},
		{
			name: "Incompatible operator",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Incompatible Operator",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/test",
								Assertions: []parser.Assertion{
									{Type: "response_time", Operator: "contains", Value: "5s"}, // Wrong operator for response_time
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"INCOMPATIBLE_OPERATOR"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			validator := validation.NewDependencyValidator()
			result := &validation.ValidationResult{}

			validator.Validate(tt.testPlan, result)

			// Check that expected issues are present
			issueCodeMap := make(map[string]bool)
			for _, issue := range result.Issues {
				if issue.Category == "dependency" {
					issueCodeMap[issue.Code] = true
				}
			}

			for _, expectedCode := range tt.expectedIssues {
				if !issueCodeMap[expectedCode] {
					t.Errorf("Expected issue code %s not found in results", expectedCode)
				}
			}

			// Check that no unexpected errors occurred
			if len(tt.expectedIssues) == 0 {
				dependencyIssues := 0
				for _, issue := range result.Issues {
					if issue.Category == "dependency" {
						dependencyIssues++
					}
				}
				if dependencyIssues > 0 {
					t.Errorf("Expected no dependency issues but got %d issues", dependencyIssues)
				}
			}
		})
	}
}

func TestDependencyValidator_CircularDependencies(t *testing.T) {
	tests := []struct {
		name           string
		testPlan       *parser.TestPlan
		expectedIssues []string
	}{
		{
			name: "No circular dependencies",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Linear Flow",
						Requests: []parser.Request{
							{
								Method: "POST",
								URL:    "/auth/login",
								Extract: []parser.Extract{
									{Name: "token", Type: "json_path", Path: "$.token"},
								},
							},
							{
								Method: "GET",
								URL:    "/user/profile",
								Headers: map[string]string{
									"Authorization": "Bearer {{.token}}",
								},
								Extract: []parser.Extract{
									{Name: "user_id", Type: "json_path", Path: "$.user.id"},
								},
							},
							{
								Method: "GET",
								URL:    "/user/{{.user_id}}/details",
								Headers: map[string]string{
									"Authorization": "Bearer {{.token}}",
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{}, // No circular dependencies
		},
		{
			name: "Circular dependency detected",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Circular Flow",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/{{.var_b}}",
								Extract: []parser.Extract{
									{Name: "var_a", Type: "json_path", Path: "$.a"},
								},
							},
							{
								Method: "GET",
								URL:    "/api/{{.var_a}}",
								Extract: []parser.Extract{
									{Name: "var_b", Type: "json_path", Path: "$.b"},
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"CIRCULAR_DEPENDENCY"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			validator := validation.NewDependencyValidator()
			result := &validation.ValidationResult{}

			validator.Validate(tt.testPlan, result)

			// Check that expected issues are present
			issueCodeMap := make(map[string]bool)
			for _, issue := range result.Issues {
				if issue.Category == "dependency" {
					issueCodeMap[issue.Code] = true
				}
			}

			for _, expectedCode := range tt.expectedIssues {
				if !issueCodeMap[expectedCode] {
					t.Errorf("Expected issue code %s not found in results", expectedCode)
				}
			}

			// Check that no unexpected errors occurred
			if len(tt.expectedIssues) == 0 {
				dependencyIssues := 0
				for _, issue := range result.Issues {
					if issue.Category == "dependency" && issue.Code == "CIRCULAR_DEPENDENCY" {
						dependencyIssues++
					}
				}
				if dependencyIssues > 0 {
					t.Errorf("Expected no circular dependency issues but got %d issues", dependencyIssues)
				}
			}
		})
	}
}

func TestDependencyValidator_RequestFlow(t *testing.T) {
	tests := []struct {
		name           string
		testPlan       *parser.TestPlan
		expectedIssues []string
	}{
		{
			name: "Good authentication flow",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Good Auth Flow",
						Requests: []parser.Request{
							{
								Method: "POST",
								URL:    "/auth/login",
								Extract: []parser.Extract{
									{Name: "auth_token", Type: "json_path", Path: "$.token"},
								},
							},
							{
								Method: "GET",
								URL:    "/user/profile",
								Headers: map[string]string{
									"Authorization": "Bearer {{.auth_token}}",
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"WRITE_WITHOUT_ASSERTION"}, // POST without assertions generates info-level issue
		},
		{
			name: "Auth before extraction",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Bad Auth Flow",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/user/profile",
								Headers: map[string]string{
									"Authorization": "Bearer {{.auth_token}}", // Using token before extraction
								},
							},
							{
								Method: "POST",
								URL:    "/auth/login",
								Extract: []parser.Extract{
									{Name: "auth_token", Type: "json_path", Path: "$.token"}, // Extract after usage
								},
							},
						},
					},
				},
			},
			expectedIssues: []string{"UNDEFINED_VARIABLE"},
		},
		{
			name: "Unused auth token",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Unused Token",
						Requests: []parser.Request{
							{
								Method: "POST",
								URL:    "/auth/login",
								Extract: []parser.Extract{
									{Name: "auth_token", Type: "json_path", Path: "$.token"}, // Extracted but never used
								},
							},
							{
								Method: "GET",
								URL:    "/public/data", // Public endpoint, no auth needed
							},
						},
					},
				},
			},
			expectedIssues: []string{"UNUSED_EXTRACTION"},
		},
		{
			name: "Unused extraction",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Unused Extraction",
						Requests: []parser.Request{
							{
								Method: "GET",
								URL:    "/api/data",
								Extract: []parser.Extract{
									{Name: "some_value", Type: "json_path", Path: "$.value"}, // Extracted but never used
								},
							},
							{
								Method: "GET",
								URL:    "/api/other", // Doesn't use extracted value
							},
						},
					},
				},
			},
			expectedIssues: []string{"UNUSED_EXTRACTION"},
		},
		{
			name: "GET after DELETE warning",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "DELETE then GET",
						Requests: []parser.Request{
							{
								Method: "DELETE",
								URL:    "/api/users/123",
							},
							{
								Method: "GET",
								URL:    "/api/users/123", // GET after DELETE on same resource
							},
						},
					},
				},
			},
			expectedIssues: []string{}, // This validator pattern not implemented yet
		},
		{
			name: "Write without assertion",
			testPlan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Plan",
				Duration:    parser.Duration{Duration: ParseDuration("5m")},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name: "Unverified Write",
						Requests: []parser.Request{
							{
								Method:     "POST",
								URL:        "/api/users",
								Body:       map[string]interface{}{"name": "John Doe"},
								Assertions: []parser.Assertion{}, // No assertions for write operation
							},
						},
					},
				},
			},
			expectedIssues: []string{"WRITE_WITHOUT_ASSERTION"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			validator := validation.NewDependencyValidator()
			result := &validation.ValidationResult{}

			validator.Validate(tt.testPlan, result)

			// Check that expected issues are present
			issueCodeMap := make(map[string]bool)
			for _, issue := range result.Issues {
				if issue.Category == "dependency" {
					issueCodeMap[issue.Code] = true
				}
			}

			for _, expectedCode := range tt.expectedIssues {
				if !issueCodeMap[expectedCode] {
					t.Errorf("Expected issue code %s not found in results", expectedCode)
				}
			}

			// For tests expecting no issues, only check error-level dependency issues
			// Allow info and warning level issues to pass through
			if len(tt.expectedIssues) == 0 {
				errorIssues := 0
				for _, issue := range result.Issues {
					if issue.Category == "dependency" && issue.Severity == validation.SeverityError {
						errorIssues++
					}
				}
				if errorIssues > 0 {
					t.Errorf("Expected no error-level dependency issues but got %d error issues", errorIssues)
				}
			}
		})
	}
}

func TestDependencyValidator_EmptyScenarios(t *testing.T) {
	testPlan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Test Plan",
		Duration:    parser.Duration{Duration: ParseDuration("5m")},
		Concurrency: 10,
		Scenarios: []parser.Scenario{
			{
				Name:     "Empty Scenario",
				Requests: []parser.Request{}, // No requests
			},
			{
				Name: "Duplicate Name",
			},
			{
				Name: "Duplicate Name", // Same name as above
			},
		},
	}

	validator := validation.NewDependencyValidator()
	result := &validation.ValidationResult{}

	validator.Validate(testPlan, result)

	// Check for expected issues
	expectedCodes := []string{"EMPTY_SCENARIO", "DUPLICATE_SCENARIO_NAME"}
	issueCodeMap := make(map[string]bool)
	for _, issue := range result.Issues {
		if issue.Category == "dependency" {
			issueCodeMap[issue.Code] = true
		}
	}

	for _, expectedCode := range expectedCodes {
		if !issueCodeMap[expectedCode] {
			t.Errorf("Expected issue code %s not found in results", expectedCode)
		}
	}
}

// Helper function to create a duration
func ParseDuration(s string) time.Duration {
	d, _ := time.ParseDuration(s)
	return d
}
