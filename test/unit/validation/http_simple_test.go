package validation

import (
	"testing"
	"time"

	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"
)

func TestHTTPValidator_Basic(t *testing.T) {
	// Test valid HTTP configuration
	validPlan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Test Plan",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:   "Valid Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "GET Request",
						Method: "GET",
						URL:    "/users",
						Headers: map[string]string{
							"Authorization": "Bearer token",
							"Content-Type":  "application/json",
						},
						Assertions: []parser.Assertion{
							{Type: "status_code", Value: 200},
							{Type: "response_time", Operator: "lt", Value: 1000},
						},
						Extract: []parser.Extract{
							{Name: "user_id", Type: "json_path", Path: "$.id"},
						},
					},
				},
			},
		},
	}

	engine := validation.NewValidationEngine()
	result := engine.ValidateTestPlan(validPlan)

	if !result.IsValid() {
		t.Errorf("Valid plan should pass validation")
		for _, issue := range result.Issues {
			if issue.Severity == validation.SeverityError {
				t.Logf("Error: %s - %s", issue.Code, issue.Message)
			}
		}
	}

	// Test invalid HTTP method
	invalidPlan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Invalid Test Plan",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Scenarios: []parser.Scenario{
			{
				Name:   "Invalid Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Invalid Method",
						Method: "INVALID",
						URL:    "/test",
					},
				},
			},
		},
	}

	result = engine.ValidateTestPlan(invalidPlan)
	if result.IsValid() {
		t.Errorf("Invalid plan should fail validation")
	}

	// Verify specific error
	found := false
	for _, issue := range result.Issues {
		if issue.Code == "INVALID_HTTP_METHOD" {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Expected INVALID_HTTP_METHOD error not found")
	}
}

func TestHTTPValidator_URLValidation(t *testing.T) {
	// Test URL validation without base URL
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "URL Test",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Scenarios: []parser.Scenario{
			{
				Name:   "URL Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Relative URL without base",
						Method: "GET",
						URL:    "/users", // Relative URL without base URL should fail
					},
				},
			},
		},
	}

	engine := validation.NewValidationEngine()
	result := engine.ValidateTestPlan(plan)

	// Should find URL validation error
	found := false
	for _, issue := range result.Issues {
		if issue.Code == "INVALID_URL" {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Expected INVALID_URL error for relative URL without base URL")
		for _, issue := range result.Issues {
			t.Logf("Issue: %s - %s", issue.Code, issue.Message)
		}
	}
}

func TestHTTPValidator_AssertionValidation(t *testing.T) {
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Assertion Test",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:   "Assertion Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Invalid Assertions",
						Method: "GET",
						URL:    "/test",
						Assertions: []parser.Assertion{
							{Type: "invalid_type", Value: 200},
							{Type: "status_code"}, // Missing value
						},
					},
				},
			},
		},
	}

	engine := validation.NewValidationEngine()
	result := engine.ValidateTestPlan(plan)

	// Should find assertion validation errors
	var errorCodes []string
	for _, issue := range result.Issues {
		if issue.Severity == validation.SeverityError {
			errorCodes = append(errorCodes, issue.Code)
		}
	}

	expectedCodes := []string{"INVALID_ASSERTION_TYPE", "MISSING_ASSERTION_VALUE"}
	for _, expected := range expectedCodes {
		found := false
		for _, code := range errorCodes {
			if code == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected error code %s not found. Found: %v", expected, errorCodes)
		}
	}
}

func TestHTTPValidator_ExtractionValidation(t *testing.T) {
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Extraction Test",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:   "Extraction Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Invalid Extractions",
						Method: "GET",
						URL:    "/test",
						Extract: []parser.Extract{
							{Name: "bad_type", Type: "invalid_type", Path: "$.data"},
							{Name: "no_path", Type: "json_path", Path: ""},
							{Name: "bad_regex", Type: "regex", Path: "[invalid regex"},
						},
					},
				},
			},
		},
	}

	engine := validation.NewValidationEngine()
	result := engine.ValidateTestPlan(plan)

	// Should find extraction validation errors
	var errorCodes []string
	for _, issue := range result.Issues {
		if issue.Severity == validation.SeverityError {
			errorCodes = append(errorCodes, issue.Code)
		}
	}

	expectedCodes := []string{"INVALID_EXTRACT_TYPE", "MISSING_EXTRACT_PATH", "INVALID_REGEX"}
	for _, expected := range expectedCodes {
		found := false
		for _, code := range errorCodes {
			if code == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected error code %s not found. Found: %v", expected, errorCodes)
		}
	}
}
