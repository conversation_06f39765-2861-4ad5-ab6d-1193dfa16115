package validation

import (
	"testing"
	"time"

	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"
)

func TestValidationEngine_ValidateTestPlan(t *testing.T) {
	tests := []struct {
		name             string
		plan             *parser.TestPlan
		expectedErrors   int
		expectedWarnings int
		expectedInfos    int
		specificCodes    []string
	}{
		{
			name: "valid complete test plan",
			plan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Suite",
				Duration:    parser.Duration{Duration: 5 * time.Minute},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name:   "Test Scenario",
						Weight: 100,
						Requests: []parser.Request{
							{
								Name:   "Test Request",
								Method: "GET",
								URL:    "https://api.example.com/test",
								Headers: map[string]string{
									"Accept": "application/json",
								},
								Assertions: []parser.Assertion{
									{
										Type:     "status_code",
										Operator: "eq",
										Value:    "200",
									},
								},
							},
						},
					},
				},
			},
			expectedErrors:   0,
			expectedWarnings: 0,
			expectedInfos:    0,
			specificCodes:    []string{},
		},
		{
			name: "missing required fields",
			plan: &parser.TestPlan{
				// Missing version, name, duration
				Concurrency: 10,
				Scenarios:   []parser.Scenario{},
			},
			expectedErrors: 5, // version, name, duration, no scenarios, zero total weight
			specificCodes:  []string{"MISSING_VERSION", "MISSING_NAME", "MISSING_DURATION", "NO_SCENARIOS", "ZERO_TOTAL_WEIGHT"},
		},
		{
			name: "invalid HTTP methods and URLs",
			plan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Suite",
				Duration:    parser.Duration{Duration: 5 * time.Minute},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name:   "Test Scenario",
						Weight: 100,
						Requests: []parser.Request{
							{
								Name:   "Invalid Request",
								Method: "INVALID_METHOD",
								URL:    "not-a-valid-url",
							},
						},
					},
				},
			},
			expectedErrors: 2, // invalid method and invalid URL
			specificCodes:  []string{"INVALID_HTTP_METHOD", "INVALID_URL"},
		},
		{
			name: "performance warnings",
			plan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Suite",
				Duration:    parser.Duration{Duration: 30 * time.Hour}, // Very long duration
				Concurrency: 10000,                                     // Very high concurrency
				Scenarios: []parser.Scenario{
					{
						Name:   "Test Scenario",
						Weight: 100,
						Requests: []parser.Request{
							{
								Name:    "Fast Timeout Request",
								Method:  "GET",
								URL:     "https://api.example.com/test",
								Timeout: parser.Duration{Duration: 100 * time.Millisecond}, // Very short timeout
							},
						},
					},
				},
			},
			expectedWarnings: 3, // long duration, high concurrency, short timeout
			specificCodes:    []string{"LONG_DURATION", "HIGH_CONCURRENCY", "SHORT_TIMEOUT"},
		},
		{
			name: "semantic validation issues",
			plan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Suite",
				Duration:    parser.Duration{Duration: 5 * time.Minute},
				Concurrency: 10,
				Variables: []parser.Variable{
					{Name: "duplicate_var", Type: "static", Value: "value1"},
					{Name: "duplicate_var", Type: "static", Value: "value2"},
				},
				Scenarios: []parser.Scenario{
					{
						Name:   "Scenario 1",
						Weight: 0, // Zero weight
					},
					{
						Name:   "Scenario 1", // Duplicate name
						Weight: 100,
						Requests: []parser.Request{
							{
								Name:   "Test Request",
								Method: "GET",
								URL:    "https://api.example.com/test",
							},
						},
					},
				},
			},
			expectedErrors:   2, // NO_REQUESTS and EMPTY_SCENARIO for zero weight scenario
			expectedWarnings: 3, // duplicate variable and 2x duplicate scenario name
			specificCodes:    []string{"NO_REQUESTS", "EMPTY_SCENARIO", "DUPLICATE_VARIABLE", "DUPLICATE_SCENARIO_NAME"},
		},
		{
			name: "invalid assertions",
			plan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Suite",
				Duration:    parser.Duration{Duration: 5 * time.Minute},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name:   "Test Scenario",
						Weight: 100,
						Requests: []parser.Request{
							{
								Name:   "Test Request",
								Method: "GET",
								URL:    "https://api.example.com/test",
								Assertions: []parser.Assertion{
									{
										Type:     "invalid_assertion_type",
										Operator: "eq",
										Value:    "200",
									},
									{
										Type:     "status_code",
										Operator: "invalid_operator",
										Value:    "200",
									},
									{
										Type:     "json_path",
										Field:    "", // Missing field for json_path
										Operator: "eq",
										Value:    "test",
									},
								},
							},
						},
					},
				},
			},
			expectedErrors: 5, // invalid assertion type, invalid operator, missing field, incompatible operator, missing JSON path field
			specificCodes:  []string{"INVALID_ASSERTION_TYPE", "INVALID_ASSERTION_OPERATOR", "MISSING_JSON_PATH"},
		},
		{
			name: "invalid extractions",
			plan: &parser.TestPlan{
				Version:     "1.0",
				Name:        "Test Suite",
				Duration:    parser.Duration{Duration: 5 * time.Minute},
				Concurrency: 10,
				Scenarios: []parser.Scenario{
					{
						Name:   "Test Scenario",
						Weight: 100,
						Requests: []parser.Request{
							{
								Name:   "Test Request",
								Method: "GET",
								URL:    "https://api.example.com/test",
								Extract: []parser.Extract{
									{
										Name: "", // Missing name
										Type: "json_path",
										Path: "$.data",
									},
									{
										Name: "test_extract",
										Type: "invalid_extract_type",
										Path: "$.data",
									},
									{
										Name: "json_extract",
										Type: "json_path",
										Path: "", // Missing path for json_path
									},
								},
							},
						},
					},
				},
			},
			expectedErrors:   3, // invalid type, missing path, empty extraction name
			expectedWarnings: 0,
			expectedInfos:    3, // unused extractions detected for each extract
			specificCodes:    []string{"INVALID_EXTRACT_TYPE", "MISSING_EXTRACT_PATH", "EMPTY_EXTRACT_NAME"},
		},
	}

	config := &validation.EngineConfig{
		StrictMode:        true,
		EnableWarnings:    true,
		EnableSuggestions: true,
	}
	engine := validation.NewValidationEngineWithConfig(config)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := engine.ValidateTestPlan(tt.plan)

			// Count severity levels
			var errors, warnings, infos int
			for _, issue := range result.Issues {
				switch issue.Severity {
				case validation.SeverityError:
					errors++
				case validation.SeverityWarning:
					warnings++
				case validation.SeverityInfo:
					infos++
				}
			}

			if errors != tt.expectedErrors {
				t.Errorf("Expected %d errors, got %d", tt.expectedErrors, errors)
				for _, issue := range result.Issues {
					if issue.Severity == validation.SeverityError {
						t.Logf("Error: %s - %s", issue.Code, issue.Message)
					}
				}
			}

			if warnings != tt.expectedWarnings {
				t.Errorf("Expected %d warnings, got %d", tt.expectedWarnings, warnings)
				for _, issue := range result.Issues {
					if issue.Severity == validation.SeverityWarning {
						t.Logf("Warning: %s - %s", issue.Code, issue.Message)
					}
				}
			}

			if infos != tt.expectedInfos {
				t.Errorf("Expected %d infos, got %d", tt.expectedInfos, infos)
			}

			// Check for specific error codes
			foundCodes := make(map[string]bool)
			for _, issue := range result.Issues {
				foundCodes[issue.Code] = true
			}

			for _, expectedCode := range tt.specificCodes {
				if !foundCodes[expectedCode] {
					t.Errorf("Expected error code %s not found", expectedCode)
				}
			}
		})
	}
}

func TestValidationEngine_WithRealFixture(t *testing.T) {
	// Test with real example fixture
	p := parser.NewParser()
	plan, err := p.ParseFile("../../fixtures/example_test_plan.yaml")
	if err != nil {
		t.Fatalf("Failed to parse example fixture: %v", err)
	}

	config := &validation.EngineConfig{
		StrictMode:        false,
		EnableWarnings:    true,
		EnableSuggestions: true,
	}
	engine := validation.NewValidationEngineWithConfig(config)

	result := engine.ValidateTestPlan(plan)

	// The example fixture has some legitimate variable dependency issues
	// Count expected errors vs actual
	var errors, warnings, infos int
	undefinedVarCount := 0
	for _, issue := range result.Issues {
		switch issue.Severity {
		case validation.SeverityError:
			errors++
			if issue.Code == "UNDEFINED_VARIABLE" {
				undefinedVarCount++
			}
		case validation.SeverityWarning:
			warnings++
		case validation.SeverityInfo:
			infos++
		}
	}

	// Expect specific undefined variable errors: auth_token (3 times) and random_offset (1 time)
	expectedUndefinedVars := 4
	if undefinedVarCount != expectedUndefinedVars {
		t.Errorf("Expected %d undefined variable errors, got %d", expectedUndefinedVars, undefinedVarCount)
	}

	// Should have some info-level issues for unused extractions
	if infos == 0 {
		t.Error("Expected some info-level validation issues for unused extractions")
	}

	// Generate human-readable report
	report := result.GenerateReport()
	if report == "" {
		t.Log("No validation issues found in example fixture")
	} else {
		t.Logf("Validation report for example fixture:\n%s", report)
	}
}

func TestValidationEngine_CustomValidators(t *testing.T) {
	// Test individual validators
	config := &validation.EngineConfig{
		StrictMode: true,
	}
	engine := validation.NewValidationEngineWithConfig(config)

	// Test with only structure validator
	engine.ClearValidators()
	engine.AddValidator(validation.NewStructureValidator())

	plan := &parser.TestPlan{
		// Missing all required fields
	}

	result := engine.ValidateTestPlan(plan)
	if len(result.Issues) == 0 {
		t.Error("Expected structure validation issues")
	}

	// Verify all issues are from structure validator
	for _, issue := range result.Issues {
		if issue.Category != "structure" {
			t.Errorf("Expected only structure issues, got %s", issue.Category)
		}
	}
}

func TestValidationResult_IsValid(t *testing.T) {
	tests := []struct {
		name     string
		issues   []validation.ValidationIssue
		expected bool
	}{
		{
			name:     "no issues",
			issues:   []validation.ValidationIssue{},
			expected: true,
		},
		{
			name: "only warnings",
			issues: []validation.ValidationIssue{
				{Severity: validation.SeverityWarning, Code: "TEST_WARNING", Message: "Test warning"},
			},
			expected: true,
		},
		{
			name: "has errors",
			issues: []validation.ValidationIssue{
				{Severity: validation.SeverityError, Code: "TEST_ERROR", Message: "Test error"},
			},
			expected: false,
		},
		{
			name: "mixed severity levels",
			issues: []validation.ValidationIssue{
				{Severity: validation.SeverityInfo, Code: "TEST_INFO", Message: "Test info"},
				{Severity: validation.SeverityWarning, Code: "TEST_WARNING", Message: "Test warning"},
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := &validation.ValidationResult{
				Valid:  len(tt.issues) == 0,
				Issues: tt.issues,
			}
			// Calculate summary
			for _, issue := range tt.issues {
				switch issue.Severity {
				case validation.SeverityError:
					result.Summary.ErrorCount++
				case validation.SeverityWarning:
					result.Summary.WarningCount++
				case validation.SeverityInfo:
					result.Summary.InfoCount++
				}
			}

			if result.IsValid() != tt.expected {
				t.Errorf("Expected IsValid() to return %v", tt.expected)
			}
		})
	}
}

func TestValidationEngine_Options(t *testing.T) {
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Test Suite",
		Duration:    parser.Duration{Duration: 30 * time.Hour}, // Long duration (warning)
		Concurrency: 1500,                                      // High concurrency (warning)
		Scenarios: []parser.Scenario{
			{
				Name:   "Test Scenario",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Test Request",
						Method: "GET",
						URL:    "https://api.example.com/test",
					},
				},
			},
		},
	}

	t.Run("warnings disabled", func(t *testing.T) {
		config := &validation.EngineConfig{
			EnableWarnings: false,
		}
		engine := validation.NewValidationEngineWithConfig(config)

		result := engine.ValidateTestPlan(plan)

		// Should not have any warnings
		for _, issue := range result.Issues {
			if issue.Severity == validation.SeverityWarning {
				t.Error("Expected no warnings when warnings are disabled")
			}
		}
	})

	t.Run("info level disabled", func(t *testing.T) {
		config := &validation.EngineConfig{
			EnableSuggestions: false,
		}
		engine := validation.NewValidationEngineWithConfig(config)

		result := engine.ValidateTestPlan(plan)

		// Should not have any info level issues
		for _, issue := range result.Issues {
			if issue.Severity == validation.SeverityInfo {
				t.Error("Expected no info issues when info level is disabled")
			}
		}
	})
}
