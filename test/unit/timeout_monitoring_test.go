package unit

import (
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

func TestTimeoutMonitoringConfig_Validation(t *testing.T) {
	tests := []struct {
		name    string
		config  client.TimeoutMonitoringConfig
		wantErr bool
	}{
		{
			name: "valid config",
			config: client.TimeoutMonitoringConfig{
				Enabled: true,
				AlertThresholds: map[string]float64{
					"dial": 0.05,
					"tls":  0.03,
				},
				DistributionBuckets: []time.Duration{
					100 * time.Millisecond,
					500 * time.Millisecond,
					1 * time.Second,
					5 * time.Second,
				},
				MonitoringWindow: 5 * time.Minute,
				MinSampleSize:    10,
			},
			wantErr: false,
		},
		{
			name: "disabled config",
			config: client.TimeoutMonitoringConfig{
				Enabled: false,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// For now, there's no specific validation method, but we can test the config creation
			if tt.config.Enabled {
				if len(tt.config.AlertThresholds) == 0 && tt.wantErr {
					t.Error("Expected error for invalid config, but got none")
				}
			}
		})
	}
}

func TestTimeoutMonitoring_ConfigurationIntegration(t *testing.T) {
	// Create HTTP client with timeout monitoring enabled
	config := client.DefaultConfig()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()

	httpClient := client.NewHTTPClient(config)

	// Test that monitoring report is available
	report := httpClient.GetTimeoutMonitoringReport()

	if !report["enabled"].(bool) {
		t.Error("Expected timeout monitoring to be enabled")
	}

	// Test metrics summary includes timeout monitoring
	summary := httpClient.GetMetricsSummary()
	if _, exists := summary["timeout_monitoring"]; !exists {
		t.Error("Expected timeout_monitoring section in metrics summary")
	}

	// Test breakdown summary is available
	breakdown := httpClient.GetTimeoutBreakdownSummary()
	if _, exists := breakdown["total_requests"]; !exists {
		t.Error("Expected total_requests in breakdown summary")
	}
}

func TestTimeoutMonitoring_BasicConfiguration(t *testing.T) {
	config := client.DefaultConfig()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
	config.TimeoutMonitoring.DistributionBuckets = []time.Duration{
		100 * time.Millisecond,
		500 * time.Millisecond,
		1 * time.Second,
		5 * time.Second,
	}

	httpClient := client.NewHTTPClient(config)

	// Verify client was created with monitoring config
	if httpClient == nil {
		t.Error("Expected HTTP client to be created successfully")
	}

	// Verify monitoring is enabled in config
	if !config.TimeoutMonitoring.Enabled {
		t.Error("Expected timeout monitoring to be enabled")
	}

	// Verify distribution buckets were set
	if len(config.TimeoutMonitoring.DistributionBuckets) != 4 {
		t.Errorf("Expected 4 distribution buckets, got %d", len(config.TimeoutMonitoring.DistributionBuckets))
	}
}

func TestTimeoutMonitoring_DefaultConfiguration(t *testing.T) {
	config := client.DefaultTimeoutMonitoringConfig()

	// Verify default configuration is reasonable
	if !config.Enabled {
		t.Error("Expected default timeout monitoring to be enabled")
	}

	if len(config.AlertThresholds) == 0 {
		t.Error("Expected default alert thresholds to be configured")
	}

	if len(config.DistributionBuckets) == 0 {
		t.Error("Expected default distribution buckets to be configured")
	}

	if config.MonitoringWindow == 0 {
		t.Error("Expected default monitoring window to be set")
	}

	if config.MinSampleSize == 0 {
		t.Error("Expected default minimum sample size to be set")
	}
}

func TestTimeoutMonitoring_MetricsSummaryIntegration(t *testing.T) {
	config := client.DefaultConfig()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()

	httpClient := client.NewHTTPClient(config)

	// Get metrics summary
	summary := httpClient.GetMetricsSummary()

	// Verify timeout monitoring section exists
	timeoutMonitoring, ok := summary["timeout_monitoring"].(map[string]interface{})
	if !ok {
		t.Error("Expected timeout_monitoring section in metrics summary")
	}

	if !timeoutMonitoring["enabled"].(bool) {
		t.Error("Expected timeout monitoring to be enabled in summary")
	}

	// Verify structure contains expected fields
	expectedFields := []string{"enabled", "overall_timeout_rate", "dial_timeouts", "tls_timeouts",
		"response_header_timeouts", "total_request_timeouts", "timeout_recoveries", "recovery_rate", "top_timeout_type"}

	for _, field := range expectedFields {
		if _, exists := timeoutMonitoring[field]; !exists {
			t.Errorf("Expected field %s to exist in timeout monitoring summary", field)
		}
	}
}

func TestTimeoutMonitoring_DisabledState(t *testing.T) {
	config := client.DefaultConfig()
	config.TimeoutMonitoring.Enabled = false

	httpClient := client.NewHTTPClient(config)

	// Try to get monitoring report when disabled
	report := httpClient.GetTimeoutMonitoringReport()

	if report["enabled"].(bool) {
		t.Error("Expected monitoring to be disabled")
	}

	if _, exists := report["message"]; !exists {
		t.Error("Expected disabled message in report")
	}

	// Verify metrics summary reflects disabled state
	summary := httpClient.GetMetricsSummary()
	timeoutMonitoring := summary["timeout_monitoring"].(map[string]interface{})

	if timeoutMonitoring["enabled"].(bool) {
		t.Error("Expected timeout monitoring to be disabled in summary")
	}
}

func TestTimeoutMonitoring_ReportStructure(t *testing.T) {
	config := client.DefaultConfig()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()

	httpClient := client.NewHTTPClient(config)

	// Get comprehensive monitoring report
	report := httpClient.GetTimeoutMonitoringReport()

	// Verify report structure
	if !report["enabled"].(bool) {
		t.Error("Expected monitoring to be enabled in report")
	}

	expectedSections := []string{"enabled", "total_requests", "timeout_breakdown", "timeout_rates",
		"timeout_distribution", "alert_status", "recovery_metrics", "monitoring_config"}

	for _, section := range expectedSections {
		if _, exists := report[section]; !exists {
			t.Errorf("Expected section %s to exist in monitoring report", section)
		}
	}

	// Verify monitoring config section structure
	monitoringConfig, ok := report["monitoring_config"].(map[string]interface{})
	if !ok {
		t.Error("Expected monitoring_config to be properly structured")
	}

	configFields := []string{"alert_thresholds", "distribution_buckets", "monitoring_window", "min_sample_size"}
	for _, field := range configFields {
		if _, exists := monitoringConfig[field]; !exists {
			t.Errorf("Expected field %s to exist in monitoring config", field)
		}
	}
}

func TestTimeoutMonitoring_BreakdownSummary(t *testing.T) {
	config := client.DefaultConfig()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()

	httpClient := client.NewHTTPClient(config)

	// Get timeout breakdown summary
	summary := httpClient.GetTimeoutBreakdownSummary()

	// Verify summary structure - when totalRequests is 0, it returns "timeout_rate"
	expectedFields := []string{"total_requests", "breakdown"}

	for _, field := range expectedFields {
		if _, exists := summary[field]; !exists {
			t.Errorf("Expected field %s to exist in breakdown summary", field)
		}
	}

	// Verify initial state
	if totalReqs, ok := summary["total_requests"].(int); !ok || totalReqs != 0 {
		t.Errorf("Expected 0 total requests initially, got %v", summary["total_requests"])
	}

	// Check timeout rate value (when totalRequests is 0, it should be "timeout_rate")
	if timeoutRate, ok := summary["timeout_rate"].(float64); !ok || timeoutRate != 0.0 {
		t.Errorf("Expected 0.0 timeout rate initially, got %v", summary["timeout_rate"])
	}

	// Check breakdown exists
	if _, exists := summary["breakdown"]; !exists {
		t.Error("Expected breakdown field to exist")
	}
}
