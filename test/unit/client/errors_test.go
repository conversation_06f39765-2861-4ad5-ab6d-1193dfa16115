package client

import (
	"fmt"
	"net"
	"net/http"
	"syscall"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"neuralmetergo/internal/client"
)

func TestEnhancedErrorCategorizer(t *testing.T) {
	categorizer := client.NewEnhancedErrorCategorizer()

	testCases := []struct {
		name           string
		errorGenerator func() (error, *http.Request, *http.Response)
		expectedType   client.EnhancedErrorType
		expectedMsg    string
	}{
		{
			name: "Network Timeout Error",
			errorGenerator: func() (error, *http.Request, *http.Response) {
				req, _ := http.NewRequest("GET", "http://example.com", nil)
				return &net.OpError{
					Op:   "dial",
					Net:  "tcp",
					Addr: &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 80},
					Err:  fmt.Errorf("i/o timeout"),
				}, req, nil
			},
			expectedType: client.EnhancedErrorTypeNetwork,
			expectedMsg:  "Persistent network error",
		},
		{
			name: "Connection Refused Error",
			errorGenerator: func() (error, *http.Request, *http.Response) {
				req, _ := http.NewRequest("GET", "http://localhost:9999", nil)
				return syscall.ECONNREFUSED, req, nil
			},
			expectedType: client.EnhancedErrorTypeConnection,
			expectedMsg:  "Connection refused",
		},
		{
			name: "DNS Resolution Error",
			errorGenerator: func() (error, *http.Request, *http.Response) {
				req, _ := http.NewRequest("GET", "http://nonexistent.invalid", nil)
				return &net.DNSError{
					Name: "nonexistent.invalid",
				}, req, nil
			},
			expectedType: client.EnhancedErrorTypeDNS,
			expectedMsg:  "DNS resolution failed",
		},
		{
			name: "Context Deadline Exceeded",
			errorGenerator: func() (error, *http.Request, *http.Response) {
				req, _ := http.NewRequest("GET", "http://example.com", nil)
				return fmt.Errorf("context deadline exceeded"), req, nil
			},
			expectedType: client.EnhancedErrorTypeTimeout,
			expectedMsg:  "Context deadline exceeded",
		},
		{
			name: "Context Cancelled",
			errorGenerator: func() (error, *http.Request, *http.Response) {
				req, _ := http.NewRequest("GET", "http://example.com", nil)
				return fmt.Errorf("context canceled"), req, nil
			},
			expectedType: client.EnhancedErrorTypeContext,
			expectedMsg:  "Operation cancelled",
		},
		{
			name: "HTTP Status Error",
			errorGenerator: func() (error, *http.Request, *http.Response) {
				req, _ := http.NewRequest("GET", "http://example.com", nil)
				resp := &http.Response{
					StatusCode: http.StatusInternalServerError,
				}
				return fmt.Errorf("server error"), req, resp
			},
			expectedType: client.EnhancedErrorTypeServerError,
			expectedMsg:  "HTTP error: server error",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err, req, resp := tc.errorGenerator()
			httpErr := categorizer.CategorizeError(err, req, resp, 100*time.Millisecond)

			require.NotNil(t, httpErr, "Error categorization should not return nil")
			assert.Equal(t, tc.expectedType, httpErr.GetType(), "Error type should match")
			assert.Contains(t, httpErr.Error(), tc.expectedMsg, "Error message should match")
		})
	}
}

func TestErrorStatisticsBasic(t *testing.T) {
	stats := client.NewErrorStatistics()

	// Simulate error tracking by manually updating statistics
	stats.TotalErrors++
	stats.ErrorsByType = map[client.EnhancedErrorType]int64{
		client.EnhancedErrorTypeNetwork: 2,
		client.EnhancedErrorTypeTimeout: 1,
	}
	stats.ErrorFrequency = map[string]int64{
		"Network error 1": 5,
		"Network error 2": 3,
		"Timeout error":   2,
	}

	t.Run("Error Rate Calculation", func(t *testing.T) {
		totalRequests := int64(10)
		stats.CalculateErrorRatePercentage(totalRequests)
		assert.Equal(t, 10.0, stats.ErrorRatePercentage, "Error rate should be calculated correctly")
	})

	t.Run("Most Frequent Error Message", func(t *testing.T) {
		mostFrequent := stats.GetMostFrequentErrorMessage()
		assert.Equal(t, "Network error 1", mostFrequent, "Most frequent error message should be correct")
	})

	t.Run("Error Type Distribution", func(t *testing.T) {
		stats.TotalErrors = 3
		distribution := stats.GetErrorTypeDistribution()
		assert.InDelta(t, 66.67, distribution[client.EnhancedErrorTypeNetwork], 0.01, "Network error distribution should be correct")
		assert.InDelta(t, 33.33, distribution[client.EnhancedErrorTypeTimeout], 0.01, "Timeout error distribution should be correct")
	})
}

func TestErrorRetriability(t *testing.T) {
	testCases := []struct {
		name              string
		errorGenerator    func() error
		expectedRetriable bool
	}{
		{
			name: "Retriable Network Error",
			errorGenerator: func() error {
				return &net.OpError{
					Op:   "dial",
					Net:  "tcp",
					Addr: &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 80},
					Err:  fmt.Errorf("temporary error"),
				}
			},
			expectedRetriable: true,
		},
		{
			name: "Non-Retriable Permanent Network Error",
			errorGenerator: func() error {
				return &net.OpError{
					Op:   "dial",
					Net:  "tcp",
					Addr: &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 80},
					Err:  fmt.Errorf("permanent error"),
				}
			},
			expectedRetriable: false,
		},
		{
			name: "Retriable DNS Error",
			errorGenerator: func() error {
				return &net.DNSError{
					Name: "temporary.error",
					Err:  "temporary resolution failure",
				}
			},
			expectedRetriable: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "http://example.com", nil)
			categorizer := client.NewEnhancedErrorCategorizer()
			httpErr := categorizer.CategorizeError(tc.errorGenerator(), req, nil, 100*time.Millisecond)

			require.NotNil(t, httpErr, "Error categorization should not return nil")
			assert.Equal(t, tc.expectedRetriable, httpErr.IsRetriable(), "Retriability should match expected")
		})
	}
}
