package client_test

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

func TestDefaultConfig(t *testing.T) {
	config := client.DefaultConfig()

	// Test connection pool settings
	if config.MaxIdleConns != 200 {
		t.<PERSON><PERSON><PERSON>("Expected MaxIdleConns to be 200, got %d", config.MaxIdleConns)
	}
	if config.MaxIdleConnsPerHost != 50 {
		t.<PERSON><PERSON><PERSON>("Expected MaxIdleConnsPerHost to be 50, got %d", config.MaxIdleConnsPerHost)
	}
	if config.MaxConnsPerHost != 100 {
		t.<PERSON><PERSON><PERSON>("Expected MaxConnsPerHost to be 100, got %d", config.MaxConnsPerHost)
	}
	if config.IdleConnTimeout != 90*time.Second {
		t.Errorf("Expected IdleConnTimeout to be 90s, got %v", config.IdleConnTimeout)
	}

	// Test timeout settings
	if config.DialTimeout != 10*time.Second {
		t.Errorf("Expected DialTimeout to be 10s, got %v", config.DialTimeout)
	}
	if config.ResponseHeaderTimeout != 30*time.Second {
		t.Errorf("Expected ResponseHeaderTimeout to be 30s, got %v", config.ResponseHeaderTimeout)
	}
	if config.TLSHandshakeTimeout != 10*time.Second {
		t.Errorf("Expected TLSHandshakeTimeout to be 10s, got %v", config.TLSHandshakeTimeout)
	}
	if config.ExpectContinueTimeout != 1*time.Second {
		t.Errorf("Expected ExpectContinueTimeout to be 1s, got %v", config.ExpectContinueTimeout)
	}

	// Test performance settings
	if config.DisableKeepAlives != false {
		t.Errorf("Expected DisableKeepAlives to be false, got %v", config.DisableKeepAlives)
	}
	if config.DisableCompression != false {
		t.Errorf("Expected DisableCompression to be false, got %v", config.DisableCompression)
	}

	// Test that default config is valid
	if err := config.Validate(); err != nil {
		t.Errorf("Default config should be valid, got error: %v", err)
	}
}

func TestHighThroughputConfig(t *testing.T) {
	config := client.HighThroughputConfig()

	// Test high throughput optimizations
	if config.MaxIdleConns != 500 {
		t.Errorf("Expected MaxIdleConns to be 500, got %d", config.MaxIdleConns)
	}
	if config.MaxIdleConnsPerHost != 100 {
		t.Errorf("Expected MaxIdleConnsPerHost to be 100, got %d", config.MaxIdleConnsPerHost)
	}
	if config.MaxConnsPerHost != 200 {
		t.Errorf("Expected MaxConnsPerHost to be 200, got %d", config.MaxConnsPerHost)
	}

	// Test aggressive timeout settings
	if config.DialTimeout != 5*time.Second {
		t.Errorf("Expected DialTimeout to be 5s, got %v", config.DialTimeout)
	}
	if config.ResponseHeaderTimeout != 15*time.Second {
		t.Errorf("Expected ResponseHeaderTimeout to be 15s, got %v", config.ResponseHeaderTimeout)
	}
	if config.TLSHandshakeTimeout != 5*time.Second {
		t.Errorf("Expected TLSHandshakeTimeout to be 5s, got %v", config.TLSHandshakeTimeout)
	}
	if config.ExpectContinueTimeout != 500*time.Millisecond {
		t.Errorf("Expected ExpectContinueTimeout to be 500ms, got %v", config.ExpectContinueTimeout)
	}

	// Test performance optimizations
	if config.DisableCompression != true {
		t.Errorf("Expected DisableCompression to be true for high throughput, got %v", config.DisableCompression)
	}

	// Test that high throughput config is valid
	if err := config.Validate(); err != nil {
		t.Errorf("High throughput config should be valid, got error: %v", err)
	}
}

func TestLowLatencyConfig(t *testing.T) {
	config := client.LowLatencyConfig()

	// Test low latency optimizations
	if config.MaxIdleConns != 100 {
		t.Errorf("Expected MaxIdleConns to be 100, got %d", config.MaxIdleConns)
	}
	if config.MaxIdleConnsPerHost != 20 {
		t.Errorf("Expected MaxIdleConnsPerHost to be 20, got %d", config.MaxIdleConnsPerHost)
	}
	if config.MaxConnsPerHost != 50 {
		t.Errorf("Expected MaxConnsPerHost to be 50, got %d", config.MaxConnsPerHost)
	}

	// Test aggressive timeout settings for low latency
	if config.DialTimeout != 3*time.Second {
		t.Errorf("Expected DialTimeout to be 3s, got %v", config.DialTimeout)
	}
	if config.ResponseHeaderTimeout != 10*time.Second {
		t.Errorf("Expected ResponseHeaderTimeout to be 10s, got %v", config.ResponseHeaderTimeout)
	}
	if config.TLSHandshakeTimeout != 3*time.Second {
		t.Errorf("Expected TLSHandshakeTimeout to be 3s, got %v", config.TLSHandshakeTimeout)
	}
	if config.ExpectContinueTimeout != 200*time.Millisecond {
		t.Errorf("Expected ExpectContinueTimeout to be 200ms, got %v", config.ExpectContinueTimeout)
	}

	// Test that low latency config is valid
	if err := config.Validate(); err != nil {
		t.Errorf("Low latency config should be valid, got error: %v", err)
	}
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name    string
		config  *client.Config
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid config",
			config: &client.Config{
				MaxIdleConns:          100,
				MaxIdleConnsPerHost:   10,
				MaxConnsPerHost:       50,
				IdleConnTimeout:       30 * time.Second,
				DialTimeout:           5 * time.Second,
				ResponseHeaderTimeout: 10 * time.Second,
				TLSHandshakeTimeout:   5 * time.Second,
				ExpectContinueTimeout: 1 * time.Second,
				TimeoutStrategy:       client.NewBalancedTimeoutStrategy(), // Add valid timeout strategy
			},
			wantErr: false,
		},
		{
			name: "negative MaxIdleConns",
			config: &client.Config{
				MaxIdleConns: -1,
			},
			wantErr: true,
			errMsg:  "MaxIdleConns must be non-negative, got -1",
		},
		{
			name: "negative MaxIdleConnsPerHost",
			config: &client.Config{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: -1,
			},
			wantErr: true,
			errMsg:  "MaxIdleConnsPerHost must be non-negative, got -1",
		},
		{
			name: "negative MaxConnsPerHost",
			config: &client.Config{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				MaxConnsPerHost:     -1,
			},
			wantErr: true,
			errMsg:  "MaxConnsPerHost must be non-negative, got -1",
		},
		{
			name: "MaxIdleConnsPerHost greater than MaxIdleConns",
			config: &client.Config{
				MaxIdleConns:        10,
				MaxIdleConnsPerHost: 20,
				MaxConnsPerHost:     50,
			},
			wantErr: true,
			errMsg:  "MaxIdleConnsPerHost (20) cannot be greater than MaxIdleConns (10)",
		},
		{
			name: "negative IdleConnTimeout",
			config: &client.Config{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				MaxConnsPerHost:     50,
				IdleConnTimeout:     -1 * time.Second,
			},
			wantErr: true,
			errMsg:  "IdleConnTimeout must be non-negative, got -1s",
		},
		{
			name: "negative DialTimeout",
			config: &client.Config{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				MaxConnsPerHost:     50,
				IdleConnTimeout:     30 * time.Second,
				DialTimeout:         -1 * time.Second,
			},
			wantErr: true,
			errMsg:  "DialTimeout must be non-negative, got -1s",
		},
		{
			name: "negative ResponseHeaderTimeout",
			config: &client.Config{
				MaxIdleConns:          100,
				MaxIdleConnsPerHost:   10,
				MaxConnsPerHost:       50,
				IdleConnTimeout:       30 * time.Second,
				DialTimeout:           5 * time.Second,
				ResponseHeaderTimeout: -1 * time.Second,
			},
			wantErr: true,
			errMsg:  "ResponseHeaderTimeout must be non-negative, got -1s",
		},
		{
			name: "negative TLSHandshakeTimeout",
			config: &client.Config{
				MaxIdleConns:          100,
				MaxIdleConnsPerHost:   10,
				MaxConnsPerHost:       50,
				IdleConnTimeout:       30 * time.Second,
				DialTimeout:           5 * time.Second,
				ResponseHeaderTimeout: 10 * time.Second,
				TLSHandshakeTimeout:   -1 * time.Second,
			},
			wantErr: true,
			errMsg:  "TLSHandshakeTimeout must be non-negative, got -1s",
		},
		{
			name: "negative ExpectContinueTimeout",
			config: &client.Config{
				MaxIdleConns:          100,
				MaxIdleConnsPerHost:   10,
				MaxConnsPerHost:       50,
				IdleConnTimeout:       30 * time.Second,
				DialTimeout:           5 * time.Second,
				ResponseHeaderTimeout: 10 * time.Second,
				TLSHandshakeTimeout:   5 * time.Second,
				ExpectContinueTimeout: -1 * time.Second,
			},
			wantErr: true,
			errMsg:  "ExpectContinueTimeout must be non-negative, got -1s",
		},
		{
			name: "zero MaxIdleConns with valid MaxIdleConnsPerHost",
			config: &client.Config{
				MaxIdleConns:        0,
				MaxIdleConnsPerHost: 10,
				MaxConnsPerHost:     50,
				TimeoutStrategy:     client.NewBalancedTimeoutStrategy(), // Add valid timeout strategy
			},
			wantErr: false, // Zero MaxIdleConns means unlimited, so this should be valid
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errMsg != "" && err.Error() != tt.errMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestNewHTTPClientWithConfig(t *testing.T) {
	config := client.DefaultConfig()
	httpClient := client.NewHTTPClient(config)

	if httpClient == nil {
		t.Fatal("NewHTTPClient returned nil")
	}

	// Note: Full testing of transport configuration will be done in subtask 32.2
	// This test just ensures the constructor works
}

func TestNewHTTPClientWithNilConfig(t *testing.T) {
	httpClient := client.NewHTTPClient(nil)

	if httpClient == nil {
		t.Fatal("NewHTTPClient with nil config returned nil")
	}

	// Should use default config when nil is passed
}

// New tests for connection pool initialization
func TestNewHTTPClientTransportConfiguration(t *testing.T) {
	config := &client.Config{
		MaxIdleConns:          200,              // Updated to match current defaults
		MaxIdleConnsPerHost:   50,               // Updated to match current defaults
		MaxConnsPerHost:       100,              // Updated to match current defaults
		IdleConnTimeout:       90 * time.Second, // Updated to match current defaults
		DialTimeout:           8 * time.Second,
		ResponseHeaderTimeout: 30 * time.Second, // Updated to match current defaults
		TLSHandshakeTimeout:   10 * time.Second, // Updated to match current defaults
		ExpectContinueTimeout: 1 * time.Second,  // Updated to match current defaults
		DisableKeepAlives:     false,
		DisableCompression:    false,                               // Updated to match current defaults
		TimeoutStrategy:       client.NewBalancedTimeoutStrategy(), // Add timeout strategy
	}

	httpClient := client.NewHTTPClient(config)

	if httpClient == nil {
		t.Fatal("NewHTTPClient returned nil")
	}

	// Get the underlying HTTP client
	underlyingClient := httpClient.GetClient()
	if underlyingClient == nil {
		t.Fatal("GetClient returned nil")
	}

	// Verify the transport configuration through the HTTPClient
	// Since the transport is wrapped, we verify the configuration was applied correctly
	// by checking that the HTTPClient was created with the expected config
	retrievedConfig := httpClient.GetConfig()

	// Test connection pool settings
	if retrievedConfig.MaxIdleConns != config.MaxIdleConns {
		t.Errorf("Expected MaxIdleConns to be %d, got %d", config.MaxIdleConns, retrievedConfig.MaxIdleConns)
	}
	if retrievedConfig.MaxIdleConnsPerHost != config.MaxIdleConnsPerHost {
		t.Errorf("Expected MaxIdleConnsPerHost to be %d, got %d", config.MaxIdleConnsPerHost, retrievedConfig.MaxIdleConnsPerHost)
	}
	if retrievedConfig.MaxConnsPerHost != config.MaxConnsPerHost {
		t.Errorf("Expected MaxConnsPerHost to be %d, got %d", config.MaxConnsPerHost, retrievedConfig.MaxConnsPerHost)
	}
	if retrievedConfig.IdleConnTimeout != config.IdleConnTimeout {
		t.Errorf("Expected IdleConnTimeout to be %v, got %v", config.IdleConnTimeout, retrievedConfig.IdleConnTimeout)
	}

	// Test timeout settings
	if retrievedConfig.ResponseHeaderTimeout != config.ResponseHeaderTimeout {
		t.Errorf("Expected ResponseHeaderTimeout to be %v, got %v", config.ResponseHeaderTimeout, retrievedConfig.ResponseHeaderTimeout)
	}
	if retrievedConfig.TLSHandshakeTimeout != config.TLSHandshakeTimeout {
		t.Errorf("Expected TLSHandshakeTimeout to be %v, got %v", config.TLSHandshakeTimeout, retrievedConfig.TLSHandshakeTimeout)
	}
	if retrievedConfig.ExpectContinueTimeout != config.ExpectContinueTimeout {
		t.Errorf("Expected ExpectContinueTimeout to be %v, got %v", config.ExpectContinueTimeout, retrievedConfig.ExpectContinueTimeout)
	}

	// Test performance settings
	if retrievedConfig.DisableKeepAlives != config.DisableKeepAlives {
		t.Errorf("Expected DisableKeepAlives to be %v, got %v", config.DisableKeepAlives, retrievedConfig.DisableKeepAlives)
	}
	if retrievedConfig.DisableCompression != config.DisableCompression {
		t.Errorf("Expected DisableCompression to be %v, got %v", config.DisableCompression, retrievedConfig.DisableCompression)
	}

	// Verify that the HTTP client was created successfully and can track statistics
	stats := httpClient.GetConnectionStats()
	if stats.TotalRequests != 0 {
		t.Error("Expected initial stats to show zero requests")
	}
}

func TestNewHTTPClientWithInvalidConfig(t *testing.T) {
	// Create an invalid config
	invalidConfig := &client.Config{
		MaxIdleConns:        -1, // Invalid negative value
		MaxIdleConnsPerHost: 10,
		MaxConnsPerHost:     50,
	}

	// Should fall back to default config when validation fails
	httpClient := client.NewHTTPClient(invalidConfig)

	if httpClient == nil {
		t.Fatal("NewHTTPClient returned nil")
	}

	// Verify it fell back to default config
	config := httpClient.GetConfig()
	defaultConfig := client.DefaultConfig()

	if config.MaxIdleConns != defaultConfig.MaxIdleConns {
		t.Errorf("Expected fallback to default MaxIdleConns %d, got %d", defaultConfig.MaxIdleConns, config.MaxIdleConns)
	}
}

func TestHTTPClientGetConfig(t *testing.T) {
	config := client.HighThroughputConfig()
	httpClient := client.NewHTTPClient(config)

	retrievedConfig := httpClient.GetConfig()
	if retrievedConfig != config {
		t.Error("GetConfig did not return the same config instance")
	}
}

func TestHTTPClientClose(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Test that Close doesn't panic
	httpClient.Close()

	// We can't easily test that connections are actually closed without
	// making real HTTP requests, but we can verify the method exists and runs
}

func TestConnectionPoolWithDifferentConfigs(t *testing.T) {
	configs := []*client.Config{
		client.DefaultConfig(),
		client.HighThroughputConfig(),
		client.LowLatencyConfig(),
	}

	for i, config := range configs {
		t.Run(fmt.Sprintf("config_%d", i), func(t *testing.T) {
			httpClient := client.NewHTTPClient(config)

			if httpClient == nil {
				t.Fatal("NewHTTPClient returned nil")
			}

			// Verify each config creates a properly configured client
			// Since the transport is wrapped, verify through the config
			retrievedConfig := httpClient.GetConfig()

			// Basic verification that the client has our settings
			if retrievedConfig.MaxIdleConns != config.MaxIdleConns {
				t.Errorf("MaxIdleConns mismatch: expected %d, got %d", config.MaxIdleConns, retrievedConfig.MaxIdleConns)
			}
			if retrievedConfig.MaxIdleConnsPerHost != config.MaxIdleConnsPerHost {
				t.Errorf("MaxIdleConnsPerHost mismatch: expected %d, got %d", config.MaxIdleConnsPerHost, retrievedConfig.MaxIdleConnsPerHost)
			}

			// Verify connection stats are available
			stats := httpClient.GetConnectionStats()
			if stats.TotalRequests != 0 {
				t.Error("Expected initial stats to show zero requests")
			}
		})
	}
}

func TestHTTPClientConcurrentCreation(t *testing.T) {
	// Test that creating multiple HTTP clients concurrently doesn't cause issues
	const numClients = 10
	clients := make([]*client.HTTPClient, numClients)

	// Create clients concurrently
	done := make(chan bool, numClients)
	for i := 0; i < numClients; i++ {
		go func(index int) {
			clients[index] = client.NewHTTPClient(client.DefaultConfig())
			done <- true
		}(i)
	}

	// Wait for all to complete
	for i := 0; i < numClients; i++ {
		<-done
	}

	// Verify all clients were created successfully
	for i, c := range clients {
		if c == nil {
			t.Errorf("Client %d was not created", i)
		}
	}
}

// New tests for connection reuse mechanism
func TestConnectionStats_Initial(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	stats := httpClient.GetConnectionStats()

	// All metrics should be zero initially
	if stats.TotalRequests != 0 {
		t.Errorf("Expected TotalRequests to be 0, got %d", stats.TotalRequests)
	}
	if stats.ConnectionsReused != 0 {
		t.Errorf("Expected ConnectionsReused to be 0, got %d", stats.ConnectionsReused)
	}
	if stats.NewConnections != 0 {
		t.Errorf("Expected NewConnections to be 0, got %d", stats.NewConnections)
	}
	if stats.ConnectionErrors != 0 {
		t.Errorf("Expected ConnectionErrors to be 0, got %d", stats.ConnectionErrors)
	}
}

func TestConnectionReuseRatio_ZeroRequests(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	ratio := httpClient.GetConnectionReuseRatio()
	if ratio != 0.0 {
		t.Errorf("Expected reuse ratio to be 0.0 with no requests, got %f", ratio)
	}
}

func TestHTTPClient_RequestTracking(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Make several requests to track metrics
	for i := 0; i < 5; i++ {
		req, err := http.NewRequest("GET", server.URL, nil)
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}

		resp, err := httpClient.GetClient().Do(req)
		if err != nil {
			t.Fatalf("Request %d failed: %v", i, err)
		}
		resp.Body.Close()
	}

	stats := httpClient.GetConnectionStats()

	// Should have tracked 5 requests
	if stats.TotalRequests != 5 {
		t.Errorf("Expected TotalRequests to be 5, got %d", stats.TotalRequests)
	}

	// Should have some connection activity (new + reused = total)
	totalConnections := stats.NewConnections + stats.ConnectionsReused
	if totalConnections != stats.TotalRequests {
		t.Errorf("Expected total connections (%d) to equal total requests (%d)", totalConnections, stats.TotalRequests)
	}

	// Should have at least one reused connection for multiple requests to same host
	if stats.TotalRequests > 1 && stats.ConnectionsReused == 0 {
		t.Error("Expected at least one connection to be reused for multiple requests to same host")
	}
}

func TestPrewarmConnections_ValidHosts(t *testing.T) {
	// Create test servers
	server1 := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer server1.Close()

	server2 := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer server2.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	hosts := []string{server1.URL, server2.URL}
	err := httpClient.PrewarmConnections(ctx, hosts, 2)

	if err != nil {
		t.Errorf("PrewarmConnections failed: %v", err)
	}

	stats := httpClient.GetConnectionStats()

	// Should have made prewarming requests
	if stats.TotalRequests == 0 {
		t.Error("Expected prewarming to make requests")
	}

	// Should have new connections from prewarming
	if stats.NewConnections == 0 {
		t.Error("Expected prewarming to create new connections")
	}
}

func TestPrewarmConnections_EmptyHosts(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	ctx := context.Background()
	err := httpClient.PrewarmConnections(ctx, []string{}, 1)

	if err == nil {
		t.Error("Expected error for empty hosts list")
	}

	if err.Error() != "no hosts provided for prewarming" {
		t.Errorf("Expected specific error message, got: %v", err)
	}
}

func TestPrewarmConnections_InvalidHost(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// Use an invalid host that will fail
	hosts := []string{"http://invalid-host-that-does-not-exist.local"}
	err := httpClient.PrewarmConnections(ctx, hosts, 1)

	if err == nil {
		t.Error("Expected error for invalid host")
	}

	stats := httpClient.GetConnectionStats()

	// Should have tracked connection errors
	if stats.ConnectionErrors == 0 {
		t.Error("Expected connection errors to be tracked for invalid host")
	}
}

func TestValidateConnectionHealth_Healthy(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Health check should pass for new client
	err := httpClient.ValidateConnectionHealth()
	if err != nil {
		t.Errorf("Expected healthy client to pass validation, got: %v", err)
	}
}

func TestValidateConnectionHealth_HighErrorRate(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Use reflection or direct access to simulate errors
	// For this test, we'll create a scenario with many requests and errors

	// Create a test server that returns errors
	errorServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	}))
	defer errorServer.Close()

	// Make many requests that will be tracked as errors due to 500 status
	// (Note: HTTP status errors are different from connection errors, but this simulates the scenario)
	for i := 0; i < 150; i++ {
		req, _ := http.NewRequest("GET", errorServer.URL, nil)
		resp, err := httpClient.GetClient().Do(req)
		if err == nil {
			resp.Body.Close()
		}
	}

	// The validation might not fail here since we're testing HTTP errors, not connection errors
	// But we can verify the stats are being tracked
	updatedStats := httpClient.GetConnectionStats()
	if updatedStats.TotalRequests < 150 {
		t.Errorf("Expected at least 150 requests to be tracked, got %d", updatedStats.TotalRequests)
	}
}

func TestFlushIdleConnections(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// Make a request to establish a connection
	req, _ := http.NewRequest("GET", server.URL, nil)
	resp, err := httpClient.GetClient().Do(req)
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}
	resp.Body.Close()

	// Flush idle connections
	httpClient.FlushIdleConnections()

	// Test that the method doesn't panic
	// The actual connection flushing is handled by Go's transport
}

func TestInstrumentedTransport_ErrorTracking(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Try to make a request to a non-existent server
	req, _ := http.NewRequest("GET", "http://localhost:99999", nil)
	_, err := httpClient.GetClient().Do(req)

	// Should get a connection error
	if err == nil {
		t.Error("Expected connection error for invalid port")
	}

	stats := httpClient.GetConnectionStats()

	// Should have tracked the error
	if stats.ConnectionErrors == 0 {
		t.Error("Expected connection error to be tracked")
	}

	if stats.TotalRequests == 0 {
		t.Error("Expected request to be counted even if it failed")
	}
}

func TestConnectionReuse_MultipleRequests(t *testing.T) {
	// Create a test server with a small delay to help distinguish new vs reused connections
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(1 * time.Millisecond) // Small delay
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Make multiple requests to the same host
	for i := 0; i < 10; i++ {
		req, _ := http.NewRequest("GET", server.URL, nil)
		resp, err := httpClient.GetClient().Do(req)
		if err != nil {
			t.Fatalf("Request %d failed: %v", i, err)
		}
		resp.Body.Close()

		// Small delay between requests to allow for connection reuse
		time.Sleep(1 * time.Millisecond)
	}

	stats := httpClient.GetConnectionStats()

	// Should have made 10 requests
	if stats.TotalRequests != 10 {
		t.Errorf("Expected 10 requests, got %d", stats.TotalRequests)
	}

	// Should have reused connections (at least some)
	if stats.ConnectionsReused == 0 {
		t.Error("Expected some connections to be reused")
	}

	// Calculate and verify reuse ratio
	reuseRatio := httpClient.GetConnectionReuseRatio()
	if reuseRatio <= 0 {
		t.Errorf("Expected positive reuse ratio, got %f", reuseRatio)
	}

	// Should have reasonable timing metrics
	if stats.AvgConnectionTime == 0 && stats.NewConnections > 0 {
		t.Error("Expected average connection time to be set when new connections are made")
	}

	if stats.AvgReuseTime == 0 && stats.ConnectionsReused > 0 {
		t.Error("Expected average reuse time to be set when connections are reused")
	}
}

// HTTP Methods Tests

func TestHTTPClient_Get(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			t.Errorf("Expected GET method, got %s", r.Method)
		}

		// Check custom header
		if r.Header.Get("X-Test-Header") != "test-value" {
			t.Errorf("Expected custom header, got %s", r.Header.Get("X-Test-Header"))
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message": "success"}`))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	headers := map[string]string{
		"X-Test-Header": "test-value",
	}

	resp, err := httpClient.Get(ctx, server.URL, headers)

	if err != nil {
		t.Fatalf("GET request failed: %v", err)
	}

	if resp.StatusCode != 200 {
		t.Errorf("Expected status code 200, got %d", resp.StatusCode)
	}

	if string(resp.Body) != `{"message": "success"}` {
		t.Errorf("Unexpected response body: %s", string(resp.Body))
	}

	if resp.Headers["Content-Type"] != "application/json" {
		t.Errorf("Expected Content-Type header, got %s", resp.Headers["Content-Type"])
	}

	if resp.Duration == 0 {
		t.Error("Expected duration to be tracked")
	}

	if resp.Timestamp.IsZero() {
		t.Error("Expected timestamp to be set")
	}
}

func TestHTTPClient_Post(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			t.Errorf("Expected POST method, got %s", r.Method)
		}

		// Read and verify body
		body, err := io.ReadAll(r.Body)
		if err != nil {
			t.Fatalf("Failed to read request body: %v", err)
		}

		expectedBody := `{"name": "test"}`
		if string(body) != expectedBody {
			t.Errorf("Expected body %s, got %s", expectedBody, string(body))
		}

		// Check Content-Type header (should be set automatically)
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("Expected Content-Type to be set automatically, got %s", r.Header.Get("Content-Type"))
		}

		w.WriteHeader(http.StatusCreated)
		w.Write([]byte(`{"id": 123}`))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	requestBody := []byte(`{"name": "test"}`)
	headers := map[string]string{
		"Authorization": "Bearer token123",
	}

	resp, err := httpClient.Post(ctx, server.URL, requestBody, headers)

	if err != nil {
		t.Fatalf("POST request failed: %v", err)
	}

	if resp.StatusCode != 201 {
		t.Errorf("Expected status code 201, got %d", resp.StatusCode)
	}

	if string(resp.Body) != `{"id": 123}` {
		t.Errorf("Unexpected response body: %s", string(resp.Body))
	}
}

func TestHTTPClient_Put(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "PUT" {
			t.Errorf("Expected PUT method, got %s", r.Method)
		}

		// Read and verify body
		body, err := io.ReadAll(r.Body)
		if err != nil {
			t.Fatalf("Failed to read request body: %v", err)
		}

		expectedBody := `{"name": "updated"}`
		if string(body) != expectedBody {
			t.Errorf("Expected body %s, got %s", expectedBody, string(body))
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"updated": true}`))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	requestBody := []byte(`{"name": "updated"}`)
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	resp, err := httpClient.Put(ctx, server.URL, requestBody, headers)

	if err != nil {
		t.Fatalf("PUT request failed: %v", err)
	}

	if resp.StatusCode != 200 {
		t.Errorf("Expected status code 200, got %d", resp.StatusCode)
	}

	if string(resp.Body) != `{"updated": true}` {
		t.Errorf("Unexpected response body: %s", string(resp.Body))
	}
}

func TestHTTPClient_Delete(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "DELETE" {
			t.Errorf("Expected DELETE method, got %s", r.Method)
		}

		w.WriteHeader(http.StatusNoContent)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	headers := map[string]string{
		"Authorization": "Bearer token123",
	}

	resp, err := httpClient.Delete(ctx, server.URL, headers)

	if err != nil {
		t.Fatalf("DELETE request failed: %v", err)
	}

	if resp.StatusCode != 204 {
		t.Errorf("Expected status code 204, got %d", resp.StatusCode)
	}

	if len(resp.Body) != 0 {
		t.Errorf("Expected empty body, got %s", string(resp.Body))
	}
}

func TestHTTPClient_Head(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "HEAD" {
			t.Errorf("Expected HEAD method, got %s", r.Method)
		}

		w.Header().Set("Content-Length", "100")
		w.Header().Set("Content-Type", "text/plain")
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	resp, err := httpClient.Head(ctx, server.URL, nil)

	if err != nil {
		t.Fatalf("HEAD request failed: %v", err)
	}

	if resp.StatusCode != 200 {
		t.Errorf("Expected status code 200, got %d", resp.StatusCode)
	}

	if resp.Headers["Content-Type"] != "text/plain" {
		t.Errorf("Expected Content-Type header, got %s", resp.Headers["Content-Type"])
	}

	// HEAD requests should have empty body
	if len(resp.Body) != 0 {
		t.Errorf("Expected empty body for HEAD request, got %s", string(resp.Body))
	}
}

func TestHTTPClient_Options(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "OPTIONS" {
			t.Errorf("Expected OPTIONS method, got %s", r.Method)
		}

		w.Header().Set("Allow", "GET, POST, PUT, DELETE, OPTIONS")
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	resp, err := httpClient.Options(ctx, server.URL, nil)

	if err != nil {
		t.Fatalf("OPTIONS request failed: %v", err)
	}

	if resp.StatusCode != 200 {
		t.Errorf("Expected status code 200, got %d", resp.StatusCode)
	}

	if resp.Headers["Allow"] != "GET, POST, PUT, DELETE, OPTIONS" {
		t.Errorf("Expected Allow header, got %s", resp.Headers["Allow"])
	}
}

func TestHTTPClient_Patch(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "PATCH" {
			t.Errorf("Expected PATCH method, got %s", r.Method)
		}

		// Read and verify body
		body, err := io.ReadAll(r.Body)
		if err != nil {
			t.Fatalf("Failed to read request body: %v", err)
		}

		expectedBody := `{"name": "patched"}`
		if string(body) != expectedBody {
			t.Errorf("Expected body %s, got %s", expectedBody, string(body))
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"patched": true}`))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	requestBody := []byte(`{"name": "patched"}`)
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	resp, err := httpClient.Patch(ctx, server.URL, requestBody, headers)

	if err != nil {
		t.Fatalf("PATCH request failed: %v", err)
	}

	if resp.StatusCode != 200 {
		t.Errorf("Expected status code 200, got %d", resp.StatusCode)
	}

	if string(resp.Body) != `{"patched": true}` {
		t.Errorf("Unexpected response body: %s", string(resp.Body))
	}
}

func TestHTTPClient_Execute_UserAgent(t *testing.T) {
	// Create test server that checks User-Agent
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		userAgent := r.Header.Get("User-Agent")
		if userAgent != "NeuralMeterGo/1.0" {
			t.Errorf("Expected User-Agent 'NeuralMeterGo/1.0', got '%s'", userAgent)
		}
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    server.URL,
	}

	_, err := httpClient.Execute(ctx, req)
	if err != nil {
		t.Fatalf("Execute request failed: %v", err)
	}
}

func TestHTTPClient_Execute_CustomUserAgent(t *testing.T) {
	// Create test server that checks User-Agent
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		userAgent := r.Header.Get("User-Agent")
		if userAgent != "CustomAgent/2.0" {
			t.Errorf("Expected User-Agent 'CustomAgent/2.0', got '%s'", userAgent)
		}
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    server.URL,
		Headers: map[string]string{
			"User-Agent": "CustomAgent/2.0",
		},
	}

	_, err := httpClient.Execute(ctx, req)
	if err != nil {
		t.Fatalf("Execute request failed: %v", err)
	}
}

func TestHTTPClient_Execute_InvalidURL(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    "://invalid-url", // Invalid URL
	}

	_, err := httpClient.Execute(ctx, req)

	if err == nil {
		t.Error("Expected error for invalid URL")
	}

	// Check for the updated error message format
	if !strings.Contains(err.Error(), "Failed to create HTTP request") {
		t.Errorf("Expected specific error message, got: %v", err)
	}
}

func TestHTTPClient_Execute_NetworkError(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    "http://localhost:99999", // Invalid port - should be non-retryable
	}

	_, err := httpClient.Execute(ctx, req)

	if err == nil {
		t.Error("Expected network error")
	}

	// Check for the updated error message format
	// Invalid port should be non-retryable and return "Network error"
	if !strings.Contains(err.Error(), "Network error") {
		t.Errorf("Expected specific error message, got: %v", err)
	}
}

func TestHTTPClient_Execute_ContextCancellation(t *testing.T) {
	// Create a server that responds slowly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(200 * time.Millisecond) // Delay to ensure context cancellation
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Create context that will be cancelled quickly
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	_, err := httpClient.Get(ctx, server.URL, nil)

	if err == nil {
		t.Error("Expected error due to context cancellation")
	}

	// The error could be either context cancellation during request or during retry delay
	if !strings.Contains(err.Error(), "context deadline exceeded") &&
		!strings.Contains(err.Error(), "Context cancelled during retry delay") {
		t.Errorf("Expected context cancellation error, got: %v", err)
	}
}

func TestHTTPClient_Execute_ResponseBodyReadError(t *testing.T) {
	// This test is challenging to implement without mocking the response body
	// We'll test a successful case to ensure body reading works correctly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    server.URL,
	}

	resp, err := httpClient.Execute(ctx, req)
	if err != nil {
		t.Fatalf("Execute request failed: %v", err)
	}

	if string(resp.Body) != "test response" {
		t.Errorf("Expected 'test response', got '%s'", string(resp.Body))
	}
}

func TestHTTPClient_Execute_MultipleHeaders(t *testing.T) {
	// Create test server that checks multiple headers
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("X-Custom-1") != "value1" {
			t.Errorf("Expected X-Custom-1 header 'value1', got '%s'", r.Header.Get("X-Custom-1"))
		}
		if r.Header.Get("X-Custom-2") != "value2" {
			t.Errorf("Expected X-Custom-2 header 'value2', got '%s'", r.Header.Get("X-Custom-2"))
		}

		// Set multiple response headers
		w.Header().Set("X-Response-1", "resp1")
		w.Header().Set("X-Response-2", "resp2")
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    server.URL,
		Headers: map[string]string{
			"X-Custom-1": "value1",
			"X-Custom-2": "value2",
		},
	}

	resp, err := httpClient.Execute(ctx, req)
	if err != nil {
		t.Fatalf("Execute request failed: %v", err)
	}

	if resp.Headers["X-Response-1"] != "resp1" {
		t.Errorf("Expected X-Response-1 header 'resp1', got '%s'", resp.Headers["X-Response-1"])
	}

	if resp.Headers["X-Response-2"] != "resp2" {
		t.Errorf("Expected X-Response-2 header 'resp2', got '%s'", resp.Headers["X-Response-2"])
	}
}

func TestHTTPClient_Execute_ContentLength(t *testing.T) {
	// Create test server with known content length
	responseBody := "This is a test response with known length"
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Length", fmt.Sprintf("%d", len(responseBody)))
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(responseBody))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    server.URL,
	}

	resp, err := httpClient.Execute(ctx, req)
	if err != nil {
		t.Fatalf("Execute request failed: %v", err)
	}

	expectedLength := int64(len(responseBody))
	if resp.ContentLength != expectedLength {
		t.Errorf("Expected content length %d, got %d", expectedLength, resp.ContentLength)
	}

	if string(resp.Body) != responseBody {
		t.Errorf("Expected body '%s', got '%s'", responseBody, string(resp.Body))
	}
}

func TestRequest_JSONSerialization(t *testing.T) {
	req := &client.Request{
		Method:  "POST",
		URL:     "https://example.com/api",
		Headers: map[string]string{"Content-Type": "application/json"},
		Body:    []byte(`{"test": true}`),
		Timeout: 30 * time.Second,
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(req)
	if err != nil {
		t.Fatalf("Failed to marshal Request to JSON: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled client.Request
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal Request from JSON: %v", err)
	}

	if unmarshaled.Method != req.Method {
		t.Errorf("Expected method %s, got %s", req.Method, unmarshaled.Method)
	}

	if unmarshaled.URL != req.URL {
		t.Errorf("Expected URL %s, got %s", req.URL, unmarshaled.URL)
	}

	if unmarshaled.Timeout != req.Timeout {
		t.Errorf("Expected timeout %v, got %v", req.Timeout, unmarshaled.Timeout)
	}
}

func TestResponse_JSONSerialization(t *testing.T) {
	resp := &client.Response{
		StatusCode:    200,
		Status:        "200 OK",
		Headers:       map[string]string{"Content-Type": "application/json"},
		Body:          []byte(`{"success": true}`),
		ContentLength: 17,
		Duration:      100 * time.Millisecond,
		Timestamp:     time.Now(),
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(resp)
	if err != nil {
		t.Fatalf("Failed to marshal Response to JSON: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled client.Response
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal Response from JSON: %v", err)
	}

	if unmarshaled.StatusCode != resp.StatusCode {
		t.Errorf("Expected status code %d, got %d", resp.StatusCode, unmarshaled.StatusCode)
	}

	if unmarshaled.Status != resp.Status {
		t.Errorf("Expected status %s, got %s", resp.Status, unmarshaled.Status)
	}

	if unmarshaled.ContentLength != resp.ContentLength {
		t.Errorf("Expected content length %d, got %d", resp.ContentLength, unmarshaled.ContentLength)
	}

	if string(unmarshaled.Body) != string(resp.Body) {
		t.Errorf("Expected body %s, got %s", string(resp.Body), string(unmarshaled.Body))
	}
}

// Error Handling and Retry Tests

func TestHTTPError_Error(t *testing.T) {
	// Test with status code
	err1 := &client.HTTPError{
		StatusCode: 500,
		Message:    "Internal Server Error",
	}
	expected1 := "HTTP 500: Internal Server Error"
	if err1.Error() != expected1 {
		t.Errorf("Expected %s, got %s", expected1, err1.Error())
	}

	// Test without status code
	err2 := &client.HTTPError{
		Message: "Network error",
	}
	expected2 := "Network error"
	if err2.Error() != expected2 {
		t.Errorf("Expected %s, got %s", expected2, err2.Error())
	}
}

func TestHTTPError_Unwrap(t *testing.T) {
	underlying := fmt.Errorf("underlying error")
	httpErr := &client.HTTPError{
		Message:    "HTTP error",
		Underlying: underlying,
	}

	unwrapped := httpErr.Unwrap()
	if unwrapped != underlying {
		t.Errorf("Expected underlying error, got %v", unwrapped)
	}
}

func TestDefaultRetryConfig(t *testing.T) {
	config := client.DefaultRetryConfig()

	if config.MaxRetries != 3 {
		t.Errorf("Expected MaxRetries 3, got %d", config.MaxRetries)
	}

	if config.InitialDelay != 100*time.Millisecond {
		t.Errorf("Expected InitialDelay 100ms, got %v", config.InitialDelay)
	}

	if config.BackoffFactor != 2.0 {
		t.Errorf("Expected BackoffFactor 2.0, got %f", config.BackoffFactor)
	}

	if !config.Jitter {
		t.Error("Expected Jitter to be true")
	}

	expectedRetryableErrors := []int{408, 429, 500, 502, 503, 504}
	if len(config.RetryableErrors) != len(expectedRetryableErrors) {
		t.Errorf("Expected %d retryable errors, got %d", len(expectedRetryableErrors), len(config.RetryableErrors))
	}
}

func TestAggressiveRetryConfig(t *testing.T) {
	config := client.AggressiveRetryConfig()

	if config.MaxRetries != 5 {
		t.Errorf("Expected MaxRetries 5, got %d", config.MaxRetries)
	}

	if config.InitialDelay != 50*time.Millisecond {
		t.Errorf("Expected InitialDelay 50ms, got %v", config.InitialDelay)
	}

	if config.BackoffFactor != 1.5 {
		t.Errorf("Expected BackoffFactor 1.5, got %f", config.BackoffFactor)
	}
}

func TestConservativeRetryConfig(t *testing.T) {
	config := client.ConservativeRetryConfig()

	if config.MaxRetries != 2 {
		t.Errorf("Expected MaxRetries 2, got %d", config.MaxRetries)
	}

	if config.InitialDelay != 200*time.Millisecond {
		t.Errorf("Expected InitialDelay 200ms, got %v", config.InitialDelay)
	}

	if config.Jitter {
		t.Error("Expected Jitter to be false")
	}
}

func TestHTTPClient_RetryConfiguration(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Test default retry config
	defaultConfig := httpClient.GetRetryConfig()
	if defaultConfig.MaxRetries != 3 {
		t.Errorf("Expected default MaxRetries 3, got %d", defaultConfig.MaxRetries)
	}

	// Test setting custom retry config
	customConfig := &client.RetryConfig{
		MaxRetries:   5,
		InitialDelay: 200 * time.Millisecond,
	}
	httpClient.SetRetryConfig(customConfig)

	retrievedConfig := httpClient.GetRetryConfig()
	if retrievedConfig.MaxRetries != 5 {
		t.Errorf("Expected custom MaxRetries 5, got %d", retrievedConfig.MaxRetries)
	}

	// Test disable retries
	httpClient.DisableRetries()
	disabledConfig := httpClient.GetRetryConfig()
	if disabledConfig.MaxRetries != 0 {
		t.Errorf("Expected MaxRetries 0 after disable, got %d", disabledConfig.MaxRetries)
	}

	// Test enable aggressive retries
	httpClient.EnableAggressiveRetries()
	aggressiveConfig := httpClient.GetRetryConfig()
	if aggressiveConfig.MaxRetries != 5 {
		t.Errorf("Expected aggressive MaxRetries 5, got %d", aggressiveConfig.MaxRetries)
	}

	// Test enable conservative retries
	httpClient.EnableConservativeRetries()
	conservativeConfig := httpClient.GetRetryConfig()
	if conservativeConfig.MaxRetries != 2 {
		t.Errorf("Expected conservative MaxRetries 2, got %d", conservativeConfig.MaxRetries)
	}
}

func TestNewHTTPClientWithRetry(t *testing.T) {
	customRetryConfig := &client.RetryConfig{
		MaxRetries:   10,
		InitialDelay: 500 * time.Millisecond,
	}

	httpClient := client.NewHTTPClientWithRetry(client.DefaultConfig(), customRetryConfig)

	retrievedConfig := httpClient.GetRetryConfig()
	if retrievedConfig.MaxRetries != 10 {
		t.Errorf("Expected MaxRetries 10, got %d", retrievedConfig.MaxRetries)
	}

	if retrievedConfig.InitialDelay != 500*time.Millisecond {
		t.Errorf("Expected InitialDelay 500ms, got %v", retrievedConfig.InitialDelay)
	}
}

func TestHTTPClient_RetryOnServerError(t *testing.T) {
	attempts := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attempts++
		if attempts < 3 {
			w.WriteHeader(http.StatusInternalServerError)
		} else {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("success"))
		}
	}))
	defer server.Close()

	// Create client with retry enabled
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	resp, err := httpClient.Get(ctx, server.URL, nil)

	if err != nil {
		t.Fatalf("Expected success after retries, got error: %v", err)
	}

	if resp.StatusCode != 200 {
		t.Errorf("Expected status code 200, got %d", resp.StatusCode)
	}

	if string(resp.Body) != "success" {
		t.Errorf("Expected 'success', got '%s'", string(resp.Body))
	}

	// Should have made 3 attempts
	if attempts != 3 {
		t.Errorf("Expected 3 attempts, got %d", attempts)
	}
}

func TestHTTPClient_RetryExhaustion(t *testing.T) {
	attempts := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attempts++
		w.WriteHeader(http.StatusInternalServerError)
	}))
	defer server.Close()

	// Create client with limited retries
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.SetRetryConfig(&client.RetryConfig{
		MaxRetries:      2,
		InitialDelay:    10 * time.Millisecond,
		MaxDelay:        100 * time.Millisecond,
		BackoffFactor:   2.0,
		Jitter:          false,
		RetryableErrors: []int{500},
	})

	ctx := context.Background()

	resp, err := httpClient.Get(ctx, server.URL, nil)

	// Should get an error after exhausting retries
	if err == nil {
		t.Error("Expected error after exhausting retries")
	}

	// Should have made 3 attempts (initial + 2 retries)
	if attempts != 3 {
		t.Errorf("Expected 3 attempts, got %d", attempts)
	}

	// Response should still be available
	if resp == nil {
		t.Error("Expected response even with error")
	}

	if resp.StatusCode != 500 {
		t.Errorf("Expected status code 500, got %d", resp.StatusCode)
	}
}

func TestHTTPClient_NoRetryOnNonRetryableError(t *testing.T) {
	attempts := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attempts++
		w.WriteHeader(http.StatusBadRequest) // 400 is not retryable by default
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	resp, err := httpClient.Get(ctx, server.URL, nil)

	// Should get an error immediately
	if err == nil {
		t.Error("Expected error for non-retryable status")
	}

	// Should have made only 1 attempt
	if attempts != 1 {
		t.Errorf("Expected 1 attempt for non-retryable error, got %d", attempts)
	}

	if resp.StatusCode != 400 {
		t.Errorf("Expected status code 400, got %d", resp.StatusCode)
	}
}

func TestHTTPClient_RetryWithContextCancellation(t *testing.T) {
	attempts := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attempts++
		w.WriteHeader(http.StatusInternalServerError)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.SetRetryConfig(&client.RetryConfig{
		MaxRetries:      5,
		InitialDelay:    100 * time.Millisecond,
		MaxDelay:        1 * time.Second,
		BackoffFactor:   2.0,
		Jitter:          false,
		RetryableErrors: []int{500},
	})

	// Create context that will be cancelled quickly
	ctx, cancel := context.WithTimeout(context.Background(), 150*time.Millisecond)
	defer cancel()

	_, err := httpClient.Get(ctx, server.URL, nil)

	// Should get a context cancellation error
	if err == nil {
		t.Error("Expected error due to context cancellation")
	}

	// Should have made at least 1 attempt but not all 6 (initial + 5 retries)
	if attempts < 1 || attempts > 3 {
		t.Errorf("Expected 1-3 attempts with context cancellation, got %d", attempts)
	}
}

func TestHTTPClient_ExecuteWithoutRetry(t *testing.T) {
	attempts := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attempts++
		w.WriteHeader(http.StatusInternalServerError)
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    server.URL,
	}

	_, err := httpClient.ExecuteWithoutRetry(ctx, req)

	// Should get an error immediately without retries
	if err == nil {
		t.Error("Expected error without retries")
	}

	// Should have made only 1 attempt
	if attempts != 1 {
		t.Errorf("Expected 1 attempt without retries, got %d", attempts)
	}
}

func TestHTTPClient_RetryDelayCalculation(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.SetRetryConfig(&client.RetryConfig{
		MaxRetries:      3,
		InitialDelay:    100 * time.Millisecond,
		MaxDelay:        1 * time.Second,
		BackoffFactor:   2.0,
		Jitter:          false,
		RetryableErrors: []int{500},
	})

	// Test delay calculation for different attempts
	delay0 := httpClient.GetRetryConfig().InitialDelay
	if delay0 != 100*time.Millisecond {
		t.Errorf("Expected initial delay 100ms, got %v", delay0)
	}

	// We can't directly test the calculateRetryDelay method as it's not exported,
	// but we can test the behavior through actual retries with timing
}

func TestHTTPClient_RetryWithNetworkError(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.SetRetryConfig(&client.RetryConfig{
		MaxRetries:    2,
		InitialDelay:  10 * time.Millisecond,
		MaxDelay:      100 * time.Millisecond,
		BackoffFactor: 2.0,
		Jitter:        false,
	})

	ctx := context.Background()

	// Try to connect to a non-existent server with invalid port
	_, err := httpClient.Get(ctx, "http://localhost:99999", nil)

	// Should get a network error immediately (invalid port is non-retryable)
	if err == nil {
		t.Error("Expected network error")
	}

	// Should be the original network error, not a retry exhaustion error
	// because "invalid port" is a non-retryable configuration error
	errStr := err.Error()
	if !strings.Contains(errStr, "Network error") {
		t.Errorf("Expected original network error with 'Network error', got: %v", err)
	}

	// Should NOT contain retry exhaustion message since it's non-retryable
	if strings.Contains(errStr, "Request failed after") {
		t.Errorf("Expected immediate failure for non-retryable error, but got retry exhaustion: %v", err)
	}
}

func TestHTTPClient_SuccessfulRetryTiming(t *testing.T) {
	attempts := 0
	start := time.Now()

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attempts++
		if attempts < 3 {
			w.WriteHeader(http.StatusInternalServerError)
		} else {
			w.WriteHeader(http.StatusOK)
		}
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.SetRetryConfig(&client.RetryConfig{
		MaxRetries:      5,
		InitialDelay:    50 * time.Millisecond,
		MaxDelay:        500 * time.Millisecond,
		BackoffFactor:   2.0,
		Jitter:          false,
		RetryableErrors: []int{500},
	})

	ctx := context.Background()

	_, err := httpClient.Get(ctx, server.URL, nil)

	elapsed := time.Since(start)

	if err != nil {
		t.Errorf("Expected success after retries, got error: %v", err)
	}

	// Should have taken at least the retry delays
	// First retry: 50ms, Second retry: 100ms = 150ms minimum
	if elapsed < 150*time.Millisecond {
		t.Errorf("Expected at least 150ms for retries, got %v", elapsed)
	}

	// But not too long (should succeed on 3rd attempt)
	if elapsed > 1*time.Second {
		t.Errorf("Expected less than 1s for successful retry, got %v", elapsed)
	}
}

// HTTP Method Metrics Tests

func TestHTTPMethodStats_UpdateMethodCount(t *testing.T) {
	stats := &client.HTTPMethodStats{}

	// Test all HTTP methods
	stats.UpdateMethodCount("GET")
	stats.UpdateMethodCount("POST")
	stats.UpdateMethodCount("PUT")
	stats.UpdateMethodCount("DELETE")
	stats.UpdateMethodCount("HEAD")
	stats.UpdateMethodCount("OPTIONS")
	stats.UpdateMethodCount("PATCH")
	stats.UpdateMethodCount("CUSTOM")

	// Verify counts
	if stats.GetRequests != 1 {
		t.Errorf("Expected 1 GET request, got %d", stats.GetRequests)
	}
	if stats.PostRequests != 1 {
		t.Errorf("Expected 1 POST request, got %d", stats.PostRequests)
	}
	if stats.PutRequests != 1 {
		t.Errorf("Expected 1 PUT request, got %d", stats.PutRequests)
	}
	if stats.DeleteRequests != 1 {
		t.Errorf("Expected 1 DELETE request, got %d", stats.DeleteRequests)
	}
	if stats.HeadRequests != 1 {
		t.Errorf("Expected 1 HEAD request, got %d", stats.HeadRequests)
	}
	if stats.OptionsRequests != 1 {
		t.Errorf("Expected 1 OPTIONS request, got %d", stats.OptionsRequests)
	}
	if stats.PatchRequests != 1 {
		t.Errorf("Expected 1 PATCH request, got %d", stats.PatchRequests)
	}
	if stats.OtherRequests != 1 {
		t.Errorf("Expected 1 other request, got %d", stats.OtherRequests)
	}

	// Test case insensitivity
	stats.UpdateMethodCount("get")
	if stats.GetRequests != 2 {
		t.Errorf("Expected 2 GET requests after lowercase, got %d", stats.GetRequests)
	}
}

func TestHTTPMethodStats_UpdateStatusCode(t *testing.T) {
	stats := &client.HTTPMethodStats{}

	// Test all status code categories
	stats.UpdateStatusCode(200) // 2xx
	stats.UpdateStatusCode(201) // 2xx
	stats.UpdateStatusCode(301) // 3xx
	stats.UpdateStatusCode(404) // 4xx
	stats.UpdateStatusCode(500) // 5xx

	if stats.Status2xx != 2 {
		t.Errorf("Expected 2 2xx responses, got %d", stats.Status2xx)
	}
	if stats.Status3xx != 1 {
		t.Errorf("Expected 1 3xx response, got %d", stats.Status3xx)
	}
	if stats.Status4xx != 1 {
		t.Errorf("Expected 1 4xx response, got %d", stats.Status4xx)
	}
	if stats.Status5xx != 1 {
		t.Errorf("Expected 1 5xx response, got %d", stats.Status5xx)
	}
}

func TestHTTPMethodStats_UpdateResponseTime(t *testing.T) {
	stats := &client.HTTPMethodStats{}

	// Add some requests first
	stats.UpdateMethodCount("GET")
	stats.UpdateMethodCount("GET")
	stats.UpdateMethodCount("GET")

	// Test response time updates
	stats.UpdateResponseTime(100 * time.Millisecond)
	stats.UpdateResponseTime(200 * time.Millisecond)
	stats.UpdateResponseTime(50 * time.Millisecond)

	if stats.MinResponseTime != 50*time.Millisecond {
		t.Errorf("Expected min response time 50ms, got %v", stats.MinResponseTime)
	}
	if stats.MaxResponseTime != 200*time.Millisecond {
		t.Errorf("Expected max response time 200ms, got %v", stats.MaxResponseTime)
	}

	expectedAvg := (100 + 200 + 50) * time.Millisecond / 3
	if stats.AvgResponseTime != expectedAvg {
		t.Errorf("Expected avg response time %v, got %v", expectedAvg, stats.AvgResponseTime)
	}
}

func TestHTTPMethodStats_GetSuccessRate(t *testing.T) {
	stats := &client.HTTPMethodStats{}

	// Test with no responses
	if rate := stats.GetSuccessRate(); rate != 0.0 {
		t.Errorf("Expected 0%% success rate with no responses, got %.2f%%", rate)
	}

	// Add some responses
	stats.UpdateStatusCode(200) // Success
	stats.UpdateStatusCode(201) // Success
	stats.UpdateStatusCode(404) // Error
	stats.UpdateStatusCode(500) // Error

	expectedRate := (2.0 / 4.0) * 100.0 // 50%
	if rate := stats.GetSuccessRate(); rate != expectedRate {
		t.Errorf("Expected %.2f%% success rate, got %.2f%%", expectedRate, rate)
	}
}

func TestHTTPMethodStats_GetErrorRate(t *testing.T) {
	stats := &client.HTTPMethodStats{}

	// Add some responses
	stats.UpdateStatusCode(200) // Success
	stats.UpdateStatusCode(301) // Redirect
	stats.UpdateStatusCode(404) // Error
	stats.UpdateStatusCode(500) // Error

	expectedRate := (2.0 / 4.0) * 100.0 // 50%
	if rate := stats.GetErrorRate(); rate != expectedRate {
		t.Errorf("Expected %.2f%% error rate, got %.2f%%", expectedRate, rate)
	}
}

func TestHTTPMethodStats_GetThroughput(t *testing.T) {
	stats := &client.HTTPMethodStats{}

	// Test with no requests
	if throughput := stats.GetThroughput(); throughput != 0.0 {
		t.Errorf("Expected 0 req/s with no requests, got %.2f", throughput)
	}

	// Simulate requests over time
	now := time.Now()
	stats.UpdateTimestamps(now)
	stats.UpdateMethodCount("GET")

	// Add another request 1 second later
	stats.UpdateTimestamps(now.Add(1 * time.Second))
	stats.UpdateMethodCount("GET")

	// Should be approximately 2 requests per second
	throughput := stats.GetThroughput()
	if throughput < 1.0 || throughput > 3.0 {
		t.Errorf("Expected throughput around 2 req/s, got %.2f", throughput)
	}
}

func TestHTTPMethodStats_Reset(t *testing.T) {
	stats := &client.HTTPMethodStats{}

	// Add some data
	stats.UpdateMethodCount("GET")
	stats.UpdateStatusCode(200)
	stats.UpdateResponseTime(100 * time.Millisecond)
	stats.UpdateBytesTransferred(100, 200)
	stats.UpdateErrorCounts(true, false)
	stats.UpdateRetryMetrics(2, true)
	stats.UpdateTimestamps(time.Now())

	// Reset
	stats.Reset()

	// Verify everything is reset
	if stats.GetTotalRequests() != 0 {
		t.Errorf("Expected 0 total requests after reset, got %d", stats.GetTotalRequests())
	}
	if stats.Status2xx != 0 {
		t.Errorf("Expected 0 2xx responses after reset, got %d", stats.Status2xx)
	}
	if stats.TotalResponseTime != 0 {
		t.Errorf("Expected 0 total response time after reset, got %v", stats.TotalResponseTime)
	}
	if stats.TotalBytesReceived != 0 {
		t.Errorf("Expected 0 bytes received after reset, got %d", stats.TotalBytesReceived)
	}
	if !stats.FirstRequestTime.IsZero() {
		t.Errorf("Expected zero first request time after reset, got %v", stats.FirstRequestTime)
	}
}

func TestHTTPClient_GetHTTPMethodStats(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	// Make some requests
	_, err := httpClient.Get(ctx, server.URL, nil)
	if err != nil {
		t.Fatalf("GET request failed: %v", err)
	}

	_, err = httpClient.Post(ctx, server.URL, []byte("test"), nil)
	if err != nil {
		t.Fatalf("POST request failed: %v", err)
	}

	// Get stats
	stats := httpClient.GetHTTPMethodStats()

	if stats.GetRequests != 1 {
		t.Errorf("Expected 1 GET request, got %d", stats.GetRequests)
	}
	if stats.PostRequests != 1 {
		t.Errorf("Expected 1 POST request, got %d", stats.PostRequests)
	}
	if stats.Status2xx != 2 {
		t.Errorf("Expected 2 successful responses, got %d", stats.Status2xx)
	}
	if stats.GetTotalRequests() != 2 {
		t.Errorf("Expected 2 total requests, got %d", stats.GetTotalRequests())
	}
}

func TestHTTPClient_GetHTTPMetrics(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	// Make a request
	_, err := httpClient.Get(ctx, server.URL, nil)
	if err != nil {
		t.Fatalf("GET request failed: %v", err)
	}

	// Get comprehensive metrics
	metrics := httpClient.GetHTTPMetrics()

	if metrics.HTTPMethodStats.GetRequests != 1 {
		t.Errorf("Expected 1 GET request in metrics, got %d", metrics.HTTPMethodStats.GetRequests)
	}
	if metrics.ConnectionStats.TotalRequests == 0 {
		t.Errorf("Expected connection stats to be populated")
	}
	if metrics.StartTime.IsZero() {
		t.Errorf("Expected start time to be set")
	}
}

func TestHTTPClient_GetMetricsSummary(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	// Make some requests
	_, err := httpClient.Get(ctx, server.URL, nil)
	if err != nil {
		t.Fatalf("GET request failed: %v", err)
	}

	// Get summary
	summary := httpClient.GetMetricsSummary()

	// Check that all expected fields are present
	expectedFields := []string{
		"total_requests", "success_rate", "error_rate",
		"avg_response_time", "min_response_time", "max_response_time",
		"throughput", "total_bytes_sent", "total_bytes_recv",
		"network_errors", "timeout_errors", "retry_attempts",
		"successful_retries", "connection_reuse", "active_connections",
	}

	for _, field := range expectedFields {
		if _, exists := summary[field]; !exists {
			t.Errorf("Expected field %s in metrics summary", field)
		}
	}

	// Verify some values
	if summary["total_requests"] != int64(1) {
		t.Errorf("Expected 1 total request, got %v", summary["total_requests"])
	}
}

func TestHTTPClient_ResetHTTPMethodStats(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	// Make a request
	_, err := httpClient.Get(ctx, server.URL, nil)
	if err != nil {
		t.Fatalf("GET request failed: %v", err)
	}

	// Verify stats exist
	stats := httpClient.GetHTTPMethodStats()
	if stats.GetTotalRequests() == 0 {
		t.Errorf("Expected some requests before reset")
	}

	// Reset stats
	httpClient.ResetHTTPMethodStats()

	// Verify stats are reset
	stats = httpClient.GetHTTPMethodStats()
	if stats.GetTotalRequests() != 0 {
		t.Errorf("Expected 0 requests after reset, got %d", stats.GetTotalRequests())
	}
}

func TestHTTPClient_MetricsWithRetries(t *testing.T) {
	// Create test server that fails first time, succeeds second time
	attemptCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attemptCount++
		t.Logf("Server attempt %d", attemptCount)
		if attemptCount == 1 {
			w.WriteHeader(http.StatusInternalServerError)
		} else {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("success"))
		}
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())

	// Debug: Check retry config
	retryConfig := httpClient.GetRetryConfig()
	t.Logf("Retry config: MaxRetries=%d, RetryableErrors=%v", retryConfig.MaxRetries, retryConfig.RetryableErrors)

	httpClient.SetRetryConfig(&client.RetryConfig{
		MaxRetries:      2,
		InitialDelay:    10 * time.Millisecond,
		MaxDelay:        100 * time.Millisecond,
		BackoffFactor:   2.0,
		Jitter:          false,
		RetryableErrors: []int{500}, // Explicitly include 500
	})

	ctx := context.Background()

	// Make request that will retry
	resp, err := httpClient.Get(ctx, server.URL, nil)
	if err != nil {
		t.Logf("Error details: %v", err)
		if httpErr, ok := err.(*client.HTTPError); ok {
			t.Logf("HTTPError details: Type=%v, StatusCode=%d, Retryable=%v", httpErr.Type, httpErr.StatusCode, httpErr.Retryable)
		}
		t.Fatalf("GET request failed: %v", err)
	}

	t.Logf("Response status: %d", resp.StatusCode)
	t.Logf("Server was called %d times", attemptCount)

	// Check metrics
	stats := httpClient.GetHTTPMethodStats()

	if stats.GetRequests != 1 {
		t.Errorf("Expected 1 GET request recorded (not retry attempts), got %d", stats.GetRequests)
	}
	if stats.RetryAttempts != 1 {
		t.Errorf("Expected 1 retry attempt, got %d", stats.RetryAttempts)
	}
	if stats.SuccessfulRetries != 1 {
		t.Errorf("Expected 1 successful retry, got %d", stats.SuccessfulRetries)
	}
	if stats.Status2xx != 1 {
		t.Errorf("Expected 1 successful response, got %d", stats.Status2xx)
	}
}

func TestHTTPClient_MetricsWithErrors(t *testing.T) {
	// Create test server that always returns errors
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("server error"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.SetRetryConfig(&client.RetryConfig{
		MaxRetries:      1,
		InitialDelay:    10 * time.Millisecond,
		MaxDelay:        100 * time.Millisecond,
		BackoffFactor:   2.0,
		Jitter:          false,
		RetryableErrors: []int{500}, // Make sure 500 is retryable
	})

	ctx := context.Background()

	// Make request that will fail
	resp, err := httpClient.Get(ctx, server.URL, nil)
	if err == nil {
		t.Error("Expected error from server")
	}

	// The response should still be returned even with error
	if resp == nil {
		t.Error("Expected response even with error")
	}

	// Check metrics
	stats := httpClient.GetHTTPMethodStats()

	if stats.GetRequests != 1 {
		t.Errorf("Expected 1 GET request recorded, got %d", stats.GetRequests)
	}
	if stats.Status5xx != 1 {
		t.Errorf("Expected 1 5xx response, got %d", stats.Status5xx)
	}
	if stats.RetryAttempts != 1 {
		t.Errorf("Expected 1 retry attempt, got %d", stats.RetryAttempts)
	}
	if stats.FailedRetries != 1 {
		t.Errorf("Expected 1 failed retry, got %d", stats.FailedRetries)
	}

	// Verify error rate calculation
	errorRate := stats.GetErrorRate()
	if errorRate != 100.0 {
		t.Errorf("Expected 100%% error rate, got %.2f%%", errorRate)
	}
}

// Request-Level Timeout Tests

func TestHTTPClient_RequestTimeout_Basic(t *testing.T) {
	// Create a server that responds slowly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(100 * time.Millisecond) // Delay longer than request timeout
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.DisableRetries() // Disable retries to test pure timeout behavior
	ctx := context.Background()

	// Create request with short timeout
	req := &client.Request{
		Method:  "GET",
		URL:     server.URL,
		Timeout: 50 * time.Millisecond, // Shorter than server delay
	}

	_, err := httpClient.Execute(ctx, req)

	if err == nil {
		t.Error("Expected timeout error, got nil")
	}

	if !strings.Contains(err.Error(), "context deadline exceeded") {
		t.Errorf("Expected context deadline exceeded error, got: %v", err)
	}
}

func TestHTTPClient_RequestTimeout_NoTimeout(t *testing.T) {
	// Create a server that responds quickly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	// Create request without timeout (should use client defaults)
	req := &client.Request{
		Method: "GET",
		URL:    server.URL,
		// No Timeout specified - should use client config timeouts
	}

	resp, err := httpClient.Execute(ctx, req)

	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	if string(resp.Body) != "success" {
		t.Errorf("Expected 'success', got '%s'", string(resp.Body))
	}
}

func TestHTTPClient_RequestTimeout_LongerThanServer(t *testing.T) {
	// Create a server that responds with a delay
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(50 * time.Millisecond) // Server delay
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("delayed_response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	// Create request with timeout longer than server delay
	req := &client.Request{
		Method:  "GET",
		URL:     server.URL,
		Timeout: 200 * time.Millisecond, // Longer than server delay
	}

	resp, err := httpClient.Execute(ctx, req)

	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	if string(resp.Body) != "delayed_response" {
		t.Errorf("Expected 'delayed_response', got '%s'", string(resp.Body))
	}
}

func TestHTTPClient_RequestTimeout_Validation(t *testing.T) {
	httpClient := client.NewHTTPClient(client.DefaultConfig())
	ctx := context.Background()

	tests := []struct {
		name        string
		timeout     time.Duration
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid positive timeout",
			timeout:     5 * time.Second,
			expectError: false,
		},
		{
			name:        "zero timeout (valid)",
			timeout:     0,
			expectError: false,
		},
		{
			name:        "negative timeout (invalid)",
			timeout:     -1 * time.Second,
			expectError: true,
			errorMsg:    "request timeout must be non-negative",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &client.Request{
				Method:  "GET",
				URL:     "http://example.com",
				Timeout: tt.timeout,
			}

			_, err := httpClient.Execute(ctx, req)

			if tt.expectError {
				if err == nil {
					t.Error("Expected validation error, got nil")
				}
				if !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("Expected error containing '%s', got: %v", tt.errorMsg, err)
				}
			} else {
				// For valid timeouts, we expect a network error (since example.com is not our test server)
				// but not a validation error
				if err != nil && strings.Contains(err.Error(), "request timeout must be non-negative") {
					t.Errorf("Unexpected validation error for valid timeout: %v", err)
				}
			}
		})
	}
}

func TestHTTPClient_RequestTimeout_ContextInteraction(t *testing.T) {
	// Create a server that responds slowly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(200 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("response"))
	}))
	defer server.Close()

	httpClient := client.NewHTTPClient(client.DefaultConfig())
	httpClient.DisableRetries() // Disable retries to test pure timeout behavior

	// Test case 1: Request timeout shorter than context timeout
	t.Run("request_timeout_shorter", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 300*time.Millisecond)
		defer cancel()

		req := &client.Request{
			Method:  "GET",
			URL:     server.URL,
			Timeout: 100 * time.Millisecond, // Request timeout shorter than context timeout
		}

		start := time.Now()
		_, err := httpClient.Execute(ctx, req)
		duration := time.Since(start)

		if err == nil {
			t.Error("Expected timeout error, got nil")
		}

		// Should timeout around request timeout, not context timeout
		if duration > 150*time.Millisecond {
			t.Errorf("Expected timeout around 100ms, took %v", duration)
		}
	})

	// Test case 2: Context timeout shorter than request timeout
	t.Run("context_timeout_shorter", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 80*time.Millisecond)
		defer cancel()

		req := &client.Request{
			Method:  "GET",
			URL:     server.URL,
			Timeout: 300 * time.Millisecond, // Request timeout longer than context timeout
		}

		start := time.Now()
		_, err := httpClient.Execute(ctx, req)
		duration := time.Since(start)

		if err == nil {
			t.Error("Expected timeout error, got nil")
		}

		// Should timeout around context timeout, not request timeout
		if duration > 120*time.Millisecond {
			t.Errorf("Expected timeout around 80ms, took %v", duration)
		}
	})
}

func TestRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		req     *client.Request
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid request",
			req: &client.Request{
				Method:  "GET",
				URL:     "http://example.com",
				Timeout: 5 * time.Second,
			},
			wantErr: false,
		},
		{
			name: "empty method",
			req: &client.Request{
				Method: "",
				URL:    "http://example.com",
			},
			wantErr: true,
			errMsg:  "request method cannot be empty",
		},
		{
			name: "empty URL",
			req: &client.Request{
				Method: "GET",
				URL:    "",
			},
			wantErr: true,
			errMsg:  "request URL cannot be empty",
		},
		{
			name: "negative timeout",
			req: &client.Request{
				Method:  "GET",
				URL:     "http://example.com",
				Timeout: -1 * time.Second,
			},
			wantErr: true,
			errMsg:  "request timeout must be non-negative",
		},
		{
			name: "zero timeout (valid)",
			req: &client.Request{
				Method:  "GET",
				URL:     "http://example.com",
				Timeout: 0,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.req.Validate()

			if tt.wantErr {
				if err == nil {
					t.Error("Expected validation error, got nil")
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("Expected error containing '%s', got: %v", tt.errMsg, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, got: %v", err)
				}
			}
		})
	}
}

// Timeout Strategy Tests

func TestTimeoutStrategy_Validation(t *testing.T) {
	tests := []struct {
		name          string
		strategy      client.TimeoutStrategy
		expectError   bool
		errorContains string
	}{
		{
			name:        "valid_conservative_strategy",
			strategy:    client.NewConservativeTimeoutStrategy(),
			expectError: false,
		},
		{
			name:        "valid_balanced_strategy",
			strategy:    client.NewBalancedTimeoutStrategy(),
			expectError: false,
		},
		{
			name:        "valid_aggressive_strategy",
			strategy:    client.NewAggressiveTimeoutStrategy(),
			expectError: false,
		},
		{
			name: "invalid_base_timeout_multiplier",
			strategy: client.NewCustomTimeoutStrategy(client.TimeoutPolicy{
				BaseTimeoutMultiplier:     0, // Invalid: must be positive
				ReadOperationMultiplier:   1.0,
				WriteOperationMultiplier:  1.0,
				RetryEscalationFactor:     1.5,
				MaxRetryTimeoutMultiplier: 5.0,
				JitterFactor:              0.1,
			}),
			expectError:   true,
			errorContains: "BaseTimeoutMultiplier must be positive",
		},
		{
			name: "invalid_retry_escalation_factor",
			strategy: client.NewCustomTimeoutStrategy(client.TimeoutPolicy{
				BaseTimeoutMultiplier:     1.0,
				ReadOperationMultiplier:   1.0,
				WriteOperationMultiplier:  1.0,
				RetryEscalationFactor:     1.0, // Invalid: must be > 1.0
				MaxRetryTimeoutMultiplier: 5.0,
				JitterFactor:              0.1,
			}),
			expectError:   true,
			errorContains: "RetryEscalationFactor must be greater than 1.0",
		},
		{
			name: "invalid_jitter_factor",
			strategy: client.NewCustomTimeoutStrategy(client.TimeoutPolicy{
				BaseTimeoutMultiplier:     1.0,
				ReadOperationMultiplier:   1.0,
				WriteOperationMultiplier:  1.0,
				RetryEscalationFactor:     1.5,
				MaxRetryTimeoutMultiplier: 5.0,
				JitterFactor:              1.5, // Invalid: must be <= 1.0
			}),
			expectError:   true,
			errorContains: "JitterFactor must be between 0.0 and 1.0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.strategy.Validate()
			if tt.expectError {
				if err == nil {
					t.Error("Expected validation error, got nil")
				} else if !strings.Contains(err.Error(), tt.errorContains) {
					t.Errorf("Expected error containing '%s', got: %v", tt.errorContains, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no validation error, got: %v", err)
				}
			}
		})
	}
}

func TestTimeoutStrategy_CalculateTimeout(t *testing.T) {
	baseTimeout := 10 * time.Second

	tests := []struct {
		name         string
		strategy     client.TimeoutStrategy
		method       string
		retryAttempt int
		expectMin    time.Duration
		expectMax    time.Duration
	}{
		{
			name:         "conservative_get_no_retry",
			strategy:     client.NewConservativeTimeoutStrategy(),
			method:       "GET",
			retryAttempt: 0,
			expectMin:    14 * time.Second, // 1.5 * 1.0 * 10s = 15s (minus jitter)
			expectMax:    16 * time.Second, // 1.5 * 1.0 * 10s = 15s (plus jitter)
		},
		{
			name:         "conservative_post_no_retry",
			strategy:     client.NewConservativeTimeoutStrategy(),
			method:       "POST",
			retryAttempt: 0,
			expectMin:    16 * time.Second, // 1.5 * 1.2 * 10s = 18s (minus jitter)
			expectMax:    19 * time.Second, // 1.5 * 1.2 * 10s = 18s (plus jitter)
		},
		{
			name:         "aggressive_get_no_retry",
			strategy:     client.NewAggressiveTimeoutStrategy(),
			method:       "GET",
			retryAttempt: 0,
			expectMin:    5 * time.Second, // 0.7 * 0.8 * 10s = 5.6s (minus jitter)
			expectMax:    6 * time.Second, // 0.7 * 0.8 * 10s = 5.6s (plus jitter)
		},
		{
			name:         "balanced_get_with_retry",
			strategy:     client.NewBalancedTimeoutStrategy(),
			method:       "GET",
			retryAttempt: 1,
			expectMin:    13 * time.Second, // 1.0 * 1.0 * 1.5 * 10s = 15s (minus jitter)
			expectMax:    17 * time.Second, // 1.0 * 1.0 * 1.5 * 10s = 15s (plus jitter)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			timeout := tt.strategy.CalculateTimeout(baseTimeout, tt.method, tt.retryAttempt)

			if timeout < tt.expectMin {
				t.Errorf("Timeout %v is less than expected minimum %v", timeout, tt.expectMin)
			}
			if timeout > tt.expectMax {
				t.Errorf("Timeout %v is greater than expected maximum %v", timeout, tt.expectMax)
			}
		})
	}
}

func TestTimeoutStrategy_RetryEscalation(t *testing.T) {
	// Create a server that responds slowly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(200 * time.Millisecond)
		w.WriteHeader(http.StatusInternalServerError) // Force retry
		w.Write([]byte("server error"))
	}))
	defer server.Close()

	// Create client with conservative strategy and retries enabled
	config := client.DefaultConfig()
	config.TimeoutStrategy = client.NewConservativeTimeoutStrategy()
	config.ResponseHeaderTimeout = 100 * time.Millisecond // Base timeout shorter than server response

	retryConfig := &client.RetryConfig{
		MaxRetries:      2,
		InitialDelay:    10 * time.Millisecond,
		MaxDelay:        1 * time.Second,
		BackoffFactor:   2.0,
		Jitter:          false,
		RetryableErrors: []int{500},
	}

	httpClient := client.NewHTTPClientWithRetry(config, retryConfig)
	ctx := context.Background()

	req := &client.Request{
		Method: "GET",
		URL:    server.URL,
	}

	start := time.Now()
	_, err := httpClient.Execute(ctx, req)
	duration := time.Since(start)

	// Should fail due to timeout escalation, but take longer than base timeout
	if err == nil {
		t.Error("Expected timeout error due to escalation, got nil")
	}

	// With retry escalation, total time should be longer than single base timeout
	// but still fail due to server taking too long even with escalated timeouts
	if duration < 100*time.Millisecond {
		t.Errorf("Expected duration > 100ms due to retry escalation, got %v", duration)
	}
}

func TestTimeoutStrategy_OperationSpecificTimeouts(t *testing.T) {
	// Create servers with different response times
	readServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(80 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("read response"))
	}))
	defer readServer.Close()

	writeServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(80 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("write response"))
	}))
	defer writeServer.Close()

	// Create client with aggressive strategy (shorter timeouts for reads)
	config := client.DefaultConfig()
	config.TimeoutStrategy = client.NewAggressiveTimeoutStrategy()
	config.ResponseHeaderTimeout = 100 * time.Millisecond // Base timeout

	httpClient := client.NewHTTPClient(config)
	ctx := context.Background()

	// Test GET request (read operation) - should use shorter timeout
	getReq := &client.Request{
		Method: "GET",
		URL:    readServer.URL,
	}

	_, getErr := httpClient.ExecuteWithoutRetry(ctx, getReq)

	// Test POST request (write operation) - should use longer timeout
	postReq := &client.Request{
		Method: "POST",
		URL:    writeServer.URL,
		Body:   []byte(`{"test": "data"}`),
	}

	_, postErr := httpClient.ExecuteWithoutRetry(ctx, postReq)

	// With aggressive strategy: reads get 0.7 * 0.8 = 0.56 multiplier (56ms)
	// writes get 0.7 * 1.0 = 0.7 multiplier (70ms)
	// Server responds in 80ms, so both should timeout, but this tests the different timeout calculations

	if getErr == nil {
		t.Error("Expected GET to timeout with aggressive strategy, got nil")
	}
	if postErr == nil {
		t.Error("Expected POST to timeout with aggressive strategy, got nil")
	}
}

func TestConfig_TimeoutStrategyValidation(t *testing.T) {
	// Test that Config.Validate() properly validates timeout strategy
	config := client.DefaultConfig()

	// Valid config should pass
	if err := config.Validate(); err != nil {
		t.Errorf("Expected valid config to pass validation, got: %v", err)
	}

	// Invalid timeout strategy should fail config validation
	invalidPolicy := client.TimeoutPolicy{
		BaseTimeoutMultiplier:     -1.0, // Invalid
		ReadOperationMultiplier:   1.0,
		WriteOperationMultiplier:  1.0,
		RetryEscalationFactor:     1.5,
		MaxRetryTimeoutMultiplier: 5.0,
		JitterFactor:              0.1,
	}
	config.TimeoutStrategy = client.NewCustomTimeoutStrategy(invalidPolicy)

	if err := config.Validate(); err == nil {
		t.Error("Expected config with invalid timeout strategy to fail validation")
	} else if !strings.Contains(err.Error(), "invalid timeout strategy") {
		t.Errorf("Expected error about invalid timeout strategy, got: %v", err)
	}
}

func TestTimeoutStrategy_String(t *testing.T) {
	tests := []struct {
		strategyType client.TimeoutStrategyType
		expected     string
	}{
		{client.TimeoutStrategyConservative, "Conservative"},
		{client.TimeoutStrategyBalanced, "Balanced"},
		{client.TimeoutStrategyAggressive, "Aggressive"},
		{client.TimeoutStrategyCustom, "Custom"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if got := tt.strategyType.String(); got != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, got)
			}
		})
	}
}

// Dynamic Timeout Adjustment Tests

func TestResponseTimeTracker_Basic(t *testing.T) {
	tracker := client.NewResponseTimeTracker(5)

	// Test empty tracker
	if tracker.GetSampleCount() != 0 {
		t.Errorf("Expected 0 samples, got %d", tracker.GetSampleCount())
	}
	if tracker.GetAverage() != 0 {
		t.Errorf("Expected 0 average, got %v", tracker.GetAverage())
	}

	// Add samples
	samples := []time.Duration{
		100 * time.Millisecond,
		200 * time.Millisecond,
		300 * time.Millisecond,
		400 * time.Millisecond,
		500 * time.Millisecond,
	}

	for _, sample := range samples {
		tracker.AddSample(sample)
	}

	// Test sample count
	if tracker.GetSampleCount() != 5 {
		t.Errorf("Expected 5 samples, got %d", tracker.GetSampleCount())
	}

	// Test average
	expectedAvg := 300 * time.Millisecond
	if tracker.GetAverage() != expectedAvg {
		t.Errorf("Expected average %v, got %v", expectedAvg, tracker.GetAverage())
	}

	// Test percentiles
	p50 := tracker.GetPercentile(0.5)
	if p50 != 300*time.Millisecond {
		t.Errorf("Expected P50 %v, got %v", 300*time.Millisecond, p50)
	}

	p95 := tracker.GetPercentile(0.95)
	// For 5 samples [100, 200, 300, 400, 500], P95 index = (5-1) * 0.95 = 3.8 -> 3 (0-indexed) = 400ms
	if p95 != 400*time.Millisecond {
		t.Errorf("Expected P95 %v, got %v", 400*time.Millisecond, p95)
	}
}

func TestResponseTimeTracker_RingBuffer(t *testing.T) {
	tracker := client.NewResponseTimeTracker(3) // Small buffer to test wraparound

	// Add more samples than buffer size
	samples := []time.Duration{
		100 * time.Millisecond,
		200 * time.Millisecond,
		300 * time.Millisecond,
		400 * time.Millisecond, // This should overwrite the first sample
		500 * time.Millisecond, // This should overwrite the second sample
	}

	for _, sample := range samples {
		tracker.AddSample(sample)
	}

	// Should only have the last 3 samples
	if tracker.GetSampleCount() != 3 {
		t.Errorf("Expected 3 samples, got %d", tracker.GetSampleCount())
	}

	// Average should be of last 3 samples: (300 + 400 + 500) / 3 = 400ms
	expectedAvg := 400 * time.Millisecond
	if tracker.GetAverage() != expectedAvg {
		t.Errorf("Expected average %v, got %v", expectedAvg, tracker.GetAverage())
	}
}

func TestDynamicTimeoutConfig_Validation(t *testing.T) {
	tests := []struct {
		name          string
		config        client.DynamicTimeoutConfig
		expectError   bool
		errorContains string
	}{
		{
			name:        "valid_default_config",
			config:      client.DefaultDynamicTimeoutConfig(),
			expectError: false,
		},
		{
			name: "invalid_adjustment_sensitivity",
			config: client.DynamicTimeoutConfig{
				AdjustmentSensitivity: -0.1,
				DampeningFactor:       0.7,
				TargetSuccessRate:     0.95,
				ErrorRateThreshold:    0.1,
				MinTimeoutMultiplier:  0.5,
				MaxTimeoutMultiplier:  5.0,
				MaxAdjustmentStep:     0.2,
				SampleWindow:          100,
				MinSampleCount:        10,
				AdjustmentWindow:      30 * time.Second,
			},
			expectError:   true,
			errorContains: "AdjustmentSensitivity",
		},
		{
			name: "invalid_dampening_factor",
			config: client.DynamicTimeoutConfig{
				AdjustmentSensitivity: 0.3,
				DampeningFactor:       1.5,
				TargetSuccessRate:     0.95,
				ErrorRateThreshold:    0.1,
				MinTimeoutMultiplier:  0.5,
				MaxTimeoutMultiplier:  5.0,
				MaxAdjustmentStep:     0.2,
				SampleWindow:          100,
				MinSampleCount:        10,
				AdjustmentWindow:      30 * time.Second,
			},
			expectError:   true,
			errorContains: "DampeningFactor",
		},
		{
			name: "invalid_min_max_multipliers",
			config: client.DynamicTimeoutConfig{
				AdjustmentSensitivity: 0.3,
				DampeningFactor:       0.7,
				TargetSuccessRate:     0.95,
				ErrorRateThreshold:    0.1,
				MinTimeoutMultiplier:  5.0,
				MaxTimeoutMultiplier:  2.0, // Max < Min
				MaxAdjustmentStep:     0.2,
				SampleWindow:          100,
				MinSampleCount:        10,
				AdjustmentWindow:      30 * time.Second,
			},
			expectError:   true,
			errorContains: "MaxTimeoutMultiplier",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				} else if !strings.Contains(err.Error(), tt.errorContains) {
					t.Errorf("Expected error containing '%s', got '%v'", tt.errorContains, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestDynamicTimeoutAdjuster_BasicFunctionality(t *testing.T) {
	config := client.DynamicTimeoutConfig{
		Enabled:               true,
		AdjustmentSensitivity: 0.5,
		DampeningFactor:       0.8,
		TargetSuccessRate:     0.95,
		ErrorRateThreshold:    0.1,
		ResponseTimeP95:       5 * time.Second,
		ResponseTimeP99:       10 * time.Second,
		MinTimeoutMultiplier:  0.5,
		MaxTimeoutMultiplier:  3.0,
		MaxAdjustmentStep:     0.2,
		SampleWindow:          10,
		MinSampleCount:        5,
		AdjustmentWindow:      1 * time.Millisecond, // Very short for testing
	}

	adjuster := client.NewDynamicTimeoutAdjuster(config)

	// Add some response times
	responseTimes := []time.Duration{
		100 * time.Millisecond,
		200 * time.Millisecond,
		300 * time.Millisecond,
		400 * time.Millisecond,
		500 * time.Millisecond,
	}

	for _, rt := range responseTimes {
		adjuster.AddResponseTime(rt)
	}

	// Create mock stats with high error rate to trigger adjustment
	stats := &client.HTTPMethodStats{}
	stats.UpdateStatusCode(500) // Server error
	stats.UpdateStatusCode(500) // Server error
	stats.UpdateStatusCode(200) // Success
	stats.UpdateStatusCode(200) // Success
	stats.UpdateStatusCode(200) // Success

	// Wait a bit to ensure adjustment window has passed
	time.Sleep(2 * time.Millisecond)

	// Calculate adjustment - should increase timeout due to high error rate
	multiplier := adjuster.CalculateAdjustment(stats)

	// Should be greater than 1.0 due to high error rate
	if multiplier <= 1.0 {
		t.Errorf("Expected multiplier > 1.0 due to high error rate, got %f", multiplier)
	}

	// Check stats
	adjustmentStats := adjuster.GetAdjustmentStats()
	if adjustmentStats["enabled"] != true {
		t.Errorf("Expected enabled=true, got %v", adjustmentStats["enabled"])
	}
	if adjustmentStats["sample_count"] != 5 {
		t.Errorf("Expected sample_count=5, got %v", adjustmentStats["sample_count"])
	}
}

func TestAdaptiveTimeoutStrategy_Integration(t *testing.T) {
	// Create adaptive timeout strategy
	strategy := client.NewAdaptiveTimeoutStrategy(client.TimeoutStrategyBalanced)

	// Validate the strategy
	if err := strategy.Validate(); err != nil {
		t.Fatalf("Strategy validation failed: %v", err)
	}

	// Test timeout calculation with dynamic adjustment
	baseTimeout := 1 * time.Second
	method := "GET"
	retryAttempt := 0

	// Initial calculation should be close to base timeout
	timeout1 := strategy.CalculateTimeout(baseTimeout, method, retryAttempt)
	if timeout1 < 500*time.Millisecond || timeout1 > 2*time.Second {
		t.Errorf("Expected timeout between 500ms and 2s, got %v", timeout1)
	}

	// Add some response times to the adjuster
	if strategy.DynamicAdjuster != nil {
		responseTimes := []time.Duration{
			2 * time.Second, // Slow responses
			3 * time.Second,
			time.Duration(2.5 * float64(time.Second)),
			4 * time.Second,
			time.Duration(3.5 * float64(time.Second)),
		}

		for _, rt := range responseTimes {
			strategy.DynamicAdjuster.AddResponseTime(rt)
		}

		// Create stats with some errors to trigger adjustment
		stats := &client.HTTPMethodStats{}
		for i := 0; i < 10; i++ {
			if i < 3 {
				stats.UpdateStatusCode(500) // 30% error rate
			} else {
				stats.UpdateStatusCode(200) // 70% success rate
			}
		}

		// Wait for adjustment window
		time.Sleep(time.Millisecond)

		// Calculate adjustment
		strategy.DynamicAdjuster.CalculateAdjustment(stats)

		// Second calculation should be different due to dynamic adjustment
		timeout2 := strategy.CalculateTimeout(baseTimeout, method, retryAttempt)

		// Should be adjusted based on performance data
		if timeout2 == timeout1 {
			t.Logf("Timeouts are the same, but this might be expected if adjustment conditions weren't met")
		}
	}
}

func TestHTTPClient_DynamicTimeoutIntegration(t *testing.T) {
	// Create server that responds with variable delays
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Simulate variable response times
		delay := 100 * time.Millisecond
		if r.URL.Query().Get("slow") == "true" {
			delay = 300 * time.Millisecond
		}
		time.Sleep(delay)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("response"))
	}))
	defer server.Close()

	// Create config with adaptive timeout strategy
	config := client.DefaultConfig()
	config.TimeoutStrategy = client.NewAdaptiveTimeoutStrategy(client.TimeoutStrategyBalanced)
	config.ResponseHeaderTimeout = 1 * time.Second

	httpClient := client.NewHTTPClient(config)
	defer httpClient.Close()

	// Make some requests to populate response time data
	ctx := context.Background()
	for i := 0; i < 10; i++ {
		url := server.URL
		if i%3 == 0 {
			url += "?slow=true" // Make some requests slower
		}

		req := &client.Request{
			Method: "GET",
			URL:    url,
		}

		resp, err := httpClient.ExecuteWithoutRetry(ctx, req)
		if err != nil {
			t.Errorf("Request %d failed: %v", i, err)
			continue
		}
		if resp.StatusCode != 200 {
			t.Errorf("Request %d got status %d", i, resp.StatusCode)
		}
	}

	// Trigger dynamic timeout adjustment
	httpClient.UpdateDynamicTimeoutAdjustment()

	// Get dynamic timeout stats
	stats := httpClient.GetDynamicTimeoutStats()
	if stats["enabled"] != true {
		t.Errorf("Expected dynamic timeout to be enabled, got %v", stats["enabled"])
	}

	sampleCount, ok := stats["sample_count"].(int)
	if !ok || sampleCount == 0 {
		t.Errorf("Expected sample_count > 0, got %v", stats["sample_count"])
	}

	// Verify response time tracking
	avgResponseTime, ok := stats["avg_response_time"].(time.Duration)
	if !ok || avgResponseTime == 0 {
		t.Errorf("Expected avg_response_time > 0, got %v", stats["avg_response_time"])
	}
}
