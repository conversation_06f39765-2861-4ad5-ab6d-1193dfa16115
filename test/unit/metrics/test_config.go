// Package metrics provides test configuration utilities for development-safe testing
package metrics

import (
	"os"
	"strconv"
	"time"

	"neuralmetergo/internal/metrics"
)

// TestScale defines the testing scale mode
type TestScale int

const (
	// DevelopmentScale: Safe for local Mac development, minimal resource usage
	DevelopmentScale TestScale = iota
	// IntegrationScale: Moderate load for CI environments
	IntegrationScale
	// PerformanceScale: Full scale for cloud/server environments only
	PerformanceScale
)

// TestModeConfig provides resource-aware configurations for different testing scenarios
type TestModeConfig struct {
	Mode                TestScale
	MaxCapacity         int           // Maximum sliding window capacity
	MaxConcurrency      int           // Maximum number of goroutines
	MaxValuesPerWorker  int           // Maximum values per goroutine in concurrent tests
	DefaultTimeout      time.Duration // Timeout for async operations
	CleanupInterval     time.Duration // Cleanup interval for time-based windows
	BenchmarkIterations int           // Benchmark iteration limit
	EnableStatistics    bool          // Whether to calculate expensive statistics
}

// GetTestScale determines the test scale from environment variables or defaults to Development
func GetTestScale() TestScale {
	if modeStr := os.Getenv("NEURALMETER_TEST_MODE"); modeStr != "" {
		switch modeStr {
		case "development", "dev":
			return DevelopmentScale
		case "integration", "int":
			return IntegrationScale
		case "performance", "perf":
			return PerformanceScale
		}
	}

	// Default to development scale for safety
	return DevelopmentScale
}

// NewTestModeConfig creates a test configuration based on the specified scale
func NewTestModeConfig(mode TestScale) TestModeConfig {
	switch mode {
	case DevelopmentScale:
		return TestModeConfig{
			Mode:                DevelopmentScale,
			MaxCapacity:         50,                     // Small datasets only
			MaxConcurrency:      3,                      // Minimal goroutines
			MaxValuesPerWorker:  20,                     // Small workload per worker
			DefaultTimeout:      time.Second,            // Fast timeouts
			CleanupInterval:     time.Millisecond * 100, // Fast cleanup
			BenchmarkIterations: 1000,                   // Limited benchmark runs
			EnableStatistics:    false,                  // Skip expensive calculations
		}
	case IntegrationScale:
		return TestModeConfig{
			Mode:                IntegrationScale,
			MaxCapacity:         200,                    // Moderate datasets
			MaxConcurrency:      5,                      // Moderate concurrency
			MaxValuesPerWorker:  50,                     // Moderate workload
			DefaultTimeout:      time.Second * 3,        // Reasonable timeouts
			CleanupInterval:     time.Millisecond * 500, // Moderate cleanup
			BenchmarkIterations: 10000,                  // More benchmark runs
			EnableStatistics:    true,                   // Enable statistics
		}
	case PerformanceScale:
		return TestModeConfig{
			Mode:                PerformanceScale,
			MaxCapacity:         1000,             // Production-scale datasets
			MaxConcurrency:      10,               // Full concurrency
			MaxValuesPerWorker:  100,              // Full workload per worker
			DefaultTimeout:      time.Second * 10, // Longer timeouts
			CleanupInterval:     time.Second * 10, // Production cleanup intervals
			BenchmarkIterations: 100000,           // Full benchmark runs
			EnableStatistics:    true,             // Full statistics
		}
	default:
		// Default to development for safety
		return NewTestModeConfig(DevelopmentScale)
	}
}

// GetCurrentTestConfig returns the test configuration for the current environment
func GetCurrentTestConfig() TestModeConfig {
	mode := GetTestScale()
	return NewTestModeConfig(mode)
}

// CreateDevelopmentSlidingWindowConfig creates a development-safe sliding window configuration
func (c TestModeConfig) CreateDevelopmentSlidingWindowConfig(windowType metrics.WindowType) metrics.SlidingWindowConfig {
	switch windowType {
	case metrics.WindowTypeCount:
		return metrics.SlidingWindowConfig{
			Type:        metrics.WindowTypeCount,
			Capacity:    c.MaxCapacity,
			InitialSize: 10, // Small initial size
			EnableStats: c.EnableStatistics,
			EnableAsync: false, // Disable async for development
		}
	case metrics.WindowTypeTime:
		return metrics.SlidingWindowConfig{
			Type:        metrics.WindowTypeTime,
			Duration:    time.Second * 2, // Short duration for development
			InitialSize: 10,
			EnableStats: c.EnableStatistics,
			EnableAsync: c.Mode != DevelopmentScale, // Only async in higher modes
			CleanupTick: c.CleanupInterval,
		}
	default:
		return c.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)
	}
}

// ShouldSkipTest determines if a test should be skipped based on the current mode
func (c TestModeConfig) ShouldSkipTest(testType string) bool {
	switch testType {
	case "benchmark":
		// Skip benchmarks in development mode unless specifically requested
		return c.Mode == DevelopmentScale && os.Getenv("RUN_BENCHMARKS") == ""
	case "concurrent-heavy":
		// Skip heavy concurrency tests in development mode
		return c.Mode == DevelopmentScale
	case "statistics-heavy":
		// Skip statistics-heavy tests if statistics are disabled
		return !c.EnableStatistics
	default:
		return false
	}
}

// GetBenchmarkN returns the appropriate benchmark iteration count
func (c TestModeConfig) GetBenchmarkN(defaultN int) int {
	if c.BenchmarkIterations < defaultN {
		return c.BenchmarkIterations
	}
	return defaultN
}

// CreateConcurrencyParams returns safe concurrency parameters for tests
func (c TestModeConfig) CreateConcurrencyParams() (numGoroutines int, valuesPerGoroutine int) {
	return c.MaxConcurrency, c.MaxValuesPerWorker
}

// GetTestTimeout returns the appropriate timeout for async tests
func (c TestModeConfig) GetTestTimeout() time.Duration {
	return c.DefaultTimeout
}

// LogTestScale logs the current test scale for debugging
func (c TestModeConfig) LogTestScale() string {
	switch c.Mode {
	case DevelopmentScale:
		return "DEVELOPMENT (Mac-safe, minimal resources)"
	case IntegrationScale:
		return "INTEGRATION (CI-appropriate, moderate resources)"
	case PerformanceScale:
		return "PERFORMANCE (Full-scale, cloud/server only)"
	default:
		return "UNKNOWN"
	}
}

// Override allows manual override of specific parameters for edge case testing
type TestConfigOverride struct {
	Capacity        *int
	Concurrency     *int
	ValuesPerWorker *int
	EnableStats     *bool
	Timeout         *time.Duration
}

// ApplyOverride applies manual overrides to the test configuration
func (c TestModeConfig) ApplyOverride(override TestConfigOverride) TestModeConfig {
	if override.Capacity != nil {
		c.MaxCapacity = *override.Capacity
	}
	if override.Concurrency != nil {
		c.MaxConcurrency = *override.Concurrency
	}
	if override.ValuesPerWorker != nil {
		c.MaxValuesPerWorker = *override.ValuesPerWorker
	}
	if override.EnableStats != nil {
		c.EnableStatistics = *override.EnableStats
	}
	if override.Timeout != nil {
		c.DefaultTimeout = *override.Timeout
	}
	return c
}

// ParseEnvInt parses an integer from environment variable with fallback
func ParseEnvInt(envVar string, fallback int) int {
	if val := os.Getenv(envVar); val != "" {
		if parsed, err := strconv.Atoi(val); err == nil {
			return parsed
		}
	}
	return fallback
}

// ParseEnvDuration parses a duration from environment variable with fallback
func ParseEnvDuration(envVar string, fallback time.Duration) time.Duration {
	if val := os.Getenv(envVar); val != "" {
		if parsed, err := time.ParseDuration(val); err == nil {
			return parsed
		}
	}
	return fallback
}
