package metrics

import (
	"fmt"
	"neuralmetergo/internal/metrics"
	"runtime"
	"sync"
	"testing"
	"time"
)

func TestNewCollectionManager(t *testing.T) {
	cm := metrics.NewCollectionManager()
	if cm == nil {
		t.Fatal("NewCollectionManager returned nil")
	}

	config := cm.GetConfig()
	if config.WorkerCount != runtime.NumCPU() {
		t.<PERSON>("Expected WorkerCount %d, got %d", runtime.NumCPU(), config.WorkerCount)
	}

	if config.CollectionRate != time.Millisecond*100 {
		t.Errorf("Expected CollectionRate %v, got %v", time.Millisecond*100, config.CollectionRate)
	}

	if config.ChannelBuffer != 1000 {
		t.<PERSON>rrorf("Expected ChannelBuffer 1000, got %d", config.ChannelBuffer)
	}

	if !config.EnableBatching {
		t.Error("Expected EnableBatching to be true")
	}
}

func TestNewCollectionManagerWithConfig(t *testing.T) {
	customConfig := metrics.CollectionConfig{
		WorkerCount:     2,
		CollectionRate:  time.Millisecond * 50,
		ChannelBuffer:   500,
		BatchSize:       25,
		EnableBatching:  false,
		MaxRetries:      5,
		RetryDelay:      time.Millisecond * 5,
		EnableMetrics:   false,
		ShutdownTimeout: time.Second * 3,
	}

	cm := metrics.NewCollectionManagerWithConfig(customConfig)
	if cm == nil {
		t.Fatal("NewCollectionManagerWithConfig returned nil")
	}

	config := cm.GetConfig()
	if config.WorkerCount != 2 {
		t.Errorf("Expected WorkerCount 2, got %d", config.WorkerCount)
	}

	if config.CollectionRate != time.Millisecond*50 {
		t.Errorf("Expected CollectionRate %v, got %v", time.Millisecond*50, config.CollectionRate)
	}

	if config.EnableBatching {
		t.Error("Expected EnableBatching to be false")
	}
}

func TestCollectionManagerRegisterSources(t *testing.T) {
	cm := metrics.NewCollectionManager()

	// Create test metrics
	counter := metrics.NewCounter()
	gauge := metrics.NewGauge()
	histogram := metrics.NewHistogram()

	// Register metrics
	tags := map[string]string{"service": "test", "version": "1.0"}
	cm.RegisterCounter("test_counter", counter, tags)
	cm.RegisterGauge("test_gauge", gauge, tags)
	cm.RegisterHistogram("test_histogram", histogram, tags)

	// Verify registration
	sources := cm.GetSources()
	if len(sources) != 3 {
		t.Errorf("Expected 3 sources, got %d", len(sources))
	}

	// Check counter source
	if counterSource, exists := sources["test_counter"]; exists {
		if counterSource.Type != "counter" {
			t.Errorf("Expected counter type, got %s", counterSource.Type)
		}
		if !counterSource.Enabled {
			t.Error("Expected counter source to be enabled")
		}
		if counterSource.Tags["service"] != "test" {
			t.Errorf("Expected service tag 'test', got %s", counterSource.Tags["service"])
		}
	} else {
		t.Error("Counter source not found")
	}

	// Check gauge source
	if gaugeSource, exists := sources["test_gauge"]; exists {
		if gaugeSource.Type != "gauge" {
			t.Errorf("Expected gauge type, got %s", gaugeSource.Type)
		}
	} else {
		t.Error("Gauge source not found")
	}

	// Check histogram source
	if histogramSource, exists := sources["test_histogram"]; exists {
		if histogramSource.Type != "histogram" {
			t.Errorf("Expected histogram type, got %s", histogramSource.Type)
		}
	} else {
		t.Error("Histogram source not found")
	}
}

func TestCollectionManagerSourceManagement(t *testing.T) {
	cm := metrics.NewCollectionManager()
	counter := metrics.NewCounter()

	// Register source
	cm.RegisterCounter("test_counter", counter, nil)

	// Verify source is enabled by default
	sources := cm.GetSources()
	if !sources["test_counter"].Enabled {
		t.Error("Source should be enabled by default")
	}

	// Disable source
	cm.DisableSource("test_counter")
	sources = cm.GetSources()
	if sources["test_counter"].Enabled {
		t.Error("Source should be disabled")
	}

	// Enable source
	cm.EnableSource("test_counter")
	sources = cm.GetSources()
	if !sources["test_counter"].Enabled {
		t.Error("Source should be enabled")
	}

	// Unregister source
	cm.UnregisterSource("test_counter")
	sources = cm.GetSources()
	if len(sources) != 0 {
		t.Errorf("Expected 0 sources after unregistering, got %d", len(sources))
	}
}

func TestCollectionManagerStartStop(t *testing.T) {
	cm := metrics.NewCollectionManager()

	// Initially should not be running
	if cm.IsRunning() {
		t.Error("Collection manager should not be running initially")
	}

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}

	// Should be running now
	if !cm.IsRunning() {
		t.Error("Collection manager should be running after start")
	}

	// Starting again should not return error
	err = cm.Start()
	if err != nil {
		t.Errorf("Starting already running manager should not return error: %v", err)
	}

	// Stop collection
	err = cm.Stop()
	if err != nil {
		t.Fatalf("Failed to stop collection manager: %v", err)
	}

	// Should not be running now
	if cm.IsRunning() {
		t.Error("Collection manager should not be running after stop")
	}

	// Stopping again should not return error
	err = cm.Stop()
	if err != nil {
		t.Errorf("Stopping already stopped manager should not return error: %v", err)
	}
}

func TestCollectionManagerWithAggregator(t *testing.T) {
	cm := metrics.NewCollectionManager()
	aggregator := metrics.NewMetricAggregator()

	// Set aggregator
	cm.SetAggregator(aggregator)

	// Create and register metrics
	counter := metrics.NewCounter()
	cm.RegisterCounter("test_counter", counter, nil)

	// Start both systems
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	err = aggregator.Start()
	if err != nil {
		t.Fatalf("Failed to start aggregator: %v", err)
	}
	defer aggregator.Stop()

	// Verify both are running
	if !cm.IsRunning() {
		t.Error("Collection manager should be running")
	}
	if !aggregator.IsRunning() {
		t.Error("Aggregator should be running")
	}
}

func TestCollectionManagerStats(t *testing.T) {
	cm := metrics.NewCollectionManager()

	// Get initial stats
	stats := cm.GetStats()
	if stats.TotalEvents != 0 {
		t.Errorf("Expected 0 total events initially, got %d", stats.TotalEvents)
	}
	if stats.ActiveWorkers != 0 {
		t.Errorf("Expected 0 active workers initially, got %d", stats.ActiveWorkers)
	}

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Wait a moment for workers to start
	time.Sleep(time.Millisecond * 50)

	// Get stats after starting
	stats = cm.GetStats()
	if stats.ActiveWorkers == 0 {
		t.Error("Expected active workers after starting")
	}
}

func TestCollectionWorkerConcurrency(t *testing.T) {
	// Use a smaller worker count for testing
	config := metrics.DefaultCollectionConfig
	config.WorkerCount = 2
	config.CollectionRate = time.Millisecond * 10 // Fast collection for testing

	cm := metrics.NewCollectionManagerWithConfig(config)

	// Create multiple metrics
	counter1 := metrics.NewCounter()
	counter2 := metrics.NewCounter()
	gauge1 := metrics.NewGauge()
	gauge2 := metrics.NewGauge()

	// Add some values
	counter1.Add(10)
	counter2.Add(20)
	gauge1.Set(1.5)
	gauge2.Set(2.5)

	// Register metrics
	cm.RegisterCounter("counter1", counter1, map[string]string{"type": "test"})
	cm.RegisterCounter("counter2", counter2, map[string]string{"type": "test"})
	cm.RegisterGauge("gauge1", gauge1, map[string]string{"type": "test"})
	cm.RegisterGauge("gauge2", gauge2, map[string]string{"type": "test"})

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Let collection run for a short time
	time.Sleep(time.Millisecond * 100)

	// Check stats
	stats := cm.GetStats()
	if stats.TotalEvents == 0 {
		t.Error("Expected some collection events")
	}
	if stats.ActiveWorkers == 0 {
		t.Error("Expected active workers")
	}
}

func TestCollectionEventProcessing(t *testing.T) {
	// Use non-batching mode for simpler testing
	config := metrics.DefaultCollectionConfig
	config.EnableBatching = false
	config.WorkerCount = 1
	config.CollectionRate = time.Millisecond * 10

	cm := metrics.NewCollectionManagerWithConfig(config)

	// Create and register a counter
	counter := metrics.NewCounter()
	counter.Add(42)
	cm.RegisterCounter("test_counter", counter, map[string]string{"env": "test"})

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Let collection run
	time.Sleep(time.Millisecond * 50)

	// Check that events were processed
	stats := cm.GetStats()
	if stats.TotalEvents == 0 {
		t.Error("Expected some events to be collected")
	}
	if stats.ProcessedEvents == 0 {
		t.Error("Expected some events to be processed")
	}
}

func TestCollectionBatchProcessing(t *testing.T) {
	// Use batching mode
	config := metrics.DefaultCollectionConfig
	config.EnableBatching = true
	config.BatchSize = 5
	config.WorkerCount = 1
	config.CollectionRate = time.Millisecond * 5

	cm := metrics.NewCollectionManagerWithConfig(config)

	// Create multiple counters to generate enough events
	for i := 0; i < 10; i++ {
		counter := metrics.NewCounter()
		counter.Add(int64(i))
		cm.RegisterCounter(fmt.Sprintf("counter_%d", i), counter, nil)
	}

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Let collection run to generate batches
	time.Sleep(time.Millisecond * 100)

	// Check that events were processed
	stats := cm.GetStats()
	if stats.TotalEvents == 0 {
		t.Error("Expected some events to be collected")
	}
	if stats.ProcessedEvents == 0 {
		t.Error("Expected some events to be processed")
	}
}

func TestCollectionManagerConfigUpdate(t *testing.T) {
	cm := metrics.NewCollectionManager()

	// Should be able to update config when not running
	newConfig := metrics.DefaultCollectionConfig
	newConfig.WorkerCount = 1
	newConfig.CollectionRate = time.Millisecond * 200

	err := cm.UpdateConfig(newConfig)
	if err != nil {
		t.Errorf("Failed to update config when stopped: %v", err)
	}

	// Verify config was updated
	config := cm.GetConfig()
	if config.WorkerCount != 1 {
		t.Errorf("Expected WorkerCount 1, got %d", config.WorkerCount)
	}

	// Start collection
	err = cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Should not be able to update config when running
	err = cm.UpdateConfig(newConfig)
	if err == nil {
		t.Error("Expected error when updating config while running")
	}
}

func TestCollectionManagerShutdownTimeout(t *testing.T) {
	// Use a very short shutdown timeout
	config := metrics.DefaultCollectionConfig
	config.ShutdownTimeout = time.Millisecond * 1 // Very short timeout
	config.WorkerCount = 1

	cm := metrics.NewCollectionManagerWithConfig(config)

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}

	// Stop should complete (might timeout, but shouldn't hang)
	err = cm.Stop()
	// Note: We don't check for timeout error here as it's expected with very short timeout
	_ = err
}

func TestCollectionHistogramProcessing(t *testing.T) {
	cm := metrics.NewCollectionManager()

	// Create and populate histogram
	histogram := metrics.NewHistogram()
	for i := 0; i < 100; i++ {
		histogram.Observe(float64(i) * 0.01) // 0.00 to 0.99 seconds
	}

	cm.RegisterHistogram("test_histogram", histogram, map[string]string{"type": "response_time"})

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Let collection run for longer to ensure events are collected
	time.Sleep(time.Millisecond * 200)

	// Check that histogram events were processed
	stats := cm.GetStats()
	t.Logf("Stats: TotalEvents=%d, ProcessedEvents=%d, CollectionErrors=%d",
		stats.TotalEvents, stats.ProcessedEvents, stats.CollectionErrors)

	if stats.TotalEvents == 0 {
		t.Error("Expected histogram events to be collected")
	}
}

func TestCollectionConcurrentSourceModification(t *testing.T) {
	cm := metrics.NewCollectionManager()

	// Start collection first
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Concurrently add/remove sources while collection is running
	var wg sync.WaitGroup
	numGoroutines := 5

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < 10; j++ {
				counter := metrics.NewCounter()
				counter.Add(int64(j))
				sourceName := fmt.Sprintf("concurrent_counter_%d_%d", id, j)

				cm.RegisterCounter(sourceName, counter, nil)
				time.Sleep(time.Millisecond * 1)
				cm.UnregisterSource(sourceName)
			}
		}(i)
	}

	wg.Wait()

	// Collection should still be running and stable
	if !cm.IsRunning() {
		t.Error("Collection manager should still be running after concurrent modifications")
	}
}

func TestCollectionSourceEnableDisable(t *testing.T) {
	cm := metrics.NewCollectionManager()
	counter := metrics.NewCounter()
	counter.Add(100)

	cm.RegisterCounter("toggle_counter", counter, nil)

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Let it collect for a bit - increased timing
	time.Sleep(time.Millisecond * 150)
	stats1 := cm.GetStats()
	t.Logf("Stats1: TotalEvents=%d", stats1.TotalEvents)

	// Disable source
	cm.DisableSource("toggle_counter")
	time.Sleep(time.Millisecond * 150)
	stats2 := cm.GetStats()
	t.Logf("Stats2: TotalEvents=%d", stats2.TotalEvents)

	// Re-enable source
	cm.EnableSource("toggle_counter")
	time.Sleep(time.Millisecond * 150)
	stats3 := cm.GetStats()
	t.Logf("Stats3: TotalEvents=%d", stats3.TotalEvents)

	// Should have more events after re-enabling
	if stats3.TotalEvents <= stats2.TotalEvents {
		t.Errorf("Expected more events after re-enabling source: stats3=%d, stats2=%d",
			stats3.TotalEvents, stats2.TotalEvents)
	}

	// First period should have events
	if stats1.TotalEvents == 0 {
		t.Error("Expected events in first collection period")
	}
}

func TestCollectionLatencyTracking(t *testing.T) {
	config := metrics.DefaultCollectionConfig
	config.EnableBatching = false // Simpler for latency testing
	config.WorkerCount = 1

	cm := metrics.NewCollectionManagerWithConfig(config)
	counter := metrics.NewCounter()
	counter.Add(1)

	cm.RegisterCounter("latency_counter", counter, nil)

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Let collection run
	time.Sleep(time.Millisecond * 50)

	// Check latency stats
	stats := cm.GetStats()
	if stats.ProcessedEvents > 0 {
		if stats.AverageLatency == 0 {
			t.Error("Expected non-zero average latency")
		}
		if stats.MaxLatency == 0 {
			t.Error("Expected non-zero max latency")
		}
	}
}

func TestCollectionChannelOverflow(t *testing.T) {
	// Use very small channel buffer to test overflow
	config := metrics.DefaultCollectionConfig
	config.ChannelBuffer = 2 // Very small buffer
	config.WorkerCount = 1
	config.CollectionRate = time.Microsecond * 100 // Very fast collection

	cm := metrics.NewCollectionManagerWithConfig(config)

	// Register many sources to overwhelm the channel
	for i := 0; i < 20; i++ {
		counter := metrics.NewCounter()
		counter.Add(int64(i))
		cm.RegisterCounter(fmt.Sprintf("overflow_counter_%d", i), counter, nil)
	}

	// Start collection
	err := cm.Start()
	if err != nil {
		t.Fatalf("Failed to start collection manager: %v", err)
	}
	defer cm.Stop()

	// Let it run and potentially overflow
	time.Sleep(time.Millisecond * 50)

	// Check for collection errors (dropped events)
	stats := cm.GetStats()
	// We expect some errors due to channel overflow
	// This test verifies the system handles overflow gracefully
	if stats.TotalEvents == 0 {
		t.Error("Expected some events to be generated")
	}
}
