package metrics

import (
	"fmt"
	"testing"

	"neuralmetergo/internal/metrics"
)

func TestTagSet_Creation(t *testing.T) {
	t.Run("NewTagSet with nil map", func(t *testing.T) {
		ts := metrics.NewTagSet(nil)
		if ts == nil {
			t.Fatal("Expected non-nil TagSet")
		}
		if ts.Len() != 0 {
			t.<PERSON><PERSON>("Expected empty TagSet, got length %d", ts.Len())
		}
	})

	t.Run("NewTagSet with empty map", func(t *testing.T) {
		ts := metrics.NewTagSet(map[string]string{})
		if ts.Len() != 0 {
			t.<PERSON><PERSON>("Expected empty TagSet, got length %d", ts.Len())
		}
	})

	t.Run("NewTagSet with tags", func(t *testing.T) {
		tags := map[string]string{
			"service": "api",
			"env":     "prod",
			"region":  "us-east-1",
		}
		ts := metrics.NewTagSet(tags)
		if ts.<PERSON>() != 3 {
			t.<PERSON><PERSON>("Expected TagSet length 3, got %d", ts.Len())
		}

		// Verify tags are accessible
		if val, ok := ts.Get("service"); !ok || val != "api" {
			t.Errorf("Expected service=api, got %s (exists: %t)", val, ok)
		}
		if val, ok := ts.Get("env"); !ok || val != "prod" {
			t.Errorf("Expected env=prod, got %s (exists: %t)", val, ok)
		}
		if val, ok := ts.Get("region"); !ok || val != "us-east-1" {
			t.Errorf("Expected region=us-east-1, got %s (exists: %t)", val, ok)
		}
	})

	t.Run("NewTagSet filters empty keys", func(t *testing.T) {
		tags := map[string]string{
			"service": "api",
			"":        "empty_key",
			"env":     "prod",
		}
		ts := metrics.NewTagSet(tags)
		if ts.Len() != 2 {
			t.Errorf("Expected TagSet length 2 (empty key filtered), got %d", ts.Len())
		}
		if ts.Has("") {
			t.Error("Expected empty key to be filtered out")
		}
	})

	t.Run("NewTagSetFromPairs", func(t *testing.T) {
		ts := metrics.NewTagSetFromPairs("service", "api", "env", "prod")
		if ts.Len() != 2 {
			t.Errorf("Expected TagSet length 2, got %d", ts.Len())
		}
		if val, ok := ts.Get("service"); !ok || val != "api" {
			t.Errorf("Expected service=api, got %s (exists: %t)", val, ok)
		}
		if val, ok := ts.Get("env"); !ok || val != "prod" {
			t.Errorf("Expected env=prod, got %s (exists: %t)", val, ok)
		}
	})

	t.Run("NewTagSetFromPairs with odd count", func(t *testing.T) {
		ts := metrics.NewTagSetFromPairs("service", "api", "env")
		if ts.Len() != 1 {
			t.Errorf("Expected TagSet length 1 (odd element dropped), got %d", ts.Len())
		}
		if val, ok := ts.Get("service"); !ok || val != "api" {
			t.Errorf("Expected service=api, got %s (exists: %t)", val, ok)
		}
		if ts.Has("env") {
			t.Error("Expected incomplete pair to be dropped")
		}
	})
}

func TestTagSet_Operations(t *testing.T) {
	baseTagSet := metrics.NewTagSet(map[string]string{
		"service": "api",
		"env":     "prod",
	})

	t.Run("Get existing key", func(t *testing.T) {
		val, ok := baseTagSet.Get("service")
		if !ok {
			t.Error("Expected key to exist")
		}
		if val != "api" {
			t.Errorf("Expected value 'api', got '%s'", val)
		}
	})

	t.Run("Get non-existing key", func(t *testing.T) {
		val, ok := baseTagSet.Get("nonexistent")
		if ok {
			t.Error("Expected key to not exist")
		}
		if val != "" {
			t.Errorf("Expected empty value for non-existing key, got '%s'", val)
		}
	})

	t.Run("Has existing key", func(t *testing.T) {
		if !baseTagSet.Has("service") {
			t.Error("Expected key to exist")
		}
	})

	t.Run("Has non-existing key", func(t *testing.T) {
		if baseTagSet.Has("nonexistent") {
			t.Error("Expected key to not exist")
		}
	})

	t.Run("Keys returns sorted keys", func(t *testing.T) {
		keys := baseTagSet.Keys()
		expectedKeys := []string{"env", "service"}
		if len(keys) != len(expectedKeys) {
			t.Errorf("Expected %d keys, got %d", len(expectedKeys), len(keys))
		}
		for i, key := range keys {
			if key != expectedKeys[i] {
				t.Errorf("Expected key[%d] = %s, got %s", i, expectedKeys[i], key)
			}
		}
	})

	t.Run("ToMap returns copy", func(t *testing.T) {
		tagMap := baseTagSet.ToMap()
		if len(tagMap) != 2 {
			t.Errorf("Expected 2 tags in map, got %d", len(tagMap))
		}
		if tagMap["service"] != "api" {
			t.Errorf("Expected service=api, got %s", tagMap["service"])
		}
		if tagMap["env"] != "prod" {
			t.Errorf("Expected env=prod, got %s", tagMap["env"])
		}

		// Modify the returned map to ensure it's a copy
		tagMap["service"] = "modified"
		if val, _ := baseTagSet.Get("service"); val == "modified" {
			t.Error("TagSet was modified when returned map was changed")
		}
	})

	t.Run("String representation", func(t *testing.T) {
		str := baseTagSet.String()
		// Should be sorted: {env=prod, service=api}
		expected := "{env=prod, service=api}"
		if str != expected {
			t.Errorf("Expected string representation '%s', got '%s'", expected, str)
		}
	})

	t.Run("Empty TagSet string", func(t *testing.T) {
		empty := metrics.NewTagSet(nil)
		str := empty.String()
		if str != "{}" {
			t.Errorf("Expected empty TagSet string '{}', got '%s'", str)
		}
	})
}

func TestTagSet_Immutability(t *testing.T) {
	original := metrics.NewTagSet(map[string]string{
		"service": "api",
		"env":     "prod",
	})

	t.Run("With adds tag without modifying original", func(t *testing.T) {
		modified := original.With("region", "us-east-1")

		// Original should be unchanged
		if original.Len() != 2 {
			t.Errorf("Original TagSet was modified, expected length 2, got %d", original.Len())
		}
		if original.Has("region") {
			t.Error("Original TagSet was modified, should not have 'region' key")
		}

		// Modified should have new tag
		if modified.Len() != 3 {
			t.Errorf("Modified TagSet should have length 3, got %d", modified.Len())
		}
		if val, ok := modified.Get("region"); !ok || val != "us-east-1" {
			t.Errorf("Expected region=us-east-1, got %s (exists: %t)", val, ok)
		}
		// Should still have original tags
		if val, ok := modified.Get("service"); !ok || val != "api" {
			t.Errorf("Expected service=api, got %s (exists: %t)", val, ok)
		}
	})

	t.Run("With empty key returns same TagSet", func(t *testing.T) {
		modified := original.With("", "empty")
		if modified != original {
			t.Error("Expected same TagSet when adding empty key")
		}
	})

	t.Run("Without removes tag without modifying original", func(t *testing.T) {
		modified := original.Without("env")

		// Original should be unchanged
		if original.Len() != 2 {
			t.Errorf("Original TagSet was modified, expected length 2, got %d", original.Len())
		}
		if !original.Has("env") {
			t.Error("Original TagSet was modified, should still have 'env' key")
		}

		// Modified should not have removed tag
		if modified.Len() != 1 {
			t.Errorf("Modified TagSet should have length 1, got %d", modified.Len())
		}
		if modified.Has("env") {
			t.Error("Modified TagSet should not have 'env' key")
		}
		// Should still have other tags
		if val, ok := modified.Get("service"); !ok || val != "api" {
			t.Errorf("Expected service=api, got %s (exists: %t)", val, ok)
		}
	})

	t.Run("Without non-existing key returns same TagSet", func(t *testing.T) {
		modified := original.Without("nonexistent")
		if modified != original {
			t.Error("Expected same TagSet when removing non-existing key")
		}
	})

	t.Run("Merge combines TagSets", func(t *testing.T) {
		other := metrics.NewTagSet(map[string]string{
			"region":  "us-west-2",
			"version": "1.0.0",
			"env":     "staging", // Should override original
		})

		merged := original.Merge(other)

		// Original should be unchanged
		if original.Len() != 2 {
			t.Errorf("Original TagSet was modified, expected length 2, got %d", original.Len())
		}

		// Merged should have all tags with other taking precedence
		if merged.Len() != 4 {
			t.Errorf("Merged TagSet should have length 4, got %d", merged.Len())
		}
		if val, ok := merged.Get("env"); !ok || val != "staging" {
			t.Errorf("Expected env=staging (from other), got %s (exists: %t)", val, ok)
		}
		if val, ok := merged.Get("service"); !ok || val != "api" {
			t.Errorf("Expected service=api (from original), got %s (exists: %t)", val, ok)
		}
		if val, ok := merged.Get("region"); !ok || val != "us-west-2" {
			t.Errorf("Expected region=us-west-2 (from other), got %s (exists: %t)", val, ok)
		}
		if val, ok := merged.Get("version"); !ok || val != "1.0.0" {
			t.Errorf("Expected version=1.0.0 (from other), got %s (exists: %t)", val, ok)
		}
	})
}

func TestTagSet_Equality(t *testing.T) {
	t.Run("Equal TagSets", func(t *testing.T) {
		ts1 := metrics.NewTagSet(map[string]string{
			"service": "api",
			"env":     "prod",
		})
		ts2 := metrics.NewTagSet(map[string]string{
			"env":     "prod",
			"service": "api", // Different order
		})

		if !ts1.Equals(ts2) {
			t.Error("Expected TagSets to be equal")
		}
		if !ts2.Equals(ts1) {
			t.Error("Expected TagSets to be equal (symmetric)")
		}
	})

	t.Run("Self equality", func(t *testing.T) {
		ts := metrics.NewTagSet(map[string]string{"service": "api"})
		if !ts.Equals(ts) {
			t.Error("Expected TagSet to equal itself")
		}
	})

	t.Run("Nil TagSet", func(t *testing.T) {
		ts := metrics.NewTagSet(map[string]string{"service": "api"})
		if ts.Equals(nil) {
			t.Error("Expected TagSet to not equal nil")
		}
	})

	t.Run("Different lengths", func(t *testing.T) {
		ts1 := metrics.NewTagSet(map[string]string{
			"service": "api",
			"env":     "prod",
		})
		ts2 := metrics.NewTagSet(map[string]string{
			"service": "api",
		})

		if ts1.Equals(ts2) {
			t.Error("Expected TagSets with different lengths to not be equal")
		}
	})

	t.Run("Different values", func(t *testing.T) {
		ts1 := metrics.NewTagSet(map[string]string{
			"service": "api",
			"env":     "prod",
		})
		ts2 := metrics.NewTagSet(map[string]string{
			"service": "api",
			"env":     "staging",
		})

		if ts1.Equals(ts2) {
			t.Error("Expected TagSets with different values to not be equal")
		}
	})

	t.Run("Different keys", func(t *testing.T) {
		ts1 := metrics.NewTagSet(map[string]string{
			"service": "api",
			"env":     "prod",
		})
		ts2 := metrics.NewTagSet(map[string]string{
			"service": "api",
			"region":  "us-east-1",
		})

		if ts1.Equals(ts2) {
			t.Error("Expected TagSets with different keys to not be equal")
		}
	})
}

func TestTagFilter_Creation(t *testing.T) {
	t.Run("NewTagFilter creates AND filter", func(t *testing.T) {
		filter := metrics.NewTagFilter()
		if filter == nil {
			t.Fatal("Expected non-nil filter")
		}
	})

	t.Run("NewTagFilterOR creates OR filter", func(t *testing.T) {
		filter := metrics.NewTagFilterOR()
		if filter == nil {
			t.Fatal("Expected non-nil filter")
		}
	})
}

func TestTagFilter_Conditions(t *testing.T) {
	tags := metrics.NewTagSet(map[string]string{
		"service": "api",
		"env":     "production",
		"region":  "us-east-1",
		"version": "1.2.3",
	})

	t.Run("Equals condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().Equals("service", "api")
		if !filter.Matches(tags) {
			t.Error("Expected filter to match tags")
		}

		filter = metrics.NewTagFilter().Equals("service", "web")
		if filter.Matches(tags) {
			t.Error("Expected filter to not match tags")
		}
	})

	t.Run("NotEquals condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().NotEquals("service", "web")
		if !filter.Matches(tags) {
			t.Error("Expected filter to match tags")
		}

		filter = metrics.NewTagFilter().NotEquals("service", "api")
		if filter.Matches(tags) {
			t.Error("Expected filter to not match tags")
		}
	})

	t.Run("Exists condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().Exists("service")
		if !filter.Matches(tags) {
			t.Error("Expected filter to match tags")
		}

		filter = metrics.NewTagFilter().Exists("nonexistent")
		if filter.Matches(tags) {
			t.Error("Expected filter to not match tags")
		}
	})

	t.Run("NotExists condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().NotExists("nonexistent")
		if !filter.Matches(tags) {
			t.Error("Expected filter to match tags")
		}

		filter = metrics.NewTagFilter().NotExists("service")
		if filter.Matches(tags) {
			t.Error("Expected filter to not match tags")
		}
	})

	t.Run("Contains condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().Contains("env", "prod")
		if !filter.Matches(tags) {
			t.Error("Expected filter to match tags")
		}

		filter = metrics.NewTagFilter().Contains("env", "staging")
		if filter.Matches(tags) {
			t.Error("Expected filter to not match tags")
		}
	})

	t.Run("StartsWith condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().StartsWith("region", "us-")
		if !filter.Matches(tags) {
			t.Error("Expected filter to match tags")
		}

		filter = metrics.NewTagFilter().StartsWith("region", "eu-")
		if filter.Matches(tags) {
			t.Error("Expected filter to not match tags")
		}
	})

	t.Run("EndsWith condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().EndsWith("region", "-1")
		if !filter.Matches(tags) {
			t.Error("Expected filter to match tags")
		}

		filter = metrics.NewTagFilter().EndsWith("region", "-2")
		if filter.Matches(tags) {
			t.Error("Expected filter to not match tags")
		}
	})
}

func TestTagFilter_Logic(t *testing.T) {
	tags := metrics.NewTagSet(map[string]string{
		"service": "api",
		"env":     "prod",
		"region":  "us-east-1",
	})

	t.Run("AND logic - all conditions must match", func(t *testing.T) {
		filter := metrics.NewTagFilter().
			Equals("service", "api").
			Equals("env", "prod")
		if !filter.Matches(tags) {
			t.Error("Expected AND filter to match when all conditions are true")
		}

		filter = metrics.NewTagFilter().
			Equals("service", "api").
			Equals("env", "staging")
		if filter.Matches(tags) {
			t.Error("Expected AND filter to not match when one condition is false")
		}
	})

	t.Run("OR logic - any condition can match", func(t *testing.T) {
		filter := metrics.NewTagFilterOR().
			Equals("service", "api").
			Equals("env", "staging")
		if !filter.Matches(tags) {
			t.Error("Expected OR filter to match when one condition is true")
		}

		filter = metrics.NewTagFilterOR().
			Equals("service", "web").
			Equals("env", "staging")
		if filter.Matches(tags) {
			t.Error("Expected OR filter to not match when all conditions are false")
		}
	})

	t.Run("Complex filter combinations", func(t *testing.T) {
		// service=api AND (env=prod OR env=staging)
		// This requires creating two separate filters since we don't have nested logic
		filter1 := metrics.NewTagFilter().
			Equals("service", "api").
			Equals("env", "prod")
		filter2 := metrics.NewTagFilter().
			Equals("service", "api").
			Equals("env", "staging")

		// Test prod environment
		if !filter1.Matches(tags) {
			t.Error("Expected filter1 to match prod environment")
		}
		if filter2.Matches(tags) {
			t.Error("Expected filter2 to not match prod environment")
		}

		// Test with staging environment
		stagingTags := metrics.NewTagSet(map[string]string{
			"service": "api",
			"env":     "staging",
			"region":  "us-east-1",
		})
		if filter1.Matches(stagingTags) {
			t.Error("Expected filter1 to not match staging environment")
		}
		if !filter2.Matches(stagingTags) {
			t.Error("Expected filter2 to match staging environment")
		}
	})
}

func TestTagFilter_String(t *testing.T) {
	t.Run("Single condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().Equals("service", "api")
		str := filter.String()
		if str == "" {
			t.Error("Expected non-empty string representation")
		}
	})

	t.Run("Multiple conditions", func(t *testing.T) {
		filter := metrics.NewTagFilter().
			Equals("service", "api").
			Contains("env", "prod")
		str := filter.String()
		if str == "" {
			t.Error("Expected non-empty string representation")
		}
	})
}

func TestTagIndex_Operations(t *testing.T) {
	index := metrics.NewTagIndex()

	t.Run("Register and find metrics", func(t *testing.T) {
		tags1 := metrics.NewTagSet(map[string]string{
			"service": "api",
			"env":     "prod",
		})
		tags2 := metrics.NewTagSet(map[string]string{
			"service": "web",
			"env":     "prod",
		})
		tags3 := metrics.NewTagSet(map[string]string{
			"service": "api",
			"env":     "staging",
		})

		index.Register("metric1", tags1, "counter")
		index.Register("metric2", tags2, "gauge")
		index.Register("metric3", tags3, "histogram")

		// Find by exact tag match
		filter := metrics.NewTagFilter().Equals("service", "api")
		results := index.Find(filter)
		if len(results) != 2 {
			t.Errorf("Expected 2 metrics with service=api, got %d", len(results))
		}

		// Find by environment
		filter = metrics.NewTagFilter().Equals("env", "prod")
		results = index.Find(filter)
		if len(results) != 2 {
			t.Errorf("Expected 2 metrics with env=prod, got %d", len(results))
		}

		// Find by multiple conditions
		filter = metrics.NewTagFilter().
			Equals("service", "api").
			Equals("env", "prod")
		results = index.Find(filter)
		if len(results) != 1 {
			t.Errorf("Expected 1 metric with service=api AND env=prod, got %d", len(results))
		}
		if len(results) > 0 && results[0] != "metric1" {
			t.Errorf("Expected metric1, got %s", results[0])
		}
	})

	t.Run("FindByTag", func(t *testing.T) {
		results := index.FindByTag("service", "api")
		if len(results) != 2 {
			t.Errorf("Expected 2 metrics with service=api, got %d", len(results))
		}

		results = index.FindByTag("env", "staging")
		if len(results) != 1 {
			t.Errorf("Expected 1 metric with env=staging, got %d", len(results))
		}

		results = index.FindByTag("nonexistent", "value")
		if len(results) != 0 {
			t.Errorf("Expected 0 metrics with nonexistent tag, got %d", len(results))
		}
	})

	t.Run("GetTags and GetMetricType", func(t *testing.T) {
		tags := index.GetTags("metric1")
		if tags == nil {
			t.Fatal("Expected non-nil tags for metric1")
		}
		if val, ok := tags.Get("service"); !ok || val != "api" {
			t.Errorf("Expected service=api for metric1, got %s (exists: %t)", val, ok)
		}

		metricType := index.GetMetricType("metric1")
		if metricType != "counter" {
			t.Errorf("Expected metric type 'counter' for metric1, got %s", metricType)
		}

		// Non-existent metric
		tags = index.GetTags("nonexistent")
		if tags != nil {
			t.Error("Expected nil tags for non-existent metric")
		}

		metricType = index.GetMetricType("nonexistent")
		if metricType != "" {
			t.Errorf("Expected empty metric type for non-existent metric, got %s", metricType)
		}
	})

	t.Run("ListAll", func(t *testing.T) {
		all := index.ListAll()
		expectedCount := 3
		if len(all) != expectedCount {
			t.Errorf("Expected %d metrics in index, got %d", expectedCount, len(all))
		}
	})

	t.Run("Unregister", func(t *testing.T) {
		index.Unregister("metric2")

		// Should not find unregistered metric
		tags := index.GetTags("metric2")
		if tags != nil {
			t.Error("Expected nil tags for unregistered metric")
		}

		// Should still find other metrics
		filter := metrics.NewTagFilter().Equals("service", "api")
		results := index.Find(filter)
		if len(results) != 2 {
			t.Errorf("Expected 2 metrics with service=api after unregistering metric2, got %d", len(results))
		}

		all := index.ListAll()
		if len(all) != 2 {
			t.Errorf("Expected 2 metrics in index after unregistering, got %d", len(all))
		}
	})
}

func TestTagIndex_Concurrent(t *testing.T) {
	index := metrics.NewTagIndex()

	t.Run("Concurrent registration and lookup", func(t *testing.T) {
		const numGoroutines = 10
		const numOpsPerGoroutine = 100

		done := make(chan bool, numGoroutines)

		// Start multiple goroutines doing concurrent operations
		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				defer func() { done <- true }()

				for j := 0; j < numOpsPerGoroutine; j++ {
					metricName := fmt.Sprintf("metric_%d_%d", id, j)
					tags := metrics.NewTagSet(map[string]string{
						"service":  fmt.Sprintf("service_%d", id),
						"instance": fmt.Sprintf("instance_%d", j),
					})

					// Register metric
					index.Register(metricName, tags, "counter")

					// Immediately try to find it
					filter := metrics.NewTagFilter().Equals("service", fmt.Sprintf("service_%d", id))
					results := index.Find(filter)
					if len(results) == 0 {
						t.Errorf("Failed to find newly registered metric %s", metricName)
					}

					// Get tags
					retrievedTags := index.GetTags(metricName)
					if retrievedTags == nil {
						t.Errorf("Failed to get tags for metric %s", metricName)
					}
				}
			}(i)
		}

		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			<-done
		}

		// Verify final state
		all := index.ListAll()
		expectedCount := numGoroutines * numOpsPerGoroutine
		if len(all) != expectedCount {
			t.Errorf("Expected %d metrics after concurrent operations, got %d", expectedCount, len(all))
		}
	})
}

func TestTagFilter_Basic(t *testing.T) {
	tags := metrics.NewTagSet(map[string]string{
		"service": "api",
		"env":     "prod",
	})

	t.Run("Equals condition", func(t *testing.T) {
		filter := metrics.NewTagFilter().Equals("service", "api")
		if !filter.Matches(tags) {
			t.Error("Expected filter to match tags")
		}
	})
}

func TestTagIndex_Basic(t *testing.T) {
	index := metrics.NewTagIndex()

	t.Run("Register and find", func(t *testing.T) {
		tags := metrics.NewTagSet(map[string]string{
			"service": "api",
		})

		index.Register("metric1", tags, "counter")

		filter := metrics.NewTagFilter().Equals("service", "api")
		results := index.Find(filter)
		if len(results) != 1 {
			t.Errorf("Expected 1 metric, got %d", len(results))
		}
	})
}
