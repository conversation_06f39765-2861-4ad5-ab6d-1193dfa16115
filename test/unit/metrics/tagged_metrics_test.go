package metrics

import (
	"testing"

	"neuralmetergo/internal/metrics"
)

func TestTaggedCounter_Creation(t *testing.T) {
	t.Run("Create tagged counter with tags", func(t *testing.T) {
		tags := map[string]string{
			"service": "api",
			"env":     "prod",
		}
		counter := metrics.NewTaggedCounterWithTags("test_counter", tags)

		if counter == nil {
			t.Fatal("Expected non-nil tagged counter")
		}

		if counter.GetName() != "test_counter" {
			t.<PERSON>("Expected name 'test_counter', got %s", counter.GetName())
		}

		if counter.GetType() != "counter" {
			t.<PERSON>("Expected type 'counter', got %s", counter.GetType())
		}

		tagSet := counter.GetTags()
		if tagSet == nil {
			t.Fatal("Expected non-nil tag set")
		}

		if val, ok := tagSet.Get("service"); !ok || val != "api" {
			t.<PERSON>("Expected service=api, got %s (exists: %t)", val, ok)
		}
	})
}

func TestMetricRegistry_Operations(t *testing.T) {
	registry := metrics.NewMetricRegistry()

	t.Run("Register counter", func(t *testing.T) {
		tags := metrics.NewTagSet(map[string]string{
			"service": "api",
		})

		counter := registry.RegisterCounter("test_counter", tags)
		if counter == nil {
			t.Fatal("Expected non-nil counter")
		}

		retrieved := registry.GetCounter("test_counter")
		if retrieved == nil {
			t.Fatal("Expected to retrieve registered counter")
		}
	})
}
