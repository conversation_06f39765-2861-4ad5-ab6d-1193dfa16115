package metrics

import (
	"math"
	"testing"

	. "neuralmetergo/internal/metrics"
)

func TestMean(t *testing.T) {
	tests := []struct {
		name     string
		data     []float64
		expected float64
		wantErr  bool
	}{
		{
			name:     "empty dataset",
			data:     []float64{},
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "single value",
			data:     []float64{5.0},
			expected: 5.0,
			wantErr:  false,
		},
		{
			name:     "multiple values",
			data:     []float64{1, 2, 3, 4, 5},
			expected: 3.0,
			wantErr:  false,
		},
		{
			name:     "with negative values",
			data:     []float64{-2, -1, 0, 1, 2},
			expected: 0.0,
			wantErr:  false,
		},
		{
			name:     "with NaN values",
			data:     []float64{1, 2, math.NaN(), 4, 5},
			expected: 3.0,
			wantErr:  false,
		},
		{
			name:     "with infinite values",
			data:     []float64{1, 2, math.Inf(1), 4, 5},
			expected: 3.0,
			wantErr:  false,
		},
		{
			name:     "all NaN values",
			data:     []float64{math.NaN(), math.NaN()},
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "decimal values",
			data:     []float64{1.5, 2.5, 3.5},
			expected: 2.5,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := Mean(tt.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("Mean() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && math.Abs(result-tt.expected) > 1e-10 {
				t.Errorf("Mean() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMedian(t *testing.T) {
	tests := []struct {
		name     string
		data     []float64
		expected float64
		wantErr  bool
	}{
		{
			name:     "empty dataset",
			data:     []float64{},
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "single value",
			data:     []float64{5.0},
			expected: 5.0,
			wantErr:  false,
		},
		{
			name:     "odd number of values",
			data:     []float64{1, 3, 2, 5, 4},
			expected: 3.0,
			wantErr:  false,
		},
		{
			name:     "even number of values",
			data:     []float64{1, 2, 3, 4},
			expected: 2.5,
			wantErr:  false,
		},
		{
			name:     "with duplicates",
			data:     []float64{1, 2, 2, 3},
			expected: 2.0,
			wantErr:  false,
		},
		{
			name:     "with negative values",
			data:     []float64{-5, -1, 0, 1, 5},
			expected: 0.0,
			wantErr:  false,
		},
		{
			name:     "unsorted data",
			data:     []float64{5, 1, 9, 3, 7},
			expected: 5.0,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := Median(tt.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("Median() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && math.Abs(result-tt.expected) > 1e-10 {
				t.Errorf("Median() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMode(t *testing.T) {
	tests := []struct {
		name          string
		data          []float64
		expectedValue float64
		expectedFreq  int
		wantErr       bool
	}{
		{
			name:          "empty dataset",
			data:          []float64{},
			expectedValue: 0,
			expectedFreq:  0,
			wantErr:       true,
		},
		{
			name:          "single value",
			data:          []float64{5.0},
			expectedValue: 5.0,
			expectedFreq:  1,
			wantErr:       false,
		},
		{
			name:          "clear mode",
			data:          []float64{1, 2, 2, 3, 2, 4},
			expectedValue: 2.0,
			expectedFreq:  3,
			wantErr:       false,
		},
		{
			name:          "all same values",
			data:          []float64{5, 5, 5, 5},
			expectedValue: 5.0,
			expectedFreq:  4,
			wantErr:       false,
		},
		{
			name:          "no clear mode",
			data:          []float64{1, 2, 3, 4},
			expectedValue: 1.0, // First value will be returned
			expectedFreq:  1,
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			value, freq, err := Mode(tt.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("Mode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if math.Abs(value-tt.expectedValue) > 1e-10 {
					t.Errorf("Mode() value = %v, want %v", value, tt.expectedValue)
				}
				if freq != tt.expectedFreq {
					t.Errorf("Mode() frequency = %v, want %v", freq, tt.expectedFreq)
				}
			}
		})
	}
}

func TestMinMax(t *testing.T) {
	tests := []struct {
		name        string
		data        []float64
		expectedMin float64
		expectedMax float64
		wantErr     bool
	}{
		{
			name:        "empty dataset",
			data:        []float64{},
			expectedMin: 0,
			expectedMax: 0,
			wantErr:     true,
		},
		{
			name:        "single value",
			data:        []float64{5.0},
			expectedMin: 5.0,
			expectedMax: 5.0,
			wantErr:     false,
		},
		{
			name:        "positive values",
			data:        []float64{1, 5, 3, 9, 2},
			expectedMin: 1.0,
			expectedMax: 9.0,
			wantErr:     false,
		},
		{
			name:        "negative values",
			data:        []float64{-5, -1, -9, -2},
			expectedMin: -9.0,
			expectedMax: -1.0,
			wantErr:     false,
		},
		{
			name:        "mixed values",
			data:        []float64{-3, 0, 5, -7, 2},
			expectedMin: -7.0,
			expectedMax: 5.0,
			wantErr:     false,
		},
		{
			name:        "with duplicates",
			data:        []float64{2, 2, 2, 2},
			expectedMin: 2.0,
			expectedMax: 2.0,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			minResult, minErr := Min(tt.data)
			maxResult, maxErr := Max(tt.data)

			if (minErr != nil) != tt.wantErr {
				t.Errorf("Min() error = %v, wantErr %v", minErr, tt.wantErr)
			}
			if (maxErr != nil) != tt.wantErr {
				t.Errorf("Max() error = %v, wantErr %v", maxErr, tt.wantErr)
			}

			if !tt.wantErr {
				if math.Abs(minResult-tt.expectedMin) > 1e-10 {
					t.Errorf("Min() = %v, want %v", minResult, tt.expectedMin)
				}
				if math.Abs(maxResult-tt.expectedMax) > 1e-10 {
					t.Errorf("Max() = %v, want %v", maxResult, tt.expectedMax)
				}
			}
		})
	}
}

func TestRange(t *testing.T) {
	tests := []struct {
		name     string
		data     []float64
		expected float64
		wantErr  bool
	}{
		{
			name:     "empty dataset",
			data:     []float64{},
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "single value",
			data:     []float64{5.0},
			expected: 0.0,
			wantErr:  false,
		},
		{
			name:     "positive range",
			data:     []float64{1, 5, 3, 9, 2},
			expected: 8.0, // 9 - 1
			wantErr:  false,
		},
		{
			name:     "negative range",
			data:     []float64{-9, -5, -1},
			expected: 8.0, // -1 - (-9)
			wantErr:  false,
		},
		{
			name:     "mixed range",
			data:     []float64{-3, 0, 5, -7, 2},
			expected: 12.0, // 5 - (-7)
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := Range(tt.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("Range() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && math.Abs(result-tt.expected) > 1e-10 {
				t.Errorf("Range() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestVarianceAndStandardDeviation(t *testing.T) {
	tests := []struct {
		name        string
		data        []float64
		expectedVar float64
		expectedStd float64
		wantErr     bool
	}{
		{
			name:        "empty dataset",
			data:        []float64{},
			expectedVar: 0,
			expectedStd: 0,
			wantErr:     true,
		},
		{
			name:        "single value",
			data:        []float64{5.0},
			expectedVar: 0.0,
			expectedStd: 0.0,
			wantErr:     false,
		},
		{
			name:        "uniform values",
			data:        []float64{2, 2, 2, 2},
			expectedVar: 0.0,
			expectedStd: 0.0,
			wantErr:     false,
		},
		{
			name:        "simple case",
			data:        []float64{1, 2, 3, 4, 5},
			expectedVar: 2.0,
			expectedStd: math.Sqrt(2.0),
			wantErr:     false,
		},
		{
			name:        "with negative values",
			data:        []float64{-2, -1, 0, 1, 2},
			expectedVar: 2.0,
			expectedStd: math.Sqrt(2.0),
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			varResult, varErr := Variance(tt.data)
			stdResult, stdErr := StandardDeviation(tt.data)

			if (varErr != nil) != tt.wantErr {
				t.Errorf("Variance() error = %v, wantErr %v", varErr, tt.wantErr)
			}
			if (stdErr != nil) != tt.wantErr {
				t.Errorf("StandardDeviation() error = %v, wantErr %v", stdErr, tt.wantErr)
			}

			if !tt.wantErr {
				if math.Abs(varResult-tt.expectedVar) > 1e-10 {
					t.Errorf("Variance() = %v, want %v", varResult, tt.expectedVar)
				}
				if math.Abs(stdResult-tt.expectedStd) > 1e-10 {
					t.Errorf("StandardDeviation() = %v, want %v", stdResult, tt.expectedStd)
				}
			}
		})
	}
}

func TestSampleVarianceAndStandardDeviation(t *testing.T) {
	tests := []struct {
		name        string
		data        []float64
		expectedVar float64
		expectedStd float64
		wantErr     bool
	}{
		{
			name:        "insufficient data",
			data:        []float64{5.0},
			expectedVar: 0,
			expectedStd: 0,
			wantErr:     true,
		},
		{
			name:        "two values",
			data:        []float64{1, 3},
			expectedVar: 2.0, // ((1-2)^2 + (3-2)^2) / (2-1) = 2/1 = 2
			expectedStd: math.Sqrt(2.0),
			wantErr:     false,
		},
		{
			name:        "simple case",
			data:        []float64{1, 2, 3, 4, 5},
			expectedVar: 2.5, // Population variance * n/(n-1) = 2.0 * 5/4 = 2.5
			expectedStd: math.Sqrt(2.5),
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			varResult, varErr := SampleVariance(tt.data)
			stdResult, stdErr := SampleStandardDeviation(tt.data)

			if (varErr != nil) != tt.wantErr {
				t.Errorf("SampleVariance() error = %v, wantErr %v", varErr, tt.wantErr)
			}
			if (stdErr != nil) != tt.wantErr {
				t.Errorf("SampleStandardDeviation() error = %v, wantErr %v", stdErr, tt.wantErr)
			}

			if !tt.wantErr {
				if math.Abs(varResult-tt.expectedVar) > 1e-10 {
					t.Errorf("SampleVariance() = %v, want %v", varResult, tt.expectedVar)
				}
				if math.Abs(stdResult-tt.expectedStd) > 1e-10 {
					t.Errorf("SampleStandardDeviation() = %v, want %v", stdResult, tt.expectedStd)
				}
			}
		})
	}
}

func TestPercentile(t *testing.T) {
	data := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	tests := []struct {
		name       string
		percentile float64
		expected   float64
		wantErr    bool
	}{
		{
			name:       "0th percentile",
			percentile: 0,
			expected:   1.0,
			wantErr:    false,
		},
		{
			name:       "25th percentile",
			percentile: 25,
			expected:   3.25,
			wantErr:    false,
		},
		{
			name:       "50th percentile (median)",
			percentile: 50,
			expected:   5.5,
			wantErr:    false,
		},
		{
			name:       "75th percentile",
			percentile: 75,
			expected:   7.75,
			wantErr:    false,
		},
		{
			name:       "100th percentile",
			percentile: 100,
			expected:   10.0,
			wantErr:    false,
		},
		{
			name:       "invalid percentile (negative)",
			percentile: -1,
			expected:   0,
			wantErr:    true,
		},
		{
			name:       "invalid percentile (over 100)",
			percentile: 101,
			expected:   0,
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := Percentile(data, tt.percentile)
			if (err != nil) != tt.wantErr {
				t.Errorf("Percentile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && math.Abs(result-tt.expected) > 1e-10 {
				t.Errorf("Percentile() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestQuantiles(t *testing.T) {
	data := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	q1, q2, q3, err := Quantiles(data)
	if err != nil {
		t.Fatalf("Quantiles() error = %v", err)
	}

	expectedQ1 := 3.25
	expectedQ2 := 5.5 // median
	expectedQ3 := 7.75

	if math.Abs(q1-expectedQ1) > 1e-10 {
		t.Errorf("Q1 = %v, want %v", q1, expectedQ1)
	}
	if math.Abs(q2-expectedQ2) > 1e-10 {
		t.Errorf("Q2 = %v, want %v", q2, expectedQ2)
	}
	if math.Abs(q3-expectedQ3) > 1e-10 {
		t.Errorf("Q3 = %v, want %v", q3, expectedQ3)
	}
}

func TestInterquartileRange(t *testing.T) {
	data := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	result, err := InterquartileRange(data)
	if err != nil {
		t.Fatalf("InterquartileRange() error = %v", err)
	}

	expected := 4.5 // Q3 (7.75) - Q1 (3.25)
	if math.Abs(result-expected) > 1e-10 {
		t.Errorf("InterquartileRange() = %v, want %v", result, expected)
	}
}

func TestCalculateStatisticalSummary(t *testing.T) {
	data := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	summary, err := CalculateStatisticalSummary(data)
	if err != nil {
		t.Fatalf("CalculateStatisticalSummary() error = %v", err)
	}

	if summary.Count != 10 {
		t.Errorf("Count = %v, want %v", summary.Count, 10)
	}

	if math.Abs(summary.Mean-5.5) > 1e-10 {
		t.Errorf("Mean = %v, want %v", summary.Mean, 5.5)
	}

	if math.Abs(summary.Median-5.5) > 1e-10 {
		t.Errorf("Median = %v, want %v", summary.Median, 5.5)
	}

	if summary.Min != 1.0 {
		t.Errorf("Min = %v, want %v", summary.Min, 1.0)
	}

	if summary.Max != 10.0 {
		t.Errorf("Max = %v, want %v", summary.Max, 10.0)
	}

	if summary.Range != 9.0 {
		t.Errorf("Range = %v, want %v", summary.Range, 9.0)
	}

	expectedVariance := 8.25 // For 1-10 sequence
	if math.Abs(summary.Variance-expectedVariance) > 1e-10 {
		t.Errorf("Variance = %v, want %v", summary.Variance, expectedVariance)
	}

	if math.Abs(summary.StandardDev-math.Sqrt(expectedVariance)) > 1e-10 {
		t.Errorf("StandardDev = %v, want %v", summary.StandardDev, math.Sqrt(expectedVariance))
	}
}

func TestKnownStatisticalDistributions(t *testing.T) {
	// Test with a normal distribution approximation
	// Using a simple case where we know the expected results
	normalData := []float64{
		-2, -1.5, -1, -0.5, 0, 0.5, 1, 1.5, 2, // Approximately normal
	}

	summary, err := CalculateStatisticalSummary(normalData)
	if err != nil {
		t.Fatalf("CalculateStatisticalSummary() error = %v", err)
	}

	// For this symmetric distribution, mean should be close to 0
	if math.Abs(summary.Mean) > 0.1 {
		t.Errorf("Mean for symmetric distribution = %v, expected close to 0", summary.Mean)
	}

	// Median should also be close to 0
	if math.Abs(summary.Median) > 0.1 {
		t.Errorf("Median for symmetric distribution = %v, expected close to 0", summary.Median)
	}

	// Min and Max should be -2 and 2
	if summary.Min != -2.0 {
		t.Errorf("Min = %v, want %v", summary.Min, -2.0)
	}
	if summary.Max != 2.0 {
		t.Errorf("Max = %v, want %v", summary.Max, 2.0)
	}
}

func TestEdgeCases(t *testing.T) {
	t.Run("very large numbers", func(t *testing.T) {
		data := []float64{1e15, 1e15 + 1, 1e15 + 2}
		summary, err := CalculateStatisticalSummary(data)
		if err != nil {
			t.Fatalf("Large numbers failed: %v", err)
		}
		if summary.Count != 3 {
			t.Errorf("Count = %v, want 3", summary.Count)
		}
	})

	t.Run("very small numbers", func(t *testing.T) {
		data := []float64{1e-15, 2e-15, 3e-15}
		summary, err := CalculateStatisticalSummary(data)
		if err != nil {
			t.Fatalf("Small numbers failed: %v", err)
		}
		if summary.Count != 3 {
			t.Errorf("Count = %v, want 3", summary.Count)
		}
	})

	t.Run("mixed with invalid values", func(t *testing.T) {
		data := []float64{1, 2, math.NaN(), 4, math.Inf(1), 6}
		summary, err := CalculateStatisticalSummary(data)
		if err != nil {
			t.Fatalf("Mixed valid/invalid failed: %v", err)
		}
		if summary.Count != 4 { // Should filter out NaN and Inf
			t.Errorf("Count = %v, want 4", summary.Count)
		}
		expectedMean := 3.25 // (1+2+4+6)/4
		if math.Abs(summary.Mean-expectedMean) > 1e-10 {
			t.Errorf("Mean = %v, want %v", summary.Mean, expectedMean)
		}
	})
}

func TestStandardDeviation(t *testing.T) {
	data := []float64{1, 2, 3, 4, 5}

	result, err := StandardDeviation(data)
	if err != nil {
		t.Errorf("StandardDeviation() error = %v", err)
	}

	expected := math.Sqrt(2.0)
	if math.Abs(result-expected) > 1e-10 {
		t.Errorf("StandardDeviation() = %v, want %v", result, expected)
	}
}
