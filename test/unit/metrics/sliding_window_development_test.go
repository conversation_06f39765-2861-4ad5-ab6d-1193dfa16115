// Package metrics provides development-safe sliding window tests
package metrics

import (
	"math"
	"sync"
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// TestDevelopmentModeConfiguration verifies the test configuration system works correctly
func TestDevelopmentModeConfiguration(t *testing.T) {
	testConfig := GetCurrentTestConfig()

	t.Logf("Test scale: %s", testConfig.LogTestScale())

	// Verify development mode defaults are safe
	if testConfig.Mode == DevelopmentScale {
		if testConfig.MaxCapacity > 100 {
			t.Errorf("Development mode capacity too high: %d (should be ≤100)", testConfig.MaxCapacity)
		}

		if testConfig.MaxConcurrency > 5 {
			t.<PERSON><PERSON><PERSON>("Development mode concurrency too high: %d (should be ≤5)", testConfig.MaxConcurrency)
		}

		if testConfig.DefaultTimeout > time.Second*2 {
			t.<PERSON>rrorf("Development mode timeout too long: %v (should be ≤2s)", testConfig.DefaultTimeout)
		}
	}
}

// TestSafeSlidingWindowCountBased tests count-based windows with development-safe parameters
func TestSafeSlidingWindowCountBased(t *testing.T) {
	testConfig := GetCurrentTestConfig()
	config := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)

	t.Logf("Using safe configuration: capacity=%d, stats=%v", config.Capacity, config.EnableStats)

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Use small dataset appropriate for development
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0}

	for _, value := range values {
		err := window.Add(value)
		if err != nil {
			t.Errorf("Failed to add value %f: %v", value, err)
		}
	}

	if window.Size() != len(values) {
		t.Errorf("Expected size %d, got %d", len(values), window.Size())
	}

	// Basic statistics validation (if enabled)
	if config.EnableStats {
		mean := window.Mean()
		if math.IsNaN(mean) {
			t.Error("Mean should not be NaN")
		}

		expectedMean := 3.0 // (1+2+3+4+5)/5
		if math.Abs(mean-expectedMean) > 0.1 {
			t.Errorf("Expected mean approximately %f, got %f", expectedMean, mean)
		}
	}
}

// TestSafeSlidingWindowTimeBased tests time-based windows with short durations
func TestSafeSlidingWindowTimeBased(t *testing.T) {
	testConfig := GetCurrentTestConfig()
	config := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeTime)

	t.Logf("Using safe time-based config: duration=%v, cleanup=%v", config.Duration, config.CleanupTick)

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Add values with controlled timing
	now := time.Now()
	testValues := []struct {
		value  float64
		offset time.Duration
	}{
		{1.0, -time.Second * 3},        // Outside window
		{2.0, -time.Millisecond * 500}, // Inside window
		{3.0, 0},                       // Current time
	}

	for _, tv := range testValues {
		element := metrics.WindowElement{
			Value:     tv.value,
			Timestamp: now.Add(tv.offset),
		}
		window.Add(element.Value) // Add uses current time internally
	}

	// Allow brief time for any async cleanup (if enabled)
	time.Sleep(testConfig.CleanupInterval * 2)

	size := window.Size()
	if size <= 0 {
		t.Error("Expected window to contain some elements")
	}

	t.Logf("Window contains %d elements after time-based filtering", size)
}

// TestSafeConcurrentAccess tests concurrent access with development-safe parameters
func TestSafeConcurrentAccess(t *testing.T) {
	testConfig := GetCurrentTestConfig()

	// Skip heavy concurrency tests in development mode
	if testConfig.ShouldSkipTest("concurrent-heavy") {
		t.Skip("Skipping heavy concurrency test in development mode")
	}

	config := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	numGoroutines, valuesPerGoroutine := testConfig.CreateConcurrencyParams()
	t.Logf("Safe concurrency test: %d goroutines, %d values each", numGoroutines, valuesPerGoroutine)

	var wg sync.WaitGroup

	// Concurrent writers
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			for j := 0; j < valuesPerGoroutine; j++ {
				value := float64(goroutineID*valuesPerGoroutine + j)
				window.Add(value)
			}
		}(i)
	}

	// Concurrent readers (fewer than writers)
	for i := 0; i < numGoroutines/2; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < valuesPerGoroutine/2; j++ {
				_ = window.GetValues()
				_ = window.Size()
				// Skip expensive operations in development mode
				if testConfig.EnableStatistics {
					_ = window.Mean()
				}
			}
		}()
	}

	// Wait with timeout
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// Success
	case <-time.After(testConfig.GetTestTimeout()):
		t.Fatal("Concurrent test timed out")
	}

	finalSize := window.Size()
	expectedMaxSize := config.Capacity
	if finalSize > expectedMaxSize {
		t.Errorf("Window size %d exceeds capacity %d", finalSize, expectedMaxSize)
	}

	t.Logf("Final window size: %d (capacity: %d)", finalSize, expectedMaxSize)
}

// TestSafeStatisticsCalculation tests statistics with small datasets
func TestSafeStatisticsCalculation(t *testing.T) {
	testConfig := GetCurrentTestConfig()

	// Skip statistics tests if disabled
	if testConfig.ShouldSkipTest("statistics-heavy") {
		t.Skip("Skipping statistics test - disabled in current mode")
	}

	config := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)
	config.EnableStats = true // Force enable for this test

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Use small, predictable dataset
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0}

	for _, value := range values {
		err := window.Add(value)
		if err != nil {
			t.Errorf("Failed to add value %f: %v", value, err)
		}
	}

	stats := window.GetStatistics()

	// Validate basic statistics
	expectedMean := 5.5
	if math.Abs(stats.Mean-expectedMean) > 0.1 {
		t.Errorf("Expected mean %f, got %f", expectedMean, stats.Mean)
	}

	if stats.Count != uint64(len(values)) {
		t.Errorf("Expected count %d, got %d", len(values), stats.Count)
	}

	if stats.Min != 1.0 {
		t.Errorf("Expected min 1.0, got %f", stats.Min)
	}

	if stats.Max != 10.0 {
		t.Errorf("Expected max 10.0, got %f", stats.Max)
	}

	// Validate percentiles are calculated (but don't enforce exact values due to algorithm differences)
	// Different percentile algorithms (exact vs P2 vs TDigest) may produce different results
	if math.IsNaN(stats.P50) {
		t.Errorf("P50 should not be NaN")
	}

	if math.IsNaN(stats.P95) {
		t.Errorf("P95 should not be NaN")
	}

	// Just ensure percentiles are within the data range
	if stats.P50 < 1.0 || stats.P50 > 10.0 {
		t.Errorf("P50 (%f) outside data range 1-10", stats.P50)
	}

	if stats.P95 < 1.0 || stats.P95 > 10.0 {
		t.Errorf("P95 (%f) outside data range 1-10", stats.P95)
	}

	t.Logf("Statistics: Mean=%.2f, P50=%.2f, P95=%.2f", stats.Mean, stats.P50, stats.P95)
}

// TestSafeMemoryUsage tests memory usage with small datasets
func TestSafeMemoryUsage(t *testing.T) {
	testConfig := GetCurrentTestConfig()
	config := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Fill to capacity
	for i := 0; i < config.Capacity; i++ {
		window.Add(float64(i))
	}

	memUsage := window.MemoryUsage()

	// Memory usage should be reasonable for development
	maxReasonableMemory := 1024 * 10 // 10KB for small capacity
	if memUsage > maxReasonableMemory {
		t.Errorf("Memory usage %d bytes seems high for capacity %d", memUsage, config.Capacity)
	}

	t.Logf("Memory usage: %d bytes for capacity %d", memUsage, config.Capacity)
}

// TestSafeBenchmarkScaling demonstrates how to run benchmarks safely
func TestSafeBenchmarkScaling(t *testing.T) {
	testConfig := GetCurrentTestConfig()

	if testConfig.ShouldSkipTest("benchmark") {
		t.Skip("Skipping benchmark test in development mode")
	}

	config := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Simulate a benchmark with controlled iterations
	maxIterations := testConfig.GetBenchmarkN(1000000) // Will be limited in dev mode

	start := time.Now()
	for i := 0; i < maxIterations; i++ {
		window.Add(float64(i))
	}
	elapsed := time.Since(start)

	t.Logf("Added %d values in %v (%.2f ops/sec)",
		maxIterations, elapsed, float64(maxIterations)/elapsed.Seconds())

	// Ensure performance is reasonable for development
	if elapsed > time.Second*5 {
		t.Errorf("Benchmark took too long: %v (should be <5s for development)", elapsed)
	}
}

// TestConfigurableTimeout demonstrates timeout handling
func TestConfigurableTimeout(t *testing.T) {
	testConfig := GetCurrentTestConfig()

	// This test demonstrates how to use configurable timeouts
	timeout := testConfig.GetTestTimeout()

	done := make(chan bool, 1)

	go func() {
		// Simulate some work
		time.Sleep(time.Millisecond * 100)
		done <- true
	}()

	select {
	case <-done:
		t.Log("Operation completed within timeout")
	case <-time.After(timeout):
		t.Errorf("Operation timed out after %v", timeout)
	}
}

// BenchmarkSafeSlidingWindowAdd demonstrates safe benchmark practices
func BenchmarkSafeSlidingWindowAdd(b *testing.B) {
	testConfig := GetCurrentTestConfig()

	if testConfig.ShouldSkipTest("benchmark") {
		b.Skip("Skipping benchmark in development mode")
	}

	config := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)
	config.EnableStats = false // Disable stats for pure performance testing

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		b.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	b.ResetTimer()

	// Use the safe iteration count
	n := testConfig.GetBenchmarkN(b.N)
	for i := 0; i < n; i++ {
		window.Add(float64(i))
	}
}
