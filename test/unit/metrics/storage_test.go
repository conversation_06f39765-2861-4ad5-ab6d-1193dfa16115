package metrics

import (
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

func TestMetricStorage_Creation(t *testing.T) {
	config := metrics.DefaultStorageConfig()
	storage := metrics.NewMetricStorage(config)

	if storage == nil {
		t.Fatal("Expected storage to be created")
	}

	stats := storage.GetStats()
	if stats.TotalMemoryUsage != 0 {
		t.<PERSON>rrorf("Expected initial memory usage to be 0, got %d", stats.TotalMemoryUsage)
	}
}

func TestMetricStorage_StartStop(t *testing.T) {
	config := metrics.DefaultStorageConfig()
	storage := metrics.NewMetricStorage(config)

	// Test start
	err := storage.Start()
	if err != nil {
		t.Fatalf("Failed to start storage: %v", err)
	}

	// Test stop
	err = storage.Stop()
	if err != nil {
		t.Fatalf("Failed to stop storage: %v", err)
	}

	// Test multiple starts/stops
	storage.Start()
	storage.Start() // Should not error
	storage.Stop()
	storage.Stop() // Should not error
}

func TestMetricStorage_Store(t *testing.T) {
	config := metrics.DefaultStorageConfig()
	storage := metrics.NewMetricStorage(config)
	storage.Start()
	defer storage.Stop()

	// Create tags
	tags := metrics.NewTagSetFromPairs("service", "test", "env", "dev")

	// Store entry
	err := storage.Store("test_metric", 42.0, *tags, time.Minute)
	if err != nil {
		t.Fatalf("Failed to store entry: %v", err)
	}

	stats := storage.GetStats()
	if stats.TotalMemoryUsage == 0 {
		t.Error("Expected memory usage to be greater than 0")
	}
}

func TestMetricStorage_Retrieve(t *testing.T) {
	config := metrics.DefaultStorageConfig()
	storage := metrics.NewMetricStorage(config)
	storage.Start()
	defer storage.Stop()

	// Store entries
	tags := metrics.NewTagSetFromPairs("service", "test")

	storage.Store("metric1", 42.0, *tags, time.Minute)
	storage.Store("metric1", 43.0, *tags, time.Minute)
	storage.Store("metric2", 44.0, *tags, time.Minute)

	// Retrieve entries
	entries, err := storage.Retrieve("metric1", 0)
	if err != nil {
		t.Fatalf("Failed to retrieve entries: %v", err)
	}

	if len(entries) != 2 {
		t.Errorf("Expected 2 entries, got %d", len(entries))
	}

	// Test non-existent key
	_, err = storage.Retrieve("nonexistent", 0)
	if err == nil {
		t.Error("Expected error for non-existent key")
	}
}

func TestMetricStorage_RetrieveByTags(t *testing.T) {
	config := metrics.DefaultStorageConfig()
	storage := metrics.NewMetricStorage(config)
	storage.Start()
	defer storage.Stop()

	// Store entries with different tags
	tags1 := metrics.NewTagSetFromPairs("service", "web", "env", "prod")
	tags2 := metrics.NewTagSetFromPairs("service", "api", "env", "prod")
	tags3 := metrics.NewTagSetFromPairs("service", "web", "env", "dev")

	storage.Store("metric1", 1.0, *tags1, time.Minute)
	storage.Store("metric2", 2.0, *tags2, time.Minute)
	storage.Store("metric3", 3.0, *tags3, time.Minute)

	// Create filter for prod environment
	filter := metrics.NewTagFilter().Equals("env", "prod")

	entries, err := storage.RetrieveByTags(filter, 0)
	if err != nil {
		t.Fatalf("Failed to retrieve by tags: %v", err)
	}

	if len(entries) != 2 {
		t.Errorf("Expected 2 entries for prod env, got %d", len(entries))
	}
}

func TestMetricStorage_MemoryLimit(t *testing.T) {
	config := metrics.DefaultStorageConfig()
	config.MaxMemoryUsage = 1024 // Very small limit
	storage := metrics.NewMetricStorage(config)
	storage.Start()
	defer storage.Stop()

	tags := metrics.NewTagSet(make(map[string]string))

	// Store many entries to exceed limit
	for i := 0; i < 100; i++ {
		err := storage.Store("test_metric", "large_value_that_takes_up_memory", *tags, time.Minute)
		if err != nil {
			// Expected to hit memory limit
			break
		}
	}

	stats := storage.GetStats()
	// Allow some tolerance since we check limits before adding entries
	tolerance := int64(200) // Allow 200 bytes tolerance
	if stats.TotalMemoryUsage > config.MaxMemoryUsage+tolerance {
		t.Errorf("Memory usage %d significantly exceeded limit %d (tolerance: %d)", stats.TotalMemoryUsage, config.MaxMemoryUsage, tolerance)
	}
}

func TestMemoryPool_Basic(t *testing.T) {
	pool := metrics.NewMemoryPool(100)

	// Get object from pool
	obj := pool.Get()
	if obj == nil {
		t.Fatal("Expected object from pool")
	}

	// Put object back
	pool.Put(obj)

	stats := pool.GetStats()
	if stats.Size != 100 {
		t.Errorf("Expected pool size 100, got %d", stats.Size)
	}
}

func TestMemoryPool_Concurrency(t *testing.T) {
	pool := metrics.NewMemoryPool(100)
	var wg sync.WaitGroup

	// Test concurrent access
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				obj := pool.Get()
				pool.Put(obj)
			}
		}()
	}

	wg.Wait()

	stats := pool.GetStats()
	if stats.Hits+stats.Misses == 0 {
		t.Error("Expected some pool operations")
	}
}

func TestStorageBuffer_Basic(t *testing.T) {
	pool := metrics.NewMemoryPool(100)
	buffer := metrics.NewStorageBuffer(10, pool)

	// Test empty buffer
	if !buffer.IsEmpty() {
		t.Error("Expected buffer to be empty")
	}

	if buffer.Size() != 0 {
		t.Errorf("Expected size 0, got %d", buffer.Size())
	}

	// Add entries
	tags := metrics.NewTagSet(make(map[string]string))
	entry := &metrics.StorageEntry{
		Key:       "test",
		Value:     42.0,
		Tags:      *tags,
		Timestamp: time.Now(),
	}

	err := buffer.Add(entry)
	if err != nil {
		t.Fatalf("Failed to add entry: %v", err)
	}

	if buffer.IsEmpty() {
		t.Error("Expected buffer to not be empty")
	}

	if buffer.Size() != 1 {
		t.Errorf("Expected size 1, got %d", buffer.Size())
	}
}

func TestStorageBuffer_CircularBehavior(t *testing.T) {
	pool := metrics.NewMemoryPool(100)
	buffer := metrics.NewStorageBuffer(3, pool) // Small buffer

	tags := metrics.NewTagSet(make(map[string]string))

	// Add more entries than buffer size
	for i := 0; i < 5; i++ {
		entry := &metrics.StorageEntry{
			Key:       "test",
			Value:     float64(i),
			Tags:      *tags,
			Timestamp: time.Now(),
		}
		buffer.Add(entry)
	}

	// Buffer should contain only 3 entries (latest ones)
	if buffer.Size() != 3 {
		t.Errorf("Expected size 3, got %d", buffer.Size())
	}

	entries := buffer.GetAll()
	if len(entries) != 3 {
		t.Errorf("Expected 3 entries, got %d", len(entries))
	}

	// Should contain values 2, 3, 4 (oldest 0, 1 were evicted)
	values := make([]float64, len(entries))
	for i, entry := range entries {
		values[i] = entry.Value.(float64)
	}

	expectedValues := []float64{2.0, 3.0, 4.0}
	for i, expected := range expectedValues {
		if values[i] != expected {
			t.Errorf("Expected value %f at position %d, got %f", expected, i, values[i])
		}
	}
}

func TestStorageBuffer_GetLatest(t *testing.T) {
	pool := metrics.NewMemoryPool(100)
	buffer := metrics.NewStorageBuffer(10, pool)

	tags := metrics.NewTagSet(make(map[string]string))

	// Add entries
	for i := 0; i < 5; i++ {
		entry := &metrics.StorageEntry{
			Key:       "test",
			Value:     float64(i),
			Tags:      *tags,
			Timestamp: time.Now(),
		}
		buffer.Add(entry)
	}

	// Get latest 3 entries
	latest := buffer.GetLatest(3)
	if len(latest) != 3 {
		t.Errorf("Expected 3 latest entries, got %d", len(latest))
	}

	// Should be in reverse order (newest first)
	expectedValues := []float64{4.0, 3.0, 2.0}
	for i, expected := range expectedValues {
		if latest[i].Value.(float64) != expected {
			t.Errorf("Expected value %f at position %d, got %f", expected, i, latest[i].Value.(float64))
		}
	}
}

func TestStorageBuffer_Cleanup(t *testing.T) {
	pool := metrics.NewMemoryPool(100)
	buffer := metrics.NewStorageBuffer(10, pool)

	tags := metrics.NewTagSet(make(map[string]string))
	now := time.Now()

	// Add entries with different TTLs
	entries := []*metrics.StorageEntry{
		{
			Key:       "test1",
			Value:     1.0,
			Tags:      *tags,
			Timestamp: now.Add(-2 * time.Minute),
			TTL:       time.Minute, // Expired
		},
		{
			Key:       "test2",
			Value:     2.0,
			Tags:      *tags,
			Timestamp: now,
			TTL:       time.Minute, // Not expired
		},
	}

	for _, entry := range entries {
		buffer.Add(entry)
	}

	// Cleanup expired entries
	freed := buffer.Cleanup(now)
	if freed == 0 {
		t.Error("Expected some memory to be freed")
	}

	// Should have only 1 entry left
	if buffer.Size() != 1 {
		t.Errorf("Expected 1 entry after cleanup, got %d", buffer.Size())
	}
}

func TestCompressionManager_Basic(t *testing.T) {
	cm := metrics.NewCompressionManager(6)

	tags := metrics.NewTagSet(make(map[string]string))
	entry := &metrics.StorageEntry{
		Key:       "test",
		Value:     "This is a test string that should compress well",
		Tags:      *tags,
		Timestamp: time.Now(),
	}

	// Compress entry
	compressed, err := cm.Compress(entry)
	if err != nil {
		t.Fatalf("Failed to compress entry: %v", err)
	}

	if !compressed.Compressed {
		t.Error("Expected entry to be marked as compressed")
	}

	// Decompress entry
	decompressed, err := cm.Decompress(compressed)
	if err != nil {
		t.Fatalf("Failed to decompress entry: %v", err)
	}

	if decompressed.Compressed {
		t.Error("Expected entry to be marked as not compressed")
	}

	if decompressed.Value.(string) != entry.Value.(string) {
		t.Error("Decompressed value does not match original")
	}
}

func TestCompressionManager_Batch(t *testing.T) {
	cm := metrics.NewCompressionManager(6)
	tags := metrics.NewTagSet(make(map[string]string))

	// Create multiple entries
	entries := make([]*metrics.StorageEntry, 5)
	for i := 0; i < 5; i++ {
		entries[i] = &metrics.StorageEntry{
			Key:       "test",
			Value:     "Test data for compression",
			Tags:      *tags,
			Timestamp: time.Now(),
		}
	}

	// Compress batch
	compressed, err := cm.CompressBatch(entries)
	if err != nil {
		t.Fatalf("Failed to compress batch: %v", err)
	}

	if len(compressed) != len(entries) {
		t.Errorf("Expected %d compressed entries, got %d", len(entries), len(compressed))
	}

	// Decompress batch
	decompressed, err := cm.DecompressBatch(compressed)
	if err != nil {
		t.Fatalf("Failed to decompress batch: %v", err)
	}

	if len(decompressed) != len(entries) {
		t.Errorf("Expected %d decompressed entries, got %d", len(entries), len(decompressed))
	}
}

func TestCompressionManager_Stats(t *testing.T) {
	cm := metrics.NewCompressionManager(6)
	tags := metrics.NewTagSet(make(map[string]string))

	// Use larger, repetitive data that compresses well
	largeData := strings.Repeat("This is test data for compression statistics. ", 100)
	entry := &metrics.StorageEntry{
		Key:       "test",
		Value:     largeData,
		Tags:      *tags,
		Timestamp: time.Now(),
	}

	// Compress entry
	_, err := cm.Compress(entry)
	if err != nil {
		t.Fatalf("Failed to compress entry: %v", err)
	}

	stats := cm.GetStats()
	if stats.CompressedBytes == 0 {
		t.Error("Expected compressed bytes to be greater than 0")
	}

	if stats.UncompressedBytes == 0 {
		t.Error("Expected uncompressed bytes to be greater than 0")
	}

	if stats.CompressionRatio <= 0 {
		t.Error("Expected positive compression ratio")
	}
}

func TestPersistenceManager_Basic(t *testing.T) {
	tempDir := t.TempDir()
	pm := metrics.NewPersistenceManager(tempDir, 24*time.Hour)

	err := pm.Start()
	if err != nil {
		t.Fatalf("Failed to start persistence manager: %v", err)
	}
	defer pm.Stop()

	// Test persistence
	tags := metrics.NewTagSet(make(map[string]string))
	entry := &metrics.StorageEntry{
		Key:       "test",
		Value:     42.0,
		Tags:      *tags,
		Timestamp: time.Now(),
	}

	pm.Persist(entry)

	// Wait a bit for async persistence
	time.Sleep(100 * time.Millisecond)

	stats := pm.GetStats()
	if stats.QueueSize > 1000 { // Should be reasonable
		t.Errorf("Queue size seems too large: %d", stats.QueueSize)
	}
}

func TestPersistenceManager_LoadSave(t *testing.T) {
	tempDir := t.TempDir()
	pm := metrics.NewPersistenceManager(tempDir, 24*time.Hour)

	err := pm.Start()
	if err != nil {
		t.Fatalf("Failed to start persistence manager: %v", err)
	}

	// Create test file manually
	testFile := filepath.Join(tempDir, "test.json")
	file, err := os.Create(testFile)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tags := metrics.NewTagSet(make(map[string]string))
	entry := &metrics.StorageEntry{
		Key:       "test_key",
		Value:     42.0,
		Tags:      *tags,
		Timestamp: time.Now(),
	}

	// Write test entry
	file.WriteString(`{"key":"test_key","value":42.0,"timestamp":"` + entry.Timestamp.Format(time.RFC3339) + `"}` + "\n")
	file.Close()

	// Load entries
	entries, err := pm.Load("test_key", 0)
	if err != nil {
		t.Fatalf("Failed to load entries: %v", err)
	}

	if len(entries) == 0 {
		t.Error("Expected at least one entry")
	}

	pm.Stop()
}

func TestStorageIntegration(t *testing.T) {
	// Test full integration with all components
	config := metrics.DefaultStorageConfig()
	config.CompressionEnabled = true
	config.PersistenceEnabled = true
	config.PersistencePath = t.TempDir()

	storage := metrics.NewMetricStorage(config)
	err := storage.Start()
	if err != nil {
		t.Fatalf("Failed to start storage: %v", err)
	}
	defer storage.Stop()

	// Store multiple entries
	tags := metrics.NewTagSetFromPairs("service", "test", "env", "integration")

	for i := 0; i < 10; i++ {
		err := storage.Store("integration_metric", float64(i), *tags, time.Minute)
		if err != nil {
			t.Fatalf("Failed to store entry %d: %v", i, err)
		}
	}

	// Retrieve entries
	entries, err := storage.Retrieve("integration_metric", 0)
	if err != nil {
		t.Fatalf("Failed to retrieve entries: %v", err)
	}

	if len(entries) != 10 {
		t.Errorf("Expected 10 entries, got %d", len(entries))
	}

	// Test tag-based retrieval
	filter := metrics.NewTagFilter().Equals("service", "test")

	taggedEntries, err := storage.RetrieveByTags(filter, 0)
	if err != nil {
		t.Fatalf("Failed to retrieve by tags: %v", err)
	}

	if len(taggedEntries) != 10 {
		t.Errorf("Expected 10 tagged entries, got %d", len(taggedEntries))
	}

	// Check stats
	stats := storage.GetStats()
	if stats.TotalMemoryUsage == 0 {
		t.Error("Expected memory usage greater than 0")
	}

	if stats.BufferCount == 0 {
		t.Error("Expected buffer count greater than 0")
	}
}

func TestStorageConfigUpdate(t *testing.T) {
	config := metrics.DefaultStorageConfig()
	storage := metrics.NewMetricStorage(config)
	storage.Start()
	defer storage.Stop()

	// Update configuration
	newConfig := config
	newConfig.CompressionLevel = 9
	newConfig.MaxMemoryUsage = 50 * 1024 * 1024 // 50MB

	err := storage.UpdateConfig(newConfig)
	if err != nil {
		t.Fatalf("Failed to update config: %v", err)
	}

	// Verify configuration was updated (would need getter methods in real implementation)
	// For now, just verify no error occurred
}

func TestStorageConcurrency(t *testing.T) {
	config := metrics.DefaultStorageConfig()
	storage := metrics.NewMetricStorage(config)
	storage.Start()
	defer storage.Stop()

	var wg sync.WaitGroup
	tags := metrics.NewTagSetFromPairs("test", "concurrency")

	// Concurrent stores
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				storage.Store("concurrent_metric", float64(id*100+j), *tags, time.Minute)
			}
		}(i)
	}

	// Concurrent retrieves
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 50; j++ {
				storage.Retrieve("concurrent_metric", 0)
			}
		}()
	}

	wg.Wait()

	// Verify no race conditions occurred
	stats := storage.GetStats()
	if stats.TotalMemoryUsage == 0 {
		t.Error("Expected some memory usage after concurrent operations")
	}
}
