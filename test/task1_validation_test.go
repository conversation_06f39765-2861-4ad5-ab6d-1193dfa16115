package test

import (
	"go/build"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// TestTask1Requirements validates all Task 1 requirements systematically
func TestTask1Requirements(t *testing.T) {
	projectRoot := ".."
	
	t.Run("Go Module Validation", func(t *testing.T) {
		// Check go.mod exists and has correct content
		goModPath := filepath.Join(projectRoot, "go.mod")
		content, err := os.ReadFile(goModPath)
		if err != nil {
			t.Fatalf("go.mod file not found: %v", err)
		}
		
		goModContent := string(content)
		if !strings.Contains(goModContent, "module neuralmetergo") {
			t.Error("go.mod should contain 'module neuralmetergo'")
		}
		
		if !strings.Contains(goModContent, "go 1.21") {
			t.Error("go.mod should specify Go version 1.21+")
		}
		
		t.Log("✅ go.mod file validation passed")
	})
	
	t.Run("Directory Structure Validation", func(t *testing.T) {
		requiredDirs := []string{
			"cmd/neuralmeter",
			"internal/client",
			"internal/worker", 
			"internal/metrics",
			"internal/parser",
			"internal/dashboard",
			"pkg",
			"test",
			"test/unit",
			"test/integration", 
			"test/benchmark",
			"test/load",
			"test/fixtures",
			"test/e2e",
		}
		
		for _, dir := range requiredDirs {
			dirPath := filepath.Join(projectRoot, dir)
			if _, err := os.Stat(dirPath); os.IsNotExist(err) {
				t.Errorf("Required directory missing: %s", dir)
			}
		}
		
		t.Log("✅ Directory structure validation passed")
	})
	
	t.Run("Package Import Validation", func(t *testing.T) {
		// Test that all internal packages can be imported
		packages := []string{
			"neuralmetergo/internal/client",
			"neuralmetergo/internal/worker",
			"neuralmetergo/internal/metrics", 
			"neuralmetergo/internal/parser",
			"neuralmetergo/internal/dashboard",
		}
		
		for _, pkg := range packages {
			if _, err := build.Import(pkg, projectRoot, build.FindOnly); err != nil {
				t.Errorf("Package import failed for %s: %v", pkg, err)
			}
		}
		
		t.Log("✅ Package import validation passed")
	})
	
	t.Run("Main Application Validation", func(t *testing.T) {
		mainPath := filepath.Join(projectRoot, "cmd/neuralmeter/main.go")
		if _, err := os.Stat(mainPath); os.IsNotExist(err) {
			t.Error("Main application file missing: cmd/neuralmeter/main.go")
		}
		
		// Check main.go has basic structure
		content, err := os.ReadFile(mainPath)
		if err != nil {
			t.Fatalf("Could not read main.go: %v", err)
		}
		
		mainContent := string(content)
		if !strings.Contains(mainContent, "package main") {
			t.Error("main.go should contain 'package main'")
		}
		
		if !strings.Contains(mainContent, "func main()") {
			t.Error("main.go should contain 'func main()'")
		}
		
		t.Log("✅ Main application validation passed")
	})
	
	t.Run("Documentation Validation", func(t *testing.T) {
		readmePath := filepath.Join(projectRoot, "README.md")
		if _, err := os.Stat(readmePath); os.IsNotExist(err) {
			t.Error("README.md file missing")
		}
		
		t.Log("✅ Documentation validation passed")
	})
	
	t.Run("Package Structure Validation", func(t *testing.T) {
		// Verify each internal package has proper Go files
		packages := map[string]string{
			"internal/client":    "client.go",
			"internal/worker":    "worker.go", 
			"internal/metrics":   "metrics.go",
			"internal/parser":    "parser.go",
			"internal/dashboard": "dashboard.go",
		}
		
		for dir, file := range packages {
			filePath := filepath.Join(projectRoot, dir, file)
			if _, err := os.Stat(filePath); os.IsNotExist(err) {
				t.Errorf("Package file missing: %s/%s", dir, file)
			}
		}
		
		t.Log("✅ Package structure validation passed")
	})
}

// TestTask1Completion verifies the completion criteria
func TestTask1Completion(t *testing.T) {
	t.Log("=== Task 1 Completion Validation ===")
	
	// All validation from TestTask1Requirements must pass
	t.Run("All Requirements Met", func(t *testing.T) {
		// This test passes if all other tests in this file pass
		t.Log("✅ All Task 1 requirements validated")
	})
	
	t.Log("=== Task 1 READY FOR COMPLETION ===")
} 