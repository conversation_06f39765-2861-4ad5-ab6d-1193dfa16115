version: "1.0"
name: "Base Test Plan with Includes"
description: "Test plan that demonstrates includes functionality"
duration: "2m"
concurrency: 10

includes:
  - "scenarios/auth_scenarios.yaml"
  - "scenarios/api_scenarios.yaml"

global:
  base_url: "https://api.example.com"
  headers:
    User-Agent: "NeuralMeter/1.0"
  timeout: "30s"
  variables:
    api_version: "v1"

variables:
  - name: "base_username"
    type: "static"
    value: "testuser"

scenarios:
  - name: "Base Scenario"
    description: "Main scenario defined in base plan"
    weight: 100
    requests:
      - name: "Health Check"
        method: "GET"
        url: "/health"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200

output:
  format: ["json"]
  metrics: ["response_time", "error_rate"] 