version: "1.0"
name: "API Scenarios"
duration: "1m"
concurrency: 1

scenarios:
  - name: "API Data Retrieval"
    description: "Test API data access"
    weight: 60
    requests:
      - name: "Get User Profile"
        method: "GET"
        url: "/users/profile"
        headers:
          Authorization: "Bearer {{.auth_token}}"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
          - type: "response_time"
            operator: "lt"
            value: "2s"

variables:
  - name: "api_timeout"
    type: "static"
    value: "15s"
    
global:
  variables:
    api_retries: "3" 