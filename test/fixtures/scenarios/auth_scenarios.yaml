version: "1.0"
name: "Auth Scenarios"
duration: "1m"
concurrency: 1

scenarios:
  - name: "User Login"
    description: "Test user authentication"
    weight: 80
    requests:
      - name: "Login Request"
        method: "POST"
        url: "/auth/login"
        headers:
          Content-Type: "application/json"
        body:
          username: "{{.base_username}}"
          password: "testpass123"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
          - type: "json_path"
            field: "$.token"
            operator: "contains"
            value: "eyJ"
        extract:
          - name: "auth_token"
            type: "json_path"
            path: "$.token"

variables:
  - name: "auth_endpoint"
    type: "static"
    value: "/auth/login"
    
global:
  headers:
    X-Auth-Source: "test-suite" 