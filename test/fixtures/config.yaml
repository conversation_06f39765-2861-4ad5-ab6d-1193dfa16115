server:
  host: "${HOST:localhost}"
  port: ${PORT:8080}
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  tls:
    enabled: false
    cert_file: ""
    key_file: ""

load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
  ramp_up_duration: "10s"
  ramp_down_duration: "10s"

metrics:
  enabled: true
  collection_interval: "1s"
  buffer_size: 1000
  retention_period: "24h"
  export_interval: "10s"

output:
  format: "json"
  file: ""
  console: true
  verbose: false
  templates: {}
  compression: false

dashboard:
  enabled: false
  host: "localhost"
  port: 8081
  refresh_rate: 1
  history_limit: 1000

worker:
  pool_size: 10
  queue_size: 100
  max_retries: 3
  retry_delay: "1s"
  shutdown_timeout: "30s"

global:
  log_level: "info"
  config_dir: "./config"
  data_dir: "./data"
  temp_dir: "${TEMP_DIR:/tmp}"
  environment: "development"
  debug: false
  profiles_enabled: false
  variables:
    test_var: "test_value"
    api_version: "v1" 