version: "1.0"
name: "Anchors and Aliases Test Plan"
description: "Test plan demonstrating YAML anchors and aliases"
duration: "3m"
concurrency: 20

global:
  base_url: "https://api.example.com"
  headers: &common_headers
    User-Agent: "NeuralMeter/1.0"
    Accept: "application/json"
    Content-Type: "application/json"
    X-Test-Suite: "anchors-test"
  timeout: "30s"

variables:
  - name: "test_user"
    type: "static"
    value: "anchor_test_user"

scenarios:
  - name: "Authentication with Anchors"
    description: "Test authentication using anchor references"
    weight: 70
    requests:
      - name: "Login with Anchor Config"
        method: "POST"
        url: "/auth/login"
        headers: *common_headers
        body:
          username: "{{.test_user}}"
          password: "test123"
        timeout: "10s"
        assertions: &auth_assertions
          - type: "status_code"
            operator: "eq"
            value: 200
          - type: "response_time"
            operator: "lt"
            value: "2s"
        extract:
          - name: "auth_token"
            type: "json_path"
            path: "$.token"

  - name: "API Calls with Anchors"
    description: "Test API calls using anchor references"
    weight: 30
    requests:
      - name: "Get Profile with Anchor Config"
        method: "GET"
        url: "/users/profile"
        headers: *common_headers
        timeout: "15s"
        assertions: &api_assertions
          - type: "status_code"
            operator: "eq"
            value: 200
          - type: "header_exists"
            field: "Content-Type"

      - name: "Update Profile with Anchor Config"
        method: "PUT"
        url: "/users/profile"
        headers: *common_headers
        body:
          bio: "Updated via anchor test"
        timeout: "15s"
        assertions: *api_assertions

output:
  format: ["json"]
  metrics: ["response_time", "error_rate", "success_rate"] 