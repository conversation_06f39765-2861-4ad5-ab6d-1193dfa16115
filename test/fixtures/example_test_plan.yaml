version: "1.0"
name: "API Load Test Suite"
description: "Comprehensive load test for user authentication and profile management API"
duration: "5m"
concurrency: 100
ramp_up: "30s"

global:
  base_url: "https://api.example.com"
  headers:
    User-Agent: "NeuralMeter/1.0"
    Accept: "application/json"
  timeout: "30s"
  variables:
    api_version: "v1"
    environment: "production"
  rate_limit:
    requests_per_second: 50
    burst_size: 10
    delay: "100ms"

variables:
  - name: "test_username"
    type: "faker"
    method: "username"
    description: "Random username for testing"
  - name: "test_password"
    type: "faker"
    method: "password"
    description: "Random password for testing"
  - name: "user_ids"
    type: "csv"
    file: "test/fixtures/user_ids.csv"
    description: "List of existing user IDs"

scenarios:
  - name: "User Authentication Flow"
    description: "Test user login and token retrieval"
    weight: 60
    variables:
      - name: "login_endpoint"
        type: "static"
        value: "/auth/login"
    requests:
      - name: "Login Request"
        method: "POST"
        url: "{{.global.base_url}}/{{.api_version}}{{.login_endpoint}}"
        headers:
          Content-Type: "application/json"
        body:
          username: "{{.test_username}}"
          password: "{{.test_password}}"
        timeout: "10s"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
            description: "Login should return 200 OK"
          - type: "response_time"
            operator: "lt"
            value: "2s"
            description: "Login should complete within 2 seconds"
          - type: "json_path"
            field: "$.token"
            operator: "contains"
            value: "eyJ"
            description: "Response should contain JWT token"
        extract:
          - name: "auth_token"
            type: "json_path"
            path: "$.token"
            default: ""
        variables:
          login_attempts: "1"

  - name: "User Profile Management"
    description: "Test authenticated user profile operations"
    weight: 30
    requests:
      - name: "Get User Profile"
        method: "GET"
        url: "/{{.api_version}}/users/profile"
        headers:
          Authorization: "Bearer {{.auth_token}}"
        timeout: "15s"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
            description: "Profile retrieval should succeed"
          - type: "header_exists"
            field: "Content-Type"
            description: "Response should have Content-Type header"
          - type: "json_path"
            field: "$.user.id"
            operator: "ne"
            value: ""
            description: "User should have a valid ID"
        extract:
          - name: "user_id"
            type: "json_path"
            path: "$.user.id"
          - name: "user_email"
            type: "json_path"
            path: "$.user.email"

      - name: "Update User Profile"
        method: "PUT"
        url: "/{{.api_version}}/users/profile"
        headers:
          Authorization: "Bearer {{.auth_token}}"
          Content-Type: "application/json"
        body:
          bio: "Updated via load test at {{.timestamp}}"
          preferences:
            theme: "dark"
            notifications: true
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
            description: "Profile update should succeed"
          - type: "response_time"
            operator: "lt"
            value: "3s"
            description: "Update should complete within 3 seconds"

  - name: "Data Retrieval Stress Test"
    description: "Test high-volume data retrieval operations"
    weight: 10
    requests:
      - name: "List Users"
        method: "GET"
        url: "/{{.api_version}}/users?limit=50&offset={{.random_offset}}"
        headers:
          Authorization: "Bearer {{.auth_token}}"
        timeout: "20s"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
            description: "User list should be accessible"
          - type: "response_time"
            operator: "lt"
            value: "5s"
            description: "List operation should complete within 5 seconds"
          - type: "json_path"
            field: "$.users"
            operator: "contains"
            value: "[]"
            description: "Response should contain users array"
        variables:
          random_offset: "{{.random_int(0,1000)}}"

output:
  format: ["json", "html"]
  file: "results/load_test_results"
  metrics: ["response_time", "throughput", "error_rate", "success_rate", "p95", "p99"]
  detailed: true 