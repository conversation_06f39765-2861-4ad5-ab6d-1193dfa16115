package main

import (
	"fmt"
	"time"
	"neuralmetergo/internal/metrics"
)

func main() {
	// Recreate the exact test scenario
	window, err := metrics.NewTimeBasedWindow(time.Second, 10, time.Millisecond*50)
	if err != nil {
		panic(err)
	}

	// Start async cleanup
	window.StartAsyncCleanup()
	defer window.StopAsyncCleanup()

	// Add old element (way outside window)
	oldElement := metrics.WindowElement{
		Value:     1.0,
		Timestamp: time.Now().Add(-time.Second * 2),
	}
	fmt.Printf("Adding old element: Value=%f, Age=%v\n", oldElement.Value, time.Since(oldElement.Timestamp))
	window.Add(oldElement)

	// Add current element
	currentElement := metrics.WindowElement{
		Value:     2.0,
		Timestamp: time.Now(),
	}
	fmt.Printf("Adding current element: Value=%f, Age=%v\n", currentElement.Value, time.Since(currentElement.Timestamp))
	window.Add(currentElement)

	fmt.Printf("Window size after adding both: %d\n", window.Size())

	// Wait for cleanup to occur
	fmt.Printf("Waiting 200ms for cleanup...\n")
	time.Sleep(time.Millisecond * 200)

	// Check elements
	elements := window.GetAll()
	fmt.Printf("Elements after cleanup: %d\n", len(elements))
	for i, elem := range elements {
		fmt.Printf("  Element %d: Value=%f, Age now=%v\n", i, elem.Value, time.Since(elem.Timestamp))
	}
}
