# NeuralMeterGo - High-Performance HTTP Load Testing Tool

[![Go Version](https://img.shields.io/badge/Go-1.24+-blue.svg)](https://golang.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-pending-yellow.svg)](https://github.com/user/NeuralMeterGo)

> A single-binary JMeter replacement built in Go, designed for 10x performance with massive concurrency support.

## 🚀 Quick Start

### Prerequisites
- **Go 1.21+** (currently using Go 1.24.4)
- **Git** for version control
- **Modern browser** for web dashboard

### Installation & Setup
```bash
# Clone the repository
git clone <repository-url>
cd NeuralMeterGo

# Initialize Go module dependencies
go mod tidy

# Verify installation
go version
```

### First Test Run
```bash
# Build the application
go build -o neuralmeter cmd/neuralmeter/main.go

# Run a simple test
./neuralmeter run examples/simple-test.yaml

# Access web dashboard
open http://localhost:8080/dashboard
```

## 🏗️ Project Architecture

NeuralMeterGo is designed as a **single binary** with embedded components:

```
neuralmeter (single binary)
├── HTTP Load Engine (goroutines)     # 5,000+ concurrent users
├── YAML Test Plan Parser             # JMeter-compatible concepts
├── Embedded Web Server               # Real-time dashboard
├── Metrics Collector                 # Sub-millisecond overhead
└── Results Dashboard                 # Live monitoring & control
```

### Performance Targets
- **Concurrent Users**: 5,000+ per instance (vs JMeter's ~1,000)
- **Memory Efficiency**: <50MB per 1,000 simulated users
- **Startup Time**: <5 seconds from launch to test execution
- **CPU Efficiency**: <50% CPU at 1,000 RPS

## 📁 Project Structure

```
NeuralMeterGo/
├── cmd/neuralmeter/              # Main application entry point
├── internal/                     # Private application code
│   ├── client/                   # HTTP client implementation
│   ├── worker/                   # Worker pool and job queue
│   ├── metrics/                  # Metrics collection system
│   ├── parser/                   # YAML test plan parser
│   └── dashboard/                # Web dashboard components
├── pkg/                          # Public APIs (if needed)
├── test/                         # Integration tests
├── examples/                     # Example test plans
├── docs/                         # Additional documentation
└── .taskmaster/                  # Task management (development)
    ├── tasks/                    # Implementation tasks
    ├── docs/                     # PRD and specifications
    └── tasklogs/                 # Development logs
```

## 🧪 Development & Testing

### Go Testing Framework
This project uses Go's built-in testing framework with comprehensive test coverage:

#### Unit Testing
```bash
# Run all tests
go test ./...

# Run tests with verbose output
go test -v ./...

# Run specific package tests
go test ./internal/worker/

# Run specific test function
go test -run TestJobQueue ./internal/worker/

# Test with race condition detection
go test -race ./...
```

## 🧪 Testing Framework & Workflow

NeuralMeterGo follows a **comprehensive testing-first approach** where every task requires both implementation AND passing tests for completion.

### 📋 Task-Driven Testing Strategy

**Every implementation task includes a `testStrategy` field** that defines:
- **Unit test requirements** - What specific functionality to test
- **Integration test needs** - How components work together  
- **Performance benchmarks** - Expected performance targets
- **Completion criteria** - When the task is truly "done"

**Task Completion Rule**: ❌ **No task is complete until BOTH implementation AND tests are finished and passing.**

### 🔄 Testing Workflow

#### **1. Per-Task Testing (MANDATORY)**
```bash
# Before starting any task - ensure existing tests pass
go test ./... -v

# During task implementation - test your component
go test ./internal/[component]/ -v -run Test[YourComponent]

# Before marking task complete - full validation
go test ./... -race -cover
```

#### **2. Testing Phases by Project Milestone**

**Phase 1: Foundation Testing (Tasks 1, 13-18, 32-36)**
```bash
# After completing HTTP client foundation
go test ./internal/client/... -race -cover

# After completing worker pool architecture  
go test ./internal/worker/... -race -cover

# Integration test: HTTP client + Worker pool
go test ./test/integration/ -tags=foundation
```

**Phase 2: System Integration (Tasks 37-51)**
```bash
# After metrics system completion
go test ./internal/metrics/... -race -cover

# After YAML parser completion
go test ./internal/parser/... -race -cover

# End-to-end test: Full pipeline
go test ./test/integration/ -tags=system
```

**Phase 3: Performance Validation (After Task 51)**
```bash
# Load testing with the tool itself
./neuralmeter run test/load/basic-load.yaml
./neuralmeter run test/load/stress-test.yaml --concurrency 1000

# Performance benchmarks
go test -bench=. ./internal/worker/
go test -bench=. ./internal/client/
```

### 🧪 Test Categories & Commands

#### **Unit Tests**
```bash
# Run all unit tests
go test ./...

# Run tests with verbose output
go test -v ./...

# Run specific component tests
go test ./internal/worker/ -v -run TestJobQueue
go test ./internal/client/ -v -run TestHTTPClient
```

#### **Concurrency & Race Condition Tests**
```bash
# Critical for NeuralMeterGo's high-concurrency design
go test -race ./...

# Stress test with high goroutine count
go test ./internal/worker/ -race -run TestWorkerPool
```

#### **Performance & Benchmark Tests**
```bash
# Run all benchmarks
go test -bench=. ./...

# Specific component benchmarks
go test -bench=BenchmarkQueue ./internal/worker/
go test -bench=BenchmarkHTTP ./internal/client/

# Memory and CPU profiling
go test -bench=. -memprofile=mem.prof ./internal/worker/
go test -bench=. -cpuprofile=cpu.prof ./internal/client/
```

#### **Coverage Analysis**
```bash
# Generate coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out

# Coverage by package
go test -cover ./internal/worker/
go test -cover ./internal/client/
```

#### **Integration & End-to-End Tests**
```bash
# Integration tests with build tags
go test ./test/integration/ -tags=foundation
go test ./test/integration/ -tags=system
go test ./test/integration/ -tags=execution

# Full end-to-end pipeline test
go test ./test/e2e/ -tags=e2e
```

### 📁 Test Structure
```
test/
├── unit/           # Unit tests for individual components
├── integration/    # Integration tests between components  
├── benchmark/      # Performance benchmarks and profiling
├── load/          # Sample load testing configurations
├── fixtures/      # Test data, mock responses, sample configs
└── e2e/           # End-to-end system tests
```

### 🎯 Performance Targets

**Each component has specific performance requirements defined in its `testStrategy`:**

- **Job Queue**: >10,000 enqueue/dequeue operations per second
- **Worker Pool**: >100 HTTP requests per second per worker
- **HTTP Client**: Connection pool efficiency >95%
- **Execution Engine**: Support for 1000+ concurrent workers
- **Memory Usage**: <100MB baseline, <1GB under full load

### 🚨 Continuous Integration

**Pre-commit validation** (run before every commit):
```bash
# Full test suite with race detection
go test ./... -race -cover

# Code quality checks
go vet ./...
go fmt ./...

# Build verification
go build ./cmd/neuralmeter/

# Performance regression check
go test -bench=. ./... | tee benchmark.txt
```

### 🔧 Test-Driven Development Process

1. **Read task `testStrategy`** - Understand what needs to be tested
2. **Write failing tests first** - Define expected behavior
3. **Implement functionality** - Make tests pass
4. **Run full test suite** - Ensure no regressions
5. **Benchmark performance** - Validate performance targets
6. **Mark task complete** - Only when all tests pass

**Remember: Implementation without tests = incomplete task! 🚫**

### Development Workflow

#### 1. Build & Run
```bash
# Development build and run
go run cmd/neuralmeter/main.go

# Production build
go build -o neuralmeter cmd/neuralmeter/main.go

# Cross-platform builds
GOOS=linux GOARCH=amd64 go build -o neuralmeter-linux cmd/neuralmeter/main.go
GOOS=windows GOARCH=amd64 go build -o neuralmeter.exe cmd/neuralmeter/main.go
```

#### 2. Testing During Development
```bash
# Quick validation during development
go test ./internal/worker/ -v

# Full test suite before commits
go test ./... -race -cover

# Integration testing
go test ./test/ -tags=integration
```

#### 3. Load Testing (Self-Testing)
```bash
# Test the tool with itself
./neuralmeter run --concurrency 100 --duration 30s examples/api-test.yaml

# High concurrency testing
./neuralmeter run --concurrency 1000 --duration 5m examples/stress-test.yaml

# Memory profiling during load
./neuralmeter run examples/memory-test.yaml --profile-memory
```

## 🎯 Usage Examples

### Command Line Interface
```bash
# Basic load test
neuralmeter run test-plan.yaml

# Quick test without YAML file
neuralmeter run --concurrency 100 --duration 5m --url https://api.example.com

# Validate test plan
neuralmeter validate test-plan.yaml

# Convert JMeter test plan
neuralmeter convert jmeter-plan.jmx

# CI/CD integration
neuralmeter run test-plan.yaml --output json --fail-on-error
```

### YAML Test Plan Format
```yaml
name: "API Load Test"
duration: "5m"
concurrency: 100
ramp_up: "30s"

scenarios:
  - name: "User Login"
    weight: 50
    requests:
      - method: "POST"
        url: "https://api.example.com/login"
        headers:
          Content-Type: "application/json"
        body: '{"email": "<EMAIL>", "password": "pass123"}'
        assertions:
          - response_code: 200
          - response_time: "<500ms"
```

### Web Dashboard
Access the embedded web dashboard at `http://localhost:8080/dashboard` for:
- **Real-time monitoring**: RPS, response times, error rates
- **Test control**: Start, stop, pause, adjust load
- **Live charts**: Response time percentiles, throughput graphs
- **Results export**: JSON, CSV formats

## 🔧 Key Components

### HTTP Client Foundation (Tasks 32-36)
- **Connection pooling** with configurable limits
- **HTTP/2 support** with multiplexing
- **Timeout management** (connection, request, response)
- **Retry logic** with exponential backoff
- **Authentication** (Basic, Bearer, Custom headers)

### Worker Pool Architecture (Tasks 13-18)
- **Goroutine-based workers** for massive concurrency
- **Job queue** with channel-based operations
- **Load balancing** across worker pool
- **Graceful shutdown** with cleanup
- **Health monitoring** and worker recycling

### Metrics System (Tasks 37-41)
- **Thread-safe data structures** with atomic operations
- **Real-time collection** with sub-millisecond overhead
- **Statistical aggregation** (percentiles, averages)
- **Export formats** (JSON, CSV, Prometheus)
- **Memory-efficient storage** for sustained testing

### Test Plan Parser (Tasks 47-51)
- **YAML parsing** with validation
- **Go struct mapping** with proper tags
- **Test execution engine** with orchestration
- **Scenario scheduling** and timing control
- **Result collection** and aggregation

## 🚦 Development Status

This project is **actively under development** using a task-driven approach:

- **Total Tasks**: 68 implementation tasks
- **Current Phase**: Foundation setup and core engine development
- **Task Management**: Using Taskmaster-AI for structured development
- **Next Task**: [Check with `task-master next`]

### Task-Driven Development
```bash
# View current tasks
task-master list

# Get next task to work on
task-master next

# View specific task details
task-master show <task-id>

# Update task status
task-master set-status --id <task-id> --status done
```

## 🧩 Integration & CI/CD

### Exit Codes
- `0`: Test completed successfully
- `1`: Test failed (assertions not met)
- `2`: Configuration error
- `3`: Runtime error

### CI/CD Pipeline Integration
```yaml
# Example GitHub Actions
- name: Run Load Tests
  run: |
    ./neuralmeter run ci-test-plan.yaml --output json --fail-on-error
    if [ $? -ne 0 ]; then exit 1; fi
```

### Output Formats
```bash
# JSON output for automation
neuralmeter run test.yaml --output json > results.json

# CSV for spreadsheet analysis
neuralmeter run test.yaml --output csv > results.csv

# Prometheus metrics endpoint
neuralmeter run test.yaml --prometheus-port 9090
```

## 🎨 Features

### ✅ Implemented
- [x] Go project structure and module setup
- [x] Task-driven development framework
- [x] Comprehensive testing strategy

### 🚧 In Development
- [ ] HTTP client with connection pooling
- [ ] Worker pool architecture
- [ ] YAML test plan parser
- [ ] Metrics collection system
- [ ] Web dashboard interface

### 📋 Planned
- [ ] JMeter test plan import
- [ ] Advanced load patterns
- [ ] Response validation engine
- [ ] Performance profiling tools
- [ ] Plugin system

## 🤝 Contributing

### Development Approach
1. **Task-based development**: Each feature is broken down into specific tasks
2. **Test-driven development**: Write tests before implementation
3. **Performance-first**: Optimize for concurrency and memory efficiency
4. **Documentation**: Keep README and code documentation current

### Code Standards
- **Go formatting**: Use `go fmt` and `go vet`
- **Testing**: Maintain >80% test coverage
- **Performance**: Benchmark critical paths
- **Documentation**: Document public APIs and complex logic

### Getting Started
1. Check current tasks: `task-master list`
2. Pick next task: `task-master next`
3. Implement with tests: `go test -v ./...`
4. Update task status: `task-master set-status --id <id> --status done`

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- **Project Repository**: [GitHub](https://github.com/user/NeuralMeterGo)
- **Documentation**: [Wiki](https://github.com/user/NeuralMeterGo/wiki)
- **Issue Tracker**: [GitHub Issues](https://github.com/user/NeuralMeterGo/issues)
- **Taskmaster Documentation**: [.taskmaster/docs/](/.taskmaster/docs/)

## 📊 Performance Comparison

| Metric | NeuralMeterGo (Target) | JMeter |
|--------|------------------------|---------|
| Concurrent Users | 5,000+ | ~1,000 |
| Memory (1K users) | <50MB | >2GB |
| Startup Time | <5s | >30s |
| CPU (1K RPS) | <50% | >80% |
| Binary Size | ~20MB | N/A (Java) |

---

**Built with ❤️ in Go | Designed for Performance | Optimized for Scale** 