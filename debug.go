package main

import (
	"fmt"
	"time"
	"neuralmetergo/internal/metrics"
)

func main() {
	// Create a time-based window with 5 second duration
	window, err := metrics.NewTimeBasedWindow(time.Second*5, 10, time.Second)
	if err != nil {
		panic(err)
	}

	// Add elements
	now := time.Now()
	fmt.Printf("Now: %v\n", now)
	
	elements := []metrics.WindowElement{
		{Value: 1.0, Timestamp: now.Add(-time.Second * 15)}, // Outside window
		{Value: 2.0, Timestamp: now.Add(-time.Second * 5)},  // Inside window
		{Value: 3.0, Timestamp: now},                        // Inside window
	}

	for i, element := range elements {
		fmt.Printf("Adding element %d: Value=%f, Timestamp=%v, Age=%v\n", 
			i, element.Value, element.Timestamp, now.Sub(element.Timestamp))
		window.Add(element)
	}

	// Check which elements are in the window
	allElements := window.GetAll()
	fmt.Printf("Elements in window: %d\n", len(allElements))
	for i, elem := range allElements {
		fmt.Printf("  Element %d: Value=%f, Timestamp=%v\n", i, elem.Value, elem.Timestamp)
	}

	// Test the expected logic
	duration := time.Second * 5
	expectedInWindow := 0
	for i, element := range elements {
		age := now.Sub(element.Timestamp)
		isInWindow := age <= duration
		fmt.Printf("Element %d: Age=%v, InWindow=%v\n", i, age, isInWindow)
		if isInWindow {
			expectedInWindow++
		}
	}
	fmt.Printf("Expected in window: %d\n", expectedInWindow)
}
