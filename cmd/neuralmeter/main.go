package main

import (
	"fmt"
	"log"
	"os"
)

// Version information
var (
	Version   = "0.1.0"
	BuildTime = "unknown"
	GitCommit = "unknown"
)

func main() {
	fmt.Printf("NeuralMeterGo v%s\n", Version)
	fmt.Println("High-Performance HTTP Load Testing Tool")
	fmt.Println()

	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	command := os.Args[1]
	switch command {
	case "version", "--version", "-v":
		printVersion()
	case "help", "--help", "-h":
		printUsage()
	case "run":
		fmt.Println("Load testing functionality will be implemented in future tasks")
		fmt.Println("Run command recognized - implementation pending")
	case "validate":
		fmt.Println("Test plan validation functionality will be implemented in future tasks")
		fmt.Println("Validate command recognized - implementation pending")
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func printVersion() {
	fmt.Printf("NeuralMeterGo %s\n", Version)
	fmt.Printf("Build Time: %s\n", BuildTime)
	fmt.Printf("Git Commit: %s\n", GitCommit)
	fmt.Printf("Go Version: %s\n", getGoVersion())
}

func printUsage() {
	fmt.Println("Usage: neuralmeter <command> [options]")
	fmt.Println()
	fmt.Println("Commands:")
	fmt.Println("  run <test-plan.yaml>    Run load test with specified test plan")
	fmt.Println("  validate <test-plan.yaml> Validate test plan configuration")
	fmt.Println("  version                 Show version information")
	fmt.Println("  help                    Show this help message")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  neuralmeter run test-plan.yaml")
	fmt.Println("  neuralmeter validate test-plan.yaml")
	fmt.Println("  neuralmeter version")
}

func getGoVersion() string {
	// This will be properly implemented when runtime info is needed
	return "1.21+"
}

// init function for any initialization that needs to happen before main
func init() {
	// Set up logging
	log.SetFlags(log.LstdFlags | log.Lshortfile)
} 