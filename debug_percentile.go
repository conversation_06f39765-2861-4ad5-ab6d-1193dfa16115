package main

import (
	"fmt"
	"neuralmetergo/internal/metrics"
)

func main() {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 10
	config.EnableStats = true

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		panic(err)
	}
	defer window.Close()

	// Add test data with known statistical properties
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0}
	for _, value := range values {
		window.Add(value)
	}

	// Get the actual values in the window
	actualValues := window.GetValues()
	fmt.Printf("Values in window: %v\n", actualValues)

	// Test different percentile methods
	median := window.Median()
	fmt.Printf("Median method: %f\n", median)

	p50 := window.Percentile(0.5)
	fmt.Printf("Percentile(0.5) method: %f\n", p50)

	stats := window.GetStatistics()
	fmt.Printf("Stats P50: %f\n", stats.P50)

	// Test the basic statistics median
	basicMedian, err := metrics.Median(actualValues)
	if err != nil {
		fmt.Printf("Basic median error: %v\n", err)
	} else {
		fmt.Printf("Basic median: %f\n", basicMedian)
	}
}
