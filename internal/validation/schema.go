// Package validation provides schema definitions for test plan validation
package validation

// Schema defines the validation schema for test plans
type Schema struct {
	Version     string          `json:"version"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Rules       map[string]Rule `json:"rules"`
}

// Rule defines a validation rule within the schema
type Rule struct {
	Type        string      `json:"type"`
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	MinValue    interface{} `json:"min_value,omitempty"`
	MaxValue    interface{} `json:"max_value,omitempty"`
	Pattern     string      `json:"pattern,omitempty"`
	Options     []string    `json:"options,omitempty"`
}

// NewDefaultSchema creates the default validation schema for test plans
func NewDefaultSchema() *Schema {
	return &Schema{
		Version:     "1.0.0",
		Name:        "NeuralMeter Test Plan Schema",
		Description: "Comprehensive validation schema for NeuralMeter test plans",
		Rules: map[string]Rule{
			"version": {
				Type:        "string",
				Description: "Test plan version identifier",
				Required:    true,
				Pattern:     `^\d+\.\d+(\.\d+)?$`,
			},
			"name": {
				Type:        "string",
				Description: "Test plan name",
				Required:    true,
				MinValue:    1,
				MaxValue:    100,
			},
			"duration": {
				Type:        "duration",
				Description: "Test execution duration",
				Required:    true,
				MinValue:    "1s",
				MaxValue:    "24h",
			},
			"concurrency": {
				Type:        "integer",
				Description: "Number of concurrent users/connections",
				Required:    true,
				MinValue:    1,
				MaxValue:    10000,
			},
			"http_method": {
				Type:        "string",
				Description: "HTTP request method",
				Required:    true,
				Options:     []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "TRACE", "CONNECT"},
			},
			"assertion_type": {
				Type:        "string",
				Description: "Type of assertion to perform",
				Required:    true,
				Options:     []string{"status_code", "response_time", "contains", "json_path", "header_exists", "regex"},
			},
			"assertion_operator": {
				Type:        "string",
				Description: "Comparison operator for assertions",
				Required:    false,
				Options:     []string{"eq", "ne", "lt", "le", "gt", "ge", "contains", "not_contains", "matches"},
			},
			"extract_type": {
				Type:        "string",
				Description: "Type of data extraction",
				Required:    true,
				Options:     []string{"json_path", "xpath", "regex", "header", "css_selector"},
			},
			"variable_type": {
				Type:        "string",
				Description: "Type of variable",
				Required:    true,
				Options:     []string{"static", "random", "faker", "csv", "counter", "uuid"},
			},
			"output_format": {
				Type:        "string",
				Description: "Output format for results",
				Required:    false,
				Options:     []string{"json", "html", "csv", "xml", "junit"},
			},
			"metric_type": {
				Type:        "string",
				Description: "Type of metric to collect",
				Required:    false,
				Options:     []string{"response_time", "throughput", "error_rate", "success_rate", "p50", "p95", "p99", "min", "max", "mean"},
			},
		},
	}
}
