// Package validation provides various validator implementations
package validation

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"neuralmetergo/internal/parser"
)

// StructureValidator validates the basic structure and required fields
type StructureValidator struct{}

func NewStructureValidator() *StructureValidator {
	return &StructureValidator{}
}

func (sv *StructureValidator) Category() string {
	return "structure"
}

func (sv *StructureValidator) Description() string {
	return "Validates test plan structure, required fields, and data types"
}

func (sv *StructureValidator) Validate(plan *parser.TestPlan, result *ValidationResult) {
	// Validate required fields are present and non-empty
	if plan.Version == "" {
		addIssue(result, createIssue(SeverityError, "MISSING_VERSION", "Test plan version is required", "version", "structure", "required_field"))
	}

	if plan.Name == "" {
		addIssue(result, createIssue(SeverityError, "MISSING_NAME", "Test plan name is required", "name", "structure", "required_field"))
	}

	if plan.Duration.Duration == 0 {
		addIssue(result, createIssue(SeverityError, "MISSING_DURATION", "Test plan duration is required", "duration", "structure", "required_field"))
	}

	if plan.Concurrency <= 0 {
		addIssue(result, createIssue(SeverityError, "INVALID_CONCURRENCY", "Concurrency must be greater than 0", "concurrency", "structure", "min_value"))
	}

	if len(plan.Scenarios) == 0 {
		addIssue(result, createIssue(SeverityError, "NO_SCENARIOS", "At least one scenario is required", "scenarios", "structure", "required_field"))
	}

	// Validate scenario structure
	for i, scenario := range plan.Scenarios {
		scenarioPrefix := fmt.Sprintf("scenarios[%d]", i)

		if scenario.Name == "" {
			addIssue(result, createIssue(SeverityError, "MISSING_SCENARIO_NAME", "Scenario name is required", fmt.Sprintf("%s.name", scenarioPrefix), "structure", "required_field"))
		}

		if len(scenario.Requests) == 0 {
			addIssue(result, createIssue(SeverityError, "NO_REQUESTS", "Scenario must have at least one request", fmt.Sprintf("%s.requests", scenarioPrefix), "structure", "required_field"))
		}

		// Validate request structure
		for j, request := range scenario.Requests {
			requestPrefix := fmt.Sprintf("%s.requests[%d]", scenarioPrefix, j)

			if request.Method == "" {
				addIssue(result, createIssue(SeverityError, "MISSING_METHOD", "Request method is required", fmt.Sprintf("%s.method", requestPrefix), "structure", "required_field"))
			}

			if request.URL == "" {
				addIssue(result, createIssue(SeverityError, "MISSING_URL", "Request URL is required", fmt.Sprintf("%s.url", requestPrefix), "structure", "required_field"))
			}
		}
	}
}

// HTTPValidator validates HTTP-specific elements
type HTTPValidator struct{}

func NewHTTPValidator() *HTTPValidator {
	return &HTTPValidator{}
}

func (hv *HTTPValidator) Category() string {
	return "http"
}

func (hv *HTTPValidator) Description() string {
	return "Validates HTTP methods, URLs, headers, and request bodies"
}

func (hv *HTTPValidator) Validate(plan *parser.TestPlan, result *ValidationResult) {
	// Validate global base URL if present
	if plan.Global.BaseURL != "" {
		if valid, msg := validateURLFormat(plan.Global.BaseURL); !valid {
			addIssue(result, ValidationIssue{
				Severity:   SeverityError,
				Code:       "INVALID_BASE_URL",
				Message:    fmt.Sprintf("Invalid base URL: %s", msg),
				Field:      "global.base_url",
				Value:      plan.Global.BaseURL,
				Category:   "http",
				Rule:       "url_format",
				Suggestion: "Ensure base URL follows format: http(s)://domain.com[:port]",
			})
		}
	}

	// Validate scenarios
	for i, scenario := range plan.Scenarios {
		for j, request := range scenario.Requests {
			requestPrefix := fmt.Sprintf("scenarios[%d].requests[%d]", i, j)

			// Validate HTTP method
			if valid, msg := validateHTTPMethod(request.Method); !valid {
				addIssue(result, ValidationIssue{
					Severity: SeverityError,
					Code:     "INVALID_HTTP_METHOD",
					Message:  msg,
					Field:    fmt.Sprintf("%s.method", requestPrefix),
					Value:    request.Method,
					Category: "http",
					Rule:     "http_method",
				})
			}

			// Validate URL format
			if valid, msg := hv.validateRequestURL(request.URL, plan.Global.BaseURL); !valid {
				addIssue(result, ValidationIssue{
					Severity:   SeverityError,
					Code:       "INVALID_URL",
					Message:    msg,
					Field:      fmt.Sprintf("%s.url", requestPrefix),
					Value:      request.URL,
					Category:   "http",
					Rule:       "url_format",
					Suggestion: "Use absolute URLs (http://...) or relative URLs (/) with base_url set",
				})
			}

			// Validate headers
			for headerName, headerValue := range request.Headers {
				if valid, msg := hv.validateHTTPHeader(headerName, headerValue); !valid {
					addIssue(result, ValidationIssue{
						Severity: SeverityWarning,
						Code:     "INVALID_HEADER",
						Message:  msg,
						Field:    fmt.Sprintf("%s.headers.%s", requestPrefix, headerName),
						Value:    headerValue,
						Category: "http",
						Rule:     "header_format",
					})
				}
			}

			// Validate request body for methods that support it
			if hv.methodSupportsBody(request.Method) && request.Body != nil {
				if valid, msg := hv.validateRequestBody(request.Body, request.Headers); !valid {
					addIssue(result, ValidationIssue{
						Severity: SeverityWarning,
						Code:     "INVALID_BODY",
						Message:  msg,
						Field:    fmt.Sprintf("%s.body", requestPrefix),
						Category: "http",
						Rule:     "body_format",
					})
				}
			}

			// Validate assertions
			for k, assertion := range request.Assertions {
				assertionPrefix := fmt.Sprintf("%s.assertions[%d]", requestPrefix, k)
				hv.validateAssertion(assertion, assertionPrefix, result)
			}

			// Validate extractions
			for k, extract := range request.Extract {
				extractPrefix := fmt.Sprintf("%s.extract[%d]", requestPrefix, k)
				hv.validateExtraction(extract, extractPrefix, result)
			}
		}
	}
}

func (hv *HTTPValidator) validateRequestURL(requestURL, baseURL string) (bool, string) {
	if requestURL == "" {
		return false, "URL cannot be empty"
	}

	// Allow template variables
	if strings.Contains(requestURL, "{{") && strings.Contains(requestURL, "}}") {
		return true, ""
	}

	// Check if it's an absolute URL
	if _, err := url.Parse(requestURL); err == nil {
		if strings.HasPrefix(requestURL, "http://") || strings.HasPrefix(requestURL, "https://") {
			return true, ""
		}
	}

	// Check if it's a relative URL with base URL set
	if strings.HasPrefix(requestURL, "/") && baseURL != "" {
		return true, ""
	}

	// Check if it's a relative URL without base URL
	if strings.HasPrefix(requestURL, "/") && baseURL == "" {
		return false, "Relative URL requires base_url to be set in global configuration"
	}

	return false, "URL must be absolute (http://...) or relative (/) with base_url set"
}

func (hv *HTTPValidator) validateHTTPHeader(name, value string) (bool, string) {
	// Basic header name validation (RFC 7230)
	headerNameRegex := regexp.MustCompile(`^[a-zA-Z0-9!#$%&'*+\-.^_` + "`" + `|~]+$`)
	if !headerNameRegex.MatchString(name) {
		return false, fmt.Sprintf("Invalid header name '%s'. Header names must contain only valid characters", name)
	}

	// Check for common header value issues
	if strings.Contains(value, "\n") || strings.Contains(value, "\r") {
		return false, "Header values cannot contain newline characters"
	}

	return true, ""
}

func (hv *HTTPValidator) validateRequestBody(body interface{}, headers map[string]string) (bool, string) {
	contentType := ""
	for name, value := range headers {
		if strings.ToLower(name) == "content-type" {
			contentType = strings.ToLower(value)
			break
		}
	}

	// If content type suggests JSON, try to validate JSON structure
	if strings.Contains(contentType, "application/json") {
		// Basic validation - in a real implementation, you might want to parse JSON
		if bodyStr, ok := body.(string); ok {
			if strings.TrimSpace(bodyStr) != "" && !strings.HasPrefix(strings.TrimSpace(bodyStr), "{") && !strings.HasPrefix(strings.TrimSpace(bodyStr), "[") {
				return false, "Body appears to be JSON but doesn't start with { or ["
			}
		}
	}

	return true, ""
}

func (hv *HTTPValidator) methodSupportsBody(method string) bool {
	bodyMethods := []string{"POST", "PUT", "PATCH"}
	for _, m := range bodyMethods {
		if method == m {
			return true
		}
	}
	return false
}

func (hv *HTTPValidator) validateAssertion(assertion parser.Assertion, prefix string, result *ValidationResult) {
	// Validate assertion type
	validTypes := []string{"status_code", "response_time", "contains", "json_path", "header_exists", "regex"}
	typeValid := false
	for _, validType := range validTypes {
		if assertion.Type == validType {
			typeValid = true
			break
		}
	}

	if !typeValid {
		addIssue(result, ValidationIssue{
			Severity: SeverityError,
			Code:     "INVALID_ASSERTION_TYPE",
			Message:  fmt.Sprintf("Invalid assertion type '%s'. Valid types: %s", assertion.Type, strings.Join(validTypes, ", ")),
			Field:    fmt.Sprintf("%s.type", prefix),
			Value:    assertion.Type,
			Category: "http",
			Rule:     "assertion_type",
		})
	}

	// Validate operator if present
	if assertion.Operator != "" {
		validOperators := []string{"eq", "ne", "lt", "le", "gt", "ge", "contains", "not_contains", "matches"}
		operatorValid := false
		for _, validOp := range validOperators {
			if assertion.Operator == validOp {
				operatorValid = true
				break
			}
		}

		if !operatorValid {
			addIssue(result, ValidationIssue{
				Severity: SeverityError,
				Code:     "INVALID_ASSERTION_OPERATOR",
				Message:  fmt.Sprintf("Invalid assertion operator '%s'. Valid operators: %s", assertion.Operator, strings.Join(validOperators, ", ")),
				Field:    fmt.Sprintf("%s.operator", prefix),
				Value:    assertion.Operator,
				Category: "http",
				Rule:     "assertion_operator",
			})
		}
	}

	// Type-specific validation
	switch assertion.Type {
	case "status_code":
		if assertion.Value == nil {
			addIssue(result, ValidationIssue{
				Severity: SeverityError,
				Code:     "MISSING_ASSERTION_VALUE",
				Message:  "status_code assertion requires a value",
				Field:    fmt.Sprintf("%s.value", prefix),
				Category: "http",
				Rule:     "assertion_value_required",
			})
		}
	case "json_path":
		if assertion.Field == "" {
			addIssue(result, ValidationIssue{
				Severity: SeverityError,
				Code:     "MISSING_JSON_PATH",
				Message:  "json_path assertion requires a field (JSONPath expression)",
				Field:    fmt.Sprintf("%s.field", prefix),
				Category: "http",
				Rule:     "json_path_required",
			})
		}
	case "header_exists":
		if assertion.Field == "" {
			addIssue(result, ValidationIssue{
				Severity: SeverityError,
				Code:     "MISSING_HEADER_NAME",
				Message:  "header_exists assertion requires a field (header name)",
				Field:    fmt.Sprintf("%s.field", prefix),
				Category: "http",
				Rule:     "header_name_required",
			})
		}
	}
}

func (hv *HTTPValidator) validateExtraction(extract parser.Extract, prefix string, result *ValidationResult) {
	// Validate extraction type
	validTypes := []string{"json_path", "xpath", "regex", "header", "css_selector"}
	typeValid := false
	for _, validType := range validTypes {
		if extract.Type == validType {
			typeValid = true
			break
		}
	}

	if !typeValid {
		addIssue(result, ValidationIssue{
			Severity: SeverityError,
			Code:     "INVALID_EXTRACT_TYPE",
			Message:  fmt.Sprintf("Invalid extraction type '%s'. Valid types: %s", extract.Type, strings.Join(validTypes, ", ")),
			Field:    fmt.Sprintf("%s.type", prefix),
			Value:    extract.Type,
			Category: "http",
			Rule:     "extract_type",
		})
	}

	// Validate path/pattern is present
	if extract.Path == "" {
		addIssue(result, ValidationIssue{
			Severity: SeverityError,
			Code:     "MISSING_EXTRACT_PATH",
			Message:  "Extraction path/pattern is required",
			Field:    fmt.Sprintf("%s.path", prefix),
			Category: "http",
			Rule:     "extract_path_required",
		})
	}

	// Type-specific validation
	switch extract.Type {
	case "regex":
		if _, err := regexp.Compile(extract.Path); err != nil {
			addIssue(result, ValidationIssue{
				Severity: SeverityError,
				Code:     "INVALID_REGEX",
				Message:  fmt.Sprintf("Invalid regex pattern: %v", err),
				Field:    fmt.Sprintf("%s.path", prefix),
				Value:    extract.Path,
				Category: "http",
				Rule:     "regex_syntax",
			})
		}
	}
}

// PerformanceValidator validates performance-related aspects
type PerformanceValidator struct{}

func NewPerformanceValidator() *PerformanceValidator {
	return &PerformanceValidator{}
}

func (pv *PerformanceValidator) Category() string {
	return "performance"
}

func (pv *PerformanceValidator) Description() string {
	return "Validates performance-related configuration and identifies potential issues"
}

func (pv *PerformanceValidator) Validate(plan *parser.TestPlan, result *ValidationResult) {
	// Check for unreasonably high concurrency
	if plan.Concurrency > 1000 {
		addIssue(result, ValidationIssue{
			Severity:   SeverityWarning,
			Code:       "HIGH_CONCURRENCY",
			Message:    fmt.Sprintf("High concurrency (%d) may cause resource exhaustion", plan.Concurrency),
			Field:      "concurrency",
			Value:      plan.Concurrency,
			Category:   "performance",
			Rule:       "reasonable_concurrency",
			Suggestion: "Consider starting with lower concurrency and gradually increasing",
		})
	}

	// Check test duration
	if plan.Duration.Duration > 24*time.Hour {
		addIssue(result, ValidationIssue{
			Severity:   SeverityWarning,
			Code:       "LONG_DURATION",
			Message:    fmt.Sprintf("Very long test duration (%v) may be impractical", plan.Duration.Duration),
			Field:      "duration",
			Value:      plan.Duration.Duration.String(),
			Category:   "performance",
			Rule:       "reasonable_duration",
			Suggestion: "Consider shorter test durations for iterative testing",
		})
	}

	// Check for too many scenarios
	if len(plan.Scenarios) > 50 {
		addIssue(result, ValidationIssue{
			Severity:   SeverityInfo,
			Code:       "MANY_SCENARIOS",
			Message:    fmt.Sprintf("Large number of scenarios (%d) may impact performance", len(plan.Scenarios)),
			Field:      "scenarios",
			Category:   "performance",
			Rule:       "scenario_count",
			Suggestion: "Consider grouping related requests into fewer scenarios",
		})
	}

	// Check for very short timeouts
	for i, scenario := range plan.Scenarios {
		for j, request := range scenario.Requests {
			if request.Timeout.Duration > 0 && request.Timeout.Duration < time.Second {
				addIssue(result, ValidationIssue{
					Severity:   SeverityWarning,
					Code:       "SHORT_TIMEOUT",
					Message:    fmt.Sprintf("Very short timeout (%v) may cause false negatives", request.Timeout.Duration),
					Field:      fmt.Sprintf("scenarios[%d].requests[%d].timeout", i, j),
					Value:      request.Timeout.Duration.String(),
					Category:   "performance",
					Rule:       "reasonable_timeout",
					Suggestion: "Consider timeouts of at least 1-5 seconds for most APIs",
				})
			}
		}
	}
}

// SemanticValidator validates semantic consistency and logic
type SemanticValidator struct{}

func NewSemanticValidator() *SemanticValidator {
	return &SemanticValidator{}
}

func (sv *SemanticValidator) Category() string {
	return "semantic"
}

func (sv *SemanticValidator) Description() string {
	return "Validates semantic consistency, variable references, and logical flow"
}

func (sv *SemanticValidator) Validate(plan *parser.TestPlan, result *ValidationResult) {
	// Collect all defined variables
	definedVars := make(map[string]string) // name -> scope

	// Global variables
	for name := range plan.Global.Variables {
		definedVars[name] = "global"
	}

	// Plan-level variables
	for _, variable := range plan.Variables {
		if _, exists := definedVars[variable.Name]; exists {
			addIssue(result, ValidationIssue{
				Severity:   SeverityWarning,
				Code:       "DUPLICATE_VARIABLE",
				Message:    fmt.Sprintf("Variable '%s' is defined multiple times", variable.Name),
				Field:      fmt.Sprintf("variables[%s]", variable.Name),
				Value:      variable.Name,
				Category:   "semantic",
				Rule:       "unique_variables",
				Suggestion: "Use unique variable names or override in appropriate scope",
			})
		}
		definedVars[variable.Name] = "plan"
	}

	// Validate scenario weights
	totalWeight := 0
	for _, scenario := range plan.Scenarios {
		totalWeight += scenario.Weight
	}

	if totalWeight == 0 {
		addIssue(result, ValidationIssue{
			Severity: SeverityError,
			Code:     "ZERO_TOTAL_WEIGHT",
			Message:  "Total scenario weight cannot be zero",
			Field:    "scenarios.weight",
			Category: "semantic",
			Rule:     "positive_weights",
		})
	}

	// Check for unbalanced weights
	if len(plan.Scenarios) > 1 {
		maxWeight := 0
		minWeight := 100
		for _, scenario := range plan.Scenarios {
			if scenario.Weight > maxWeight {
				maxWeight = scenario.Weight
			}
			if scenario.Weight < minWeight {
				minWeight = scenario.Weight
			}
		}

		if maxWeight > 0 && minWeight > 0 && maxWeight/minWeight > 10 {
			addIssue(result, ValidationIssue{
				Severity:   SeverityInfo,
				Code:       "UNBALANCED_WEIGHTS",
				Message:    "Scenario weights are significantly unbalanced",
				Field:      "scenarios.weight",
				Category:   "semantic",
				Rule:       "balanced_weights",
				Suggestion: "Consider more balanced weight distribution for representative testing",
			})
		}
	}

	// TODO: Implement variable reference validation in URLs, headers, body
	// This would require parsing template expressions like {{.variable_name}}
}

// DependencyValidator validates dependencies and relationships
type DependencyValidator struct{}

func NewDependencyValidator() *DependencyValidator {
	return &DependencyValidator{}
}

func (dv *DependencyValidator) Category() string {
	return "dependency"
}

func (dv *DependencyValidator) Description() string {
	return "Validates dependencies, circular references, variable flows, and assertion logic"
}

func (dv *DependencyValidator) Validate(plan *parser.TestPlan, result *ValidationResult) {
	// Check for scenarios with no requests
	for i, scenario := range plan.Scenarios {
		if len(scenario.Requests) == 0 {
			addIssue(result, ValidationIssue{
				Severity: SeverityError,
				Code:     "EMPTY_SCENARIO",
				Message:  fmt.Sprintf("Scenario '%s' has no requests", scenario.Name),
				Field:    fmt.Sprintf("scenarios[%d].requests", i),
				Category: "dependency",
				Rule:     "non_empty_scenario",
			})
		}

		// Check for duplicate scenario names
		for j, otherScenario := range plan.Scenarios {
			if i != j && scenario.Name == otherScenario.Name {
				addIssue(result, ValidationIssue{
					Severity:   SeverityWarning,
					Code:       "DUPLICATE_SCENARIO_NAME",
					Message:    fmt.Sprintf("Scenario name '%s' is used multiple times", scenario.Name),
					Field:      fmt.Sprintf("scenarios[%d].name", i),
					Value:      scenario.Name,
					Category:   "dependency",
					Rule:       "unique_scenario_names",
					Suggestion: "Use unique scenario names for better identification",
				})
			}
		}
	}

	// Enhanced dependency validation
	dv.validateVariableDependencies(plan, result)
	dv.validateAssertionLogic(plan, result)
	dv.detectCircularDependencies(plan, result)
	dv.validateRequestFlow(plan, result)
}

// validateVariableDependencies checks variable extraction and usage dependencies
func (dv *DependencyValidator) validateVariableDependencies(plan *parser.TestPlan, result *ValidationResult) {
	// Collect all available variables at different scopes
	globalVars := make(map[string]bool)
	planVars := make(map[string]bool)

	// Global variables
	for name := range plan.Global.Variables {
		globalVars[name] = true
	}

	// Plan-level variables
	for _, variable := range plan.Variables {
		planVars[variable.Name] = true
	}

	for scenarioIdx, scenario := range plan.Scenarios {
		// Track variables available in this scenario
		scenarioVars := make(map[string]bool)

		// Add scenario-level variables
		for _, variable := range scenario.Variables {
			scenarioVars[variable.Name] = true
		}

		// Track extracted variables within the scenario (order matters)
		extractedVars := make(map[string]int) // variable -> request index where it's extracted

		for requestIdx, request := range scenario.Requests {
			// First, check for variable usage in this request
			dv.checkVariableUsage(request, scenarioIdx, requestIdx, globalVars, planVars, scenarioVars, extractedVars, result)

			// Then, record variables extracted by this request (available for later requests)
			for _, extract := range request.Extract {
				if extract.Name == "" {
					addIssue(result, ValidationIssue{
						Severity: SeverityError,
						Code:     "EMPTY_EXTRACT_NAME",
						Message:  "Extraction variable name cannot be empty",
						Field:    fmt.Sprintf("scenarios[%d].requests[%d].extract[].name", scenarioIdx, requestIdx),
						Category: "dependency",
						Rule:     "extract_name_required",
					})
					continue
				}

				// Check for duplicate extractions within the same scenario
				if prevRequestIdx, exists := extractedVars[extract.Name]; exists {
					addIssue(result, ValidationIssue{
						Severity:   SeverityWarning,
						Code:       "DUPLICATE_EXTRACTION",
						Message:    fmt.Sprintf("Variable '%s' is extracted multiple times in scenario '%s' (first at request %d, again at request %d)", extract.Name, scenario.Name, prevRequestIdx, requestIdx),
						Field:      fmt.Sprintf("scenarios[%d].requests[%d].extract", scenarioIdx, requestIdx),
						Value:      extract.Name,
						Category:   "dependency",
						Rule:       "unique_extractions",
						Suggestion: "Consider using different variable names or removing duplicate extractions",
					})
				}

				extractedVars[extract.Name] = requestIdx
			}

			// Check for variables defined in request-level variables
			for varName := range request.Variables {
				if _, exists := extractedVars[varName]; exists {
					addIssue(result, ValidationIssue{
						Severity:   SeverityWarning,
						Code:       "VARIABLE_NAME_CONFLICT",
						Message:    fmt.Sprintf("Request variable '%s' conflicts with extracted variable in scenario '%s'", varName, scenario.Name),
						Field:      fmt.Sprintf("scenarios[%d].requests[%d].variables[%s]", scenarioIdx, requestIdx, varName),
						Value:      varName,
						Category:   "dependency",
						Rule:       "unique_variable_names",
						Suggestion: "Use unique variable names to avoid conflicts",
					})
				}
			}
		}
	}
}

// checkVariableUsage validates that all template variables used in a request are properly defined
func (dv *DependencyValidator) checkVariableUsage(request parser.Request, scenarioIdx, requestIdx int, globalVars, planVars, scenarioVars map[string]bool, extractedVars map[string]int, result *ValidationResult) {
	// Check URL for template variables
	dv.validateTemplateVariables(request.URL, fmt.Sprintf("scenarios[%d].requests[%d].url", scenarioIdx, requestIdx), globalVars, planVars, scenarioVars, extractedVars, requestIdx, result)

	// Check headers for template variables
	for headerName, headerValue := range request.Headers {
		dv.validateTemplateVariables(headerValue, fmt.Sprintf("scenarios[%d].requests[%d].headers[%s]", scenarioIdx, requestIdx, headerName), globalVars, planVars, scenarioVars, extractedVars, requestIdx, result)
	}

	// Check body for template variables (if it's a string)
	if bodyStr, ok := request.Body.(string); ok {
		dv.validateTemplateVariables(bodyStr, fmt.Sprintf("scenarios[%d].requests[%d].body", scenarioIdx, requestIdx), globalVars, planVars, scenarioVars, extractedVars, requestIdx, result)
	}
}

// validateTemplateVariables checks for template variable references and validates their availability
func (dv *DependencyValidator) validateTemplateVariables(content, fieldPath string, globalVars, planVars, scenarioVars map[string]bool, extractedVars map[string]int, currentRequestIdx int, result *ValidationResult) {
	// Look for template variables in the format {{.variable_name}} or {{variable_name}}
	// This is a simplified regex - in practice, you might want a more sophisticated parser
	templateVarPattern := `\{\{\.?([^}]+)\}\}`
	re, err := regexp.Compile(templateVarPattern)
	if err != nil {
		return // Skip validation if regex compilation fails
	}

	matches := re.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) < 2 {
			continue
		}

		varRef := strings.TrimSpace(match[1])

		// Handle nested variable references like .global.base_url
		parts := strings.Split(varRef, ".")
		varName := parts[0]

		// Special handling for certain built-in variables
		if varName == "global" || varName == "api_version" || varName == "timestamp" || varName == "random_int" {
			continue // These are built-in or special variables
		}

		// Check if variable is available
		varAvailable := false
		varSource := ""
		dependencyRequestIdx := -1

		if globalVars[varName] {
			varAvailable = true
			varSource = "global"
		} else if planVars[varName] {
			varAvailable = true
			varSource = "plan"
		} else if scenarioVars[varName] {
			varAvailable = true
			varSource = "scenario"
		} else if extractRequestIdx, exists := extractedVars[varName]; exists {
			if extractRequestIdx < currentRequestIdx {
				varAvailable = true
				varSource = "extracted"
				dependencyRequestIdx = extractRequestIdx
			} else {
				// Variable is extracted but in a later request - dependency order issue
				addIssue(result, ValidationIssue{
					Severity:   SeverityError,
					Code:       "FORWARD_DEPENDENCY",
					Message:    fmt.Sprintf("Variable '%s' is used before it's extracted (used at request %d, extracted at request %d)", varName, currentRequestIdx, extractRequestIdx),
					Field:      fieldPath,
					Value:      varRef,
					Category:   "dependency",
					Rule:       "dependency_order",
					Suggestion: fmt.Sprintf("Move the extraction of '%s' to an earlier request or reorder requests", varName),
				})
				continue
			}
		}

		if !varAvailable {
			addIssue(result, ValidationIssue{
				Severity:   SeverityError,
				Code:       "UNDEFINED_VARIABLE",
				Message:    fmt.Sprintf("Variable '%s' is used but not defined", varName),
				Field:      fieldPath,
				Value:      varRef,
				Category:   "dependency",
				Rule:       "variable_defined",
				Suggestion: "Define the variable in global, plan, scenario scope, or extract it from a previous request",
			})
		} else if varSource == "extracted" && dependencyRequestIdx >= 0 {
			// Log the dependency for potential circular reference detection
			// This could be enhanced to build a full dependency graph
		}
	}
}

// validateAssertionLogic validates assertion syntax and logic
func (dv *DependencyValidator) validateAssertionLogic(plan *parser.TestPlan, result *ValidationResult) {
	for scenarioIdx, scenario := range plan.Scenarios {
		for requestIdx, request := range scenario.Requests {
			for assertionIdx, assertion := range request.Assertions {
				prefix := fmt.Sprintf("scenarios[%d].requests[%d].assertions[%d]", scenarioIdx, requestIdx, assertionIdx)

				// Validate assertion completeness
				if assertion.Type == "" {
					addIssue(result, ValidationIssue{
						Severity: SeverityError,
						Code:     "MISSING_ASSERTION_TYPE",
						Message:  "Assertion type is required",
						Field:    fmt.Sprintf("%s.type", prefix),
						Category: "dependency",
						Rule:     "assertion_type_required",
					})
					continue
				}

				// Validate assertion logic based on type
				switch assertion.Type {
				case "status_code":
					if assertion.Value == nil {
						addIssue(result, ValidationIssue{
							Severity: SeverityError,
							Code:     "MISSING_STATUS_CODE_VALUE",
							Message:  "status_code assertion requires a value",
							Field:    fmt.Sprintf("%s.value", prefix),
							Category: "dependency",
							Rule:     "status_code_value_required",
						})
					} else if statusCode, ok := assertion.Value.(int); ok {
						if statusCode < 100 || statusCode > 599 {
							addIssue(result, ValidationIssue{
								Severity:   SeverityWarning,
								Code:       "INVALID_STATUS_CODE_RANGE",
								Message:    fmt.Sprintf("Status code %d is outside valid HTTP range (100-599)", statusCode),
								Field:      fmt.Sprintf("%s.value", prefix),
								Value:      statusCode,
								Category:   "dependency",
								Rule:       "valid_status_code",
								Suggestion: "Use standard HTTP status codes (100-599)",
							})
						}
					}

				case "response_time":
					if assertion.Value == nil {
						addIssue(result, ValidationIssue{
							Severity: SeverityError,
							Code:     "MISSING_RESPONSE_TIME_VALUE",
							Message:  "response_time assertion requires a value",
							Field:    fmt.Sprintf("%s.value", prefix),
							Category: "dependency",
							Rule:     "response_time_value_required",
						})
					} else if timeStr, ok := assertion.Value.(string); ok {
						if _, err := time.ParseDuration(timeStr); err != nil {
							addIssue(result, ValidationIssue{
								Severity:   SeverityError,
								Code:       "INVALID_DURATION_FORMAT",
								Message:    fmt.Sprintf("Invalid duration format '%s': %v", timeStr, err),
								Field:      fmt.Sprintf("%s.value", prefix),
								Value:      timeStr,
								Category:   "dependency",
								Rule:       "valid_duration",
								Suggestion: "Use valid Go duration format (e.g., '5s', '100ms', '2m')",
							})
						}
					}

				case "json_path":
					if assertion.Field == "" {
						addIssue(result, ValidationIssue{
							Severity: SeverityError,
							Code:     "MISSING_JSON_PATH_FIELD",
							Message:  "json_path assertion requires a field (JSONPath expression)",
							Field:    fmt.Sprintf("%s.field", prefix),
							Category: "dependency",
							Rule:     "json_path_field_required",
						})
					} else {
						// Basic JSONPath syntax validation
						if !strings.HasPrefix(assertion.Field, "$") {
							addIssue(result, ValidationIssue{
								Severity:   SeverityWarning,
								Code:       "INVALID_JSON_PATH_SYNTAX",
								Message:    fmt.Sprintf("JSONPath expression '%s' should start with '$'", assertion.Field),
								Field:      fmt.Sprintf("%s.field", prefix),
								Value:      assertion.Field,
								Category:   "dependency",
								Rule:       "json_path_syntax",
								Suggestion: "Use valid JSONPath syntax starting with '$' (e.g., '$.user.id')",
							})
						}
					}

				case "header_exists":
					if assertion.Field == "" {
						addIssue(result, ValidationIssue{
							Severity: SeverityError,
							Code:     "MISSING_HEADER_NAME",
							Message:  "header_exists assertion requires a field (header name)",
							Field:    fmt.Sprintf("%s.field", prefix),
							Category: "dependency",
							Rule:     "header_name_required",
						})
					}

				case "contains":
					if assertion.Value == nil {
						addIssue(result, ValidationIssue{
							Severity: SeverityError,
							Code:     "MISSING_CONTAINS_VALUE",
							Message:  "contains assertion requires a value to search for",
							Field:    fmt.Sprintf("%s.value", prefix),
							Category: "dependency",
							Rule:     "contains_value_required",
						})
					}
				}

				// Validate operator compatibility with assertion type
				if assertion.Operator != "" {
					dv.validateOperatorCompatibility(assertion, prefix, result)
				}
			}
		}
	}
}

// validateOperatorCompatibility checks if the operator is compatible with the assertion type
func (dv *DependencyValidator) validateOperatorCompatibility(assertion parser.Assertion, prefix string, result *ValidationResult) {
	validOperators := map[string][]string{
		"status_code":   {"eq", "ne", "lt", "le", "gt", "ge"},
		"response_time": {"lt", "le", "gt", "ge"},
		"contains":      {"contains", "not_contains"},
		"json_path":     {"eq", "ne", "lt", "le", "gt", "ge", "contains", "not_contains"},
		"header_exists": {"eq", "ne"}, // Usually just checking existence
	}

	if validOps, exists := validOperators[assertion.Type]; exists {
		opValid := false
		for _, validOp := range validOps {
			if assertion.Operator == validOp {
				opValid = true
				break
			}
		}

		if !opValid {
			addIssue(result, ValidationIssue{
				Severity:   SeverityError,
				Code:       "INCOMPATIBLE_OPERATOR",
				Message:    fmt.Sprintf("Operator '%s' is not compatible with assertion type '%s'. Valid operators: %s", assertion.Operator, assertion.Type, strings.Join(validOps, ", ")),
				Field:      fmt.Sprintf("%s.operator", prefix),
				Value:      assertion.Operator,
				Category:   "dependency",
				Rule:       "operator_compatibility",
				Suggestion: fmt.Sprintf("Use one of the valid operators for %s: %s", assertion.Type, strings.Join(validOps, ", ")),
			})
		}
	}
}

// detectCircularDependencies analyzes variable dependencies for circular references
func (dv *DependencyValidator) detectCircularDependencies(plan *parser.TestPlan, result *ValidationResult) {
	// Build a dependency graph for each scenario
	for scenarioIdx, scenario := range plan.Scenarios {
		dependencyGraph := make(map[int][]int) // request index -> list of dependent request indices
		extractionMap := make(map[string]int)  // variable name -> request index where it's extracted

		// Build the graph by analyzing variable extractions and usage
		for requestIdx, request := range scenario.Requests {
			// Record extractions
			for _, extract := range request.Extract {
				extractionMap[extract.Name] = requestIdx
			}
		}

		// Analyze usage to build dependency edges
		for requestIdx, request := range scenario.Requests {
			usedVars := dv.extractUsedVariables(request)

			for _, varName := range usedVars {
				if extractRequestIdx, exists := extractionMap[varName]; exists && extractRequestIdx != requestIdx {
					// This request depends on the request that extracts the variable
					dependencyGraph[requestIdx] = append(dependencyGraph[requestIdx], extractRequestIdx)
				}
			}
		}

		// Detect cycles using DFS
		visited := make(map[int]bool)
		recStack := make(map[int]bool)

		for requestIdx := range scenario.Requests {
			if !visited[requestIdx] {
				if dv.hasCycleDFS(requestIdx, dependencyGraph, visited, recStack) {
					addIssue(result, ValidationIssue{
						Severity:   SeverityError,
						Code:       "CIRCULAR_DEPENDENCY",
						Message:    fmt.Sprintf("Circular dependency detected in scenario '%s' involving request %d", scenario.Name, requestIdx),
						Field:      fmt.Sprintf("scenarios[%d].requests[%d]", scenarioIdx, requestIdx),
						Category:   "dependency",
						Rule:       "no_circular_dependencies",
						Suggestion: "Reorganize requests to eliminate circular variable dependencies",
					})
					break // Report only the first cycle found per scenario
				}
			}
		}
	}
}

// extractUsedVariables extracts all variable names used in a request
func (dv *DependencyValidator) extractUsedVariables(request parser.Request) []string {
	var usedVars []string
	templateVarPattern := `\{\{\.?([^}]+)\}\}`
	re, err := regexp.Compile(templateVarPattern)
	if err != nil {
		return usedVars
	}

	// Check URL
	matches := re.FindAllStringSubmatch(request.URL, -1)
	for _, match := range matches {
		if len(match) >= 2 {
			varRef := strings.TrimSpace(match[1])
			parts := strings.Split(varRef, ".")
			varName := parts[0]
			if varName != "global" && varName != "api_version" && varName != "timestamp" && varName != "random_int" {
				usedVars = append(usedVars, varName)
			}
		}
	}

	// Check headers
	for _, headerValue := range request.Headers {
		matches := re.FindAllStringSubmatch(headerValue, -1)
		for _, match := range matches {
			if len(match) >= 2 {
				varRef := strings.TrimSpace(match[1])
				parts := strings.Split(varRef, ".")
				varName := parts[0]
				if varName != "global" && varName != "api_version" && varName != "timestamp" && varName != "random_int" {
					usedVars = append(usedVars, varName)
				}
			}
		}
	}

	// Check body (if string)
	if bodyStr, ok := request.Body.(string); ok {
		matches := re.FindAllStringSubmatch(bodyStr, -1)
		for _, match := range matches {
			if len(match) >= 2 {
				varRef := strings.TrimSpace(match[1])
				parts := strings.Split(varRef, ".")
				varName := parts[0]
				if varName != "global" && varName != "api_version" && varName != "timestamp" && varName != "random_int" {
					usedVars = append(usedVars, varName)
				}
			}
		}
	}

	return usedVars
}

// hasCycleDFS performs DFS to detect cycles in the dependency graph
func (dv *DependencyValidator) hasCycleDFS(node int, graph map[int][]int, visited, recStack map[int]bool) bool {
	visited[node] = true
	recStack[node] = true

	for _, neighbor := range graph[node] {
		if !visited[neighbor] {
			if dv.hasCycleDFS(neighbor, graph, visited, recStack) {
				return true
			}
		} else if recStack[neighbor] {
			return true
		}
	}

	recStack[node] = false
	return false
}

// validateRequestFlow validates the logical flow of requests within scenarios
func (dv *DependencyValidator) validateRequestFlow(plan *parser.TestPlan, result *ValidationResult) {
	for scenarioIdx, scenario := range plan.Scenarios {
		if len(scenario.Requests) == 0 {
			continue
		}

		// Check for proper authentication flow
		dv.validateAuthenticationFlow(scenario, scenarioIdx, result)

		// Check for orphaned extractions (extracted but never used)
		dv.validateExtractionUsage(scenario, scenarioIdx, result)

		// Check for logical request ordering
		dv.validateRequestOrdering(scenario, scenarioIdx, result)
	}
}

// validateAuthenticationFlow checks for proper authentication patterns
func (dv *DependencyValidator) validateAuthenticationFlow(scenario parser.Scenario, scenarioIdx int, result *ValidationResult) {
	hasAuthExtraction := false
	hasAuthUsage := false
	authExtractionIdx := -1

	for requestIdx, request := range scenario.Requests {
		// Check for auth token extraction
		for _, extract := range request.Extract {
			if strings.Contains(strings.ToLower(extract.Name), "token") || strings.Contains(strings.ToLower(extract.Name), "auth") {
				hasAuthExtraction = true
				authExtractionIdx = requestIdx
			}
		}

		// Check for auth token usage in headers
		for headerName, headerValue := range request.Headers {
			if strings.ToLower(headerName) == "authorization" && strings.Contains(headerValue, "{{") {
				hasAuthUsage = true
				if authExtractionIdx >= 0 && requestIdx <= authExtractionIdx {
					addIssue(result, ValidationIssue{
						Severity:   SeverityWarning,
						Code:       "AUTH_BEFORE_EXTRACTION",
						Message:    fmt.Sprintf("Authorization header used before auth token extraction in scenario '%s'", scenario.Name),
						Field:      fmt.Sprintf("scenarios[%d].requests[%d].headers.Authorization", scenarioIdx, requestIdx),
						Category:   "dependency",
						Rule:       "auth_flow_order",
						Suggestion: "Ensure authentication requests come before requests that use auth tokens",
					})
				}
			}
		}
	}

	// Warn if auth token is extracted but never used
	if hasAuthExtraction && !hasAuthUsage {
		addIssue(result, ValidationIssue{
			Severity:   SeverityInfo,
			Code:       "UNUSED_AUTH_TOKEN",
			Message:    fmt.Sprintf("Auth token extracted but not used in scenario '%s'", scenario.Name),
			Field:      fmt.Sprintf("scenarios[%d].requests[%d].extract", scenarioIdx, authExtractionIdx),
			Category:   "dependency",
			Rule:       "extract_usage",
			Suggestion: "Consider removing unused extractions or use the auth token in subsequent requests",
		})
	}
}

// validateExtractionUsage checks for extracted variables that are never used
func (dv *DependencyValidator) validateExtractionUsage(scenario parser.Scenario, scenarioIdx int, result *ValidationResult) {
	extractedVars := make(map[string]int) // variable name -> request index where extracted
	usedVars := make(map[string]bool)     // variable name -> used

	// Collect all extractions
	for requestIdx, request := range scenario.Requests {
		for _, extract := range request.Extract {
			extractedVars[extract.Name] = requestIdx
		}
	}

	// Check usage in all requests
	for _, request := range scenario.Requests {
		usedVarsInRequest := dv.extractUsedVariables(request)
		for _, varName := range usedVarsInRequest {
			usedVars[varName] = true
		}
	}

	// Report unused extractions
	for varName, requestIdx := range extractedVars {
		if !usedVars[varName] {
			addIssue(result, ValidationIssue{
				Severity:   SeverityInfo,
				Code:       "UNUSED_EXTRACTION",
				Message:    fmt.Sprintf("Variable '%s' is extracted but never used in scenario '%s'", varName, scenario.Name),
				Field:      fmt.Sprintf("scenarios[%d].requests[%d].extract", scenarioIdx, requestIdx),
				Value:      varName,
				Category:   "dependency",
				Rule:       "extract_usage",
				Suggestion: "Remove unused extractions or use the variable in subsequent requests",
			})
		}
	}
}

// validateRequestOrdering checks for logical ordering issues
func (dv *DependencyValidator) validateRequestOrdering(scenario parser.Scenario, scenarioIdx int, result *ValidationResult) {
	// Check for common anti-patterns
	for requestIdx, request := range scenario.Requests {
		method := strings.ToUpper(request.Method)

		// Warn about DELETE followed by GET on the same resource
		if method == "DELETE" && requestIdx < len(scenario.Requests)-1 {
			nextRequest := scenario.Requests[requestIdx+1]
			if strings.ToUpper(nextRequest.Method) == "GET" {
				// Simple check if URLs might be related (same base path)
				if dv.sharesSimilarPath(request.URL, nextRequest.URL) {
					addIssue(result, ValidationIssue{
						Severity:   SeverityWarning,
						Code:       "GET_AFTER_DELETE",
						Message:    fmt.Sprintf("GET request follows DELETE on similar path in scenario '%s'", scenario.Name),
						Field:      fmt.Sprintf("scenarios[%d].requests[%d]", scenarioIdx, requestIdx+1),
						Category:   "dependency",
						Rule:       "logical_request_order",
						Suggestion: "Consider if GET after DELETE is intentional (e.g., testing 404 response)",
					})
				}
			}
		}

		// Warn about POST/PUT without checking the result
		if (method == "POST" || method == "PUT") && len(request.Assertions) == 0 {
			addIssue(result, ValidationIssue{
				Severity:   SeverityInfo,
				Code:       "WRITE_WITHOUT_ASSERTION",
				Message:    fmt.Sprintf("%s request without assertions in scenario '%s'", method, scenario.Name),
				Field:      fmt.Sprintf("scenarios[%d].requests[%d].assertions", scenarioIdx, requestIdx),
				Category:   "dependency",
				Rule:       "verify_writes",
				Suggestion: "Consider adding assertions to verify write operations succeed",
			})
		}
	}
}

// sharesSimilarPath checks if two URLs share a similar path (simple heuristic)
func (dv *DependencyValidator) sharesSimilarPath(url1, url2 string) bool {
	// Remove template variables for comparison
	cleanUrl1 := regexp.MustCompile(`\{\{[^}]+\}\}`).ReplaceAllString(url1, "")
	cleanUrl2 := regexp.MustCompile(`\{\{[^}]+\}\}`).ReplaceAllString(url2, "")

	// Extract path components
	parts1 := strings.Split(strings.Trim(cleanUrl1, "/"), "/")
	parts2 := strings.Split(strings.Trim(cleanUrl2, "/"), "/")

	// Check if they share the first 2 path components
	if len(parts1) >= 2 && len(parts2) >= 2 {
		return parts1[0] == parts2[0] && parts1[1] == parts2[1]
	}

	return false
}

// Helper function to add issues to validation result
func addIssue(result *ValidationResult, issue ValidationIssue) {
	result.Issues = append(result.Issues, issue)
}
