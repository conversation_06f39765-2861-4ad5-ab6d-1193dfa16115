// Package validation provides comprehensive test plan validation engine
package validation

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"neuralmetergo/internal/parser"
)

// ValidationSeverity represents the severity level of a validation issue
type ValidationSeverity int

const (
	SeverityError ValidationSeverity = iota
	SeverityWarning
	SeverityInfo
)

func (s ValidationSeverity) String() string {
	switch s {
	case SeverityError:
		return "error"
	case SeverityWarning:
		return "warning"
	case SeverityInfo:
		return "info"
	default:
		return "unknown"
	}
}

// ValidationIssue represents a single validation issue with detailed context
type ValidationIssue struct {
	Severity   ValidationSeverity `json:"severity"`
	Code       string             `json:"code"`
	Message    string             `json:"message"`
	Field      string             `json:"field"`
	Value      interface{}        `json:"value,omitempty"`
	Line       int                `json:"line,omitempty"`
	Column     int                `json:"column,omitempty"`
	Context    string             `json:"context,omitempty"`
	Suggestion string             `json:"suggestion,omitempty"`
	Rule       string             `json:"rule"`
	Category   string             `json:"category"`
}

// ValidationResult contains the complete validation results
type ValidationResult struct {
	Valid      bool              `json:"valid"`
	Issues     []ValidationIssue `json:"issues"`
	Summary    ValidationSummary `json:"summary"`
	SchemaInfo SchemaInfo        `json:"schema_info"`
}

// ValidationSummary provides a summary of validation results
type ValidationSummary struct {
	ErrorCount   int `json:"error_count"`
	WarningCount int `json:"warning_count"`
	InfoCount    int `json:"info_count"`
	TotalCount   int `json:"total_count"`
}

// SchemaInfo provides information about the schema used for validation
type SchemaInfo struct {
	Version     string    `json:"version"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	ValidatedAt time.Time `json:"validated_at"`
}

// ValidationEngine provides comprehensive test plan validation
type ValidationEngine struct {
	schema     *Schema
	config     *EngineConfig
	validators []Validator
}

// EngineConfig configures the validation engine behavior
type EngineConfig struct {
	StrictMode        bool     `json:"strict_mode"`
	EnableWarnings    bool     `json:"enable_warnings"`
	EnableSuggestions bool     `json:"enable_suggestions"`
	SkipCategories    []string `json:"skip_categories"`
	MaxIssues         int      `json:"max_issues"`
	PerformanceChecks bool     `json:"performance_checks"`
}

// DefaultConfig returns a default configuration for the validation engine
func DefaultConfig() *EngineConfig {
	return &EngineConfig{
		StrictMode:        false,
		EnableWarnings:    true,
		EnableSuggestions: true,
		SkipCategories:    []string{},
		MaxIssues:         100,
		PerformanceChecks: true,
	}
}

// Validator interface for different types of validation rules
type Validator interface {
	Validate(plan *parser.TestPlan, result *ValidationResult)
	Category() string
	Description() string
}

// NewValidationEngine creates a new validation engine with default configuration
func NewValidationEngine() *ValidationEngine {
	return NewValidationEngineWithConfig(DefaultConfig())
}

// NewValidationEngineWithConfig creates a new validation engine with custom configuration
func NewValidationEngineWithConfig(config *EngineConfig) *ValidationEngine {
	engine := &ValidationEngine{
		schema: NewDefaultSchema(),
		config: config,
	}

	// Register default validators
	engine.registerDefaultValidators()

	return engine
}

// registerDefaultValidators registers all built-in validators
func (ve *ValidationEngine) registerDefaultValidators() {
	ve.validators = []Validator{
		NewStructureValidator(),
		NewHTTPValidator(),
		NewPerformanceValidator(),
		NewSemanticValidator(),
		NewDependencyValidator(),
	}
}

// Validate performs comprehensive validation of a test plan
func (ve *ValidationEngine) Validate(plan *parser.TestPlan) *ValidationResult {
	result := &ValidationResult{
		Valid:   true,
		Issues:  []ValidationIssue{},
		Summary: ValidationSummary{},
		SchemaInfo: SchemaInfo{
			Version:     ve.schema.Version,
			Name:        ve.schema.Name,
			Description: ve.schema.Description,
			ValidatedAt: time.Now(),
		},
	}

	// Run all validators with filtered results
	for _, validator := range ve.validators {
		// Skip categories if configured to do so
		if ve.shouldSkipCategory(validator.Category()) {
			continue
		}

		// Create a temporary result for this validator
		tempResult := &ValidationResult{
			Valid:  true,
			Issues: []ValidationIssue{},
		}

		validator.Validate(plan, tempResult)

		// Filter and add issues from this validator
		for _, issue := range tempResult.Issues {
			ve.addIssue(result, issue)

			// Check if we've reached the maximum number of issues
			if ve.config.MaxIssues > 0 && len(result.Issues) >= ve.config.MaxIssues {
				ve.addIssue(result, ValidationIssue{
					Severity: SeverityWarning,
					Code:     "MAX_ISSUES_REACHED",
					Message:  fmt.Sprintf("Maximum number of validation issues (%d) reached. Some issues may not be reported.", ve.config.MaxIssues),
					Category: "system",
					Rule:     "max_issues_limit",
				})
				return result
			}
		}
	}

	// Calculate summary
	ve.calculateSummary(result)

	// Set overall validity
	result.Valid = result.Summary.ErrorCount == 0

	return result
}

// ValidateWithParser validates a test plan using the parser first, then the validation engine
func (ve *ValidationEngine) ValidateWithParser(data []byte) (*ValidationResult, *parser.TestPlan, error) {
	// First, use the parser to parse and do basic validation
	p := parser.NewParser()
	plan, err := p.ParseBytes(data)
	if err != nil {
		// Convert parser error to validation result
		result := &ValidationResult{
			Valid:  false,
			Issues: []ValidationIssue{},
			SchemaInfo: SchemaInfo{
				Version:     ve.schema.Version,
				Name:        ve.schema.Name,
				Description: ve.schema.Description,
				ValidatedAt: time.Now(),
			},
		}

		// Try to extract meaningful information from parser error
		if parseErr, ok := err.(*parser.ParseError); ok {
			ve.addIssue(result, ValidationIssue{
				Severity: SeverityError,
				Code:     "PARSE_ERROR",
				Message:  parseErr.Message,
				Line:     parseErr.Line,
				Column:   parseErr.Column,
				Context:  parseErr.Context,
				Category: "parsing",
				Rule:     "yaml_syntax",
			})
		} else {
			ve.addIssue(result, ValidationIssue{
				Severity: SeverityError,
				Code:     "PARSE_ERROR",
				Message:  err.Error(),
				Category: "parsing",
				Rule:     "yaml_syntax",
			})
		}

		ve.calculateSummary(result)
		return result, nil, err
	}

	// If parsing succeeded, run comprehensive validation
	result := ve.Validate(plan)
	return result, plan, nil
}

// addIssue adds a validation issue to the result
func (ve *ValidationEngine) addIssue(result *ValidationResult, issue ValidationIssue) {
	// Skip warnings if they're disabled
	if issue.Severity == SeverityWarning && !ve.config.EnableWarnings {
		return
	}

	// Skip info messages if suggestions are disabled
	if issue.Severity == SeverityInfo && !ve.config.EnableSuggestions {
		return
	}

	result.Issues = append(result.Issues, issue)
}

// shouldSkipCategory checks if a validation category should be skipped
func (ve *ValidationEngine) shouldSkipCategory(category string) bool {
	for _, skip := range ve.config.SkipCategories {
		if skip == category {
			return true
		}
	}
	return false
}

// calculateSummary calculates the validation summary
func (ve *ValidationEngine) calculateSummary(result *ValidationResult) {
	result.Summary = ValidationSummary{}

	for _, issue := range result.Issues {
		switch issue.Severity {
		case SeverityError:
			result.Summary.ErrorCount++
		case SeverityWarning:
			result.Summary.WarningCount++
		case SeverityInfo:
			result.Summary.InfoCount++
		}
	}

	result.Summary.TotalCount = len(result.Issues)
}

// GetValidationReport generates a human-readable validation report
func (ve *ValidationEngine) GetValidationReport(result *ValidationResult) string {
	var report strings.Builder

	report.WriteString(fmt.Sprintf("Test Plan Validation Report\n"))
	report.WriteString(fmt.Sprintf("===========================\n\n"))
	report.WriteString(fmt.Sprintf("Schema: %s v%s\n", result.SchemaInfo.Name, result.SchemaInfo.Version))
	report.WriteString(fmt.Sprintf("Validated: %s\n", result.SchemaInfo.ValidatedAt.Format(time.RFC3339)))
	report.WriteString(fmt.Sprintf("Overall Status: %s\n\n", map[bool]string{true: "VALID", false: "INVALID"}[result.Valid]))

	report.WriteString(fmt.Sprintf("Summary:\n"))
	report.WriteString(fmt.Sprintf("  Errors: %d\n", result.Summary.ErrorCount))
	report.WriteString(fmt.Sprintf("  Warnings: %d\n", result.Summary.WarningCount))
	report.WriteString(fmt.Sprintf("  Info: %d\n", result.Summary.InfoCount))
	report.WriteString(fmt.Sprintf("  Total Issues: %d\n\n", result.Summary.TotalCount))

	if len(result.Issues) > 0 {
		report.WriteString("Issues:\n")
		report.WriteString("-------\n")

		for i, issue := range result.Issues {
			report.WriteString(fmt.Sprintf("%d. [%s] %s\n", i+1, strings.ToUpper(issue.Severity.String()), issue.Message))
			if issue.Field != "" {
				report.WriteString(fmt.Sprintf("   Field: %s\n", issue.Field))
			}
			if issue.Line > 0 {
				report.WriteString(fmt.Sprintf("   Location: line %d", issue.Line))
				if issue.Column > 0 {
					report.WriteString(fmt.Sprintf(", column %d", issue.Column))
				}
				report.WriteString("\n")
			}
			if issue.Code != "" {
				report.WriteString(fmt.Sprintf("   Code: %s\n", issue.Code))
			}
			if issue.Suggestion != "" {
				report.WriteString(fmt.Sprintf("   Suggestion: %s\n", issue.Suggestion))
			}
			report.WriteString("\n")
		}
	}

	return report.String()
}

// Helper function to create validation issues with common fields
func createIssue(severity ValidationSeverity, code, message, field, category, rule string) ValidationIssue {
	return ValidationIssue{
		Severity: severity,
		Code:     code,
		Message:  message,
		Field:    field,
		Category: category,
		Rule:     rule,
	}
}

// Helper function to validate URL format with detailed feedback
func validateURLFormat(url string) (bool, string) {
	if url == "" {
		return false, "URL cannot be empty"
	}

	// Check for template variables
	if strings.Contains(url, "{{") && strings.Contains(url, "}}") {
		// Basic template URL validation
		templateRegex := regexp.MustCompile(`\{\{[^}]+\}\}`)
		if !templateRegex.MatchString(url) {
			return false, "Invalid template syntax in URL"
		}
		return true, ""
	}

	// Validate absolute URLs
	if strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://") {
		// Basic URL pattern validation
		urlRegex := regexp.MustCompile(`^https?://[a-zA-Z0-9\-\.]+[a-zA-Z]{2,}(:[0-9]+)?(/.*)?$`)
		if !urlRegex.MatchString(url) {
			return false, "Invalid absolute URL format"
		}
		return true, ""
	}

	// Validate relative URLs (should start with /)
	if strings.HasPrefix(url, "/") {
		return true, ""
	}

	return false, "URL must be absolute (http/https) or relative (starting with /)"
}

// Helper function to validate HTTP method
func validateHTTPMethod(method string) (bool, string) {
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "TRACE", "CONNECT"}

	for _, validMethod := range validMethods {
		if method == validMethod {
			return true, ""
		}
	}

	return false, fmt.Sprintf("Invalid HTTP method '%s'. Valid methods: %s", method, strings.Join(validMethods, ", "))
}

// IsValid returns true if the validation result has no errors
func (vr *ValidationResult) IsValid() bool {
	return vr.Summary.ErrorCount == 0
}

// GenerateReport generates a human-readable validation report
func (vr *ValidationResult) GenerateReport() string {
	var report strings.Builder

	if len(vr.Issues) == 0 {
		return ""
	}

	report.WriteString(fmt.Sprintf("Test Plan Validation Report\n"))
	report.WriteString(fmt.Sprintf("===========================\n\n"))
	report.WriteString(fmt.Sprintf("Schema: %s v%s\n", vr.SchemaInfo.Name, vr.SchemaInfo.Version))
	report.WriteString(fmt.Sprintf("Validated: %s\n", vr.SchemaInfo.ValidatedAt.Format(time.RFC3339)))
	report.WriteString(fmt.Sprintf("Overall Status: %s\n\n", map[bool]string{true: "VALID", false: "INVALID"}[vr.Valid]))

	report.WriteString(fmt.Sprintf("Summary:\n"))
	report.WriteString(fmt.Sprintf("  Errors: %d\n", vr.Summary.ErrorCount))
	report.WriteString(fmt.Sprintf("  Warnings: %d\n", vr.Summary.WarningCount))
	report.WriteString(fmt.Sprintf("  Info: %d\n", vr.Summary.InfoCount))
	report.WriteString(fmt.Sprintf("  Total Issues: %d\n\n", vr.Summary.TotalCount))

	if len(vr.Issues) > 0 {
		report.WriteString("Issues:\n")
		report.WriteString("-------\n")

		for i, issue := range vr.Issues {
			report.WriteString(fmt.Sprintf("%d. [%s] %s\n", i+1, strings.ToUpper(issue.Severity.String()), issue.Message))
			if issue.Field != "" {
				report.WriteString(fmt.Sprintf("   Field: %s\n", issue.Field))
			}
			if issue.Line > 0 {
				report.WriteString(fmt.Sprintf("   Location: line %d", issue.Line))
				if issue.Column > 0 {
					report.WriteString(fmt.Sprintf(", column %d", issue.Column))
				}
				report.WriteString("\n")
			}
			if issue.Code != "" {
				report.WriteString(fmt.Sprintf("   Code: %s\n", issue.Code))
			}
			if issue.Suggestion != "" {
				report.WriteString(fmt.Sprintf("   Suggestion: %s\n", issue.Suggestion))
			}
			report.WriteString("\n")
		}
	}

	return report.String()
}

// ValidateTestPlan is an alias for Validate for backwards compatibility
func (ve *ValidationEngine) ValidateTestPlan(plan *parser.TestPlan) *ValidationResult {
	return ve.Validate(plan)
}

// ClearValidators removes all validators from the engine
func (ve *ValidationEngine) ClearValidators() {
	ve.validators = []Validator{}
}

// AddValidator adds a new validator to the engine
func (ve *ValidationEngine) AddValidator(validator Validator) {
	ve.validators = append(ve.validators, validator)
}
