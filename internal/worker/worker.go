// Package worker provides goroutine-based worker pool functionality for load testing
package worker

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"

	"neuralmetergo/internal/client"
)

// Custom error types for job queue operations
var (
	ErrQueueFull   = errors.New("job queue is full")
	ErrQueueClosed = errors.New("job queue is closed")
	ErrQueueEmpty  = errors.New("job queue is empty")
	ErrInvalidJob  = errors.New("invalid job data")
)

// Job represents a work item to be processed by a worker
type Job struct {
	ID      string                 `json:"id" yaml:"id"`
	Type    string                 `json:"type" yaml:"type"`
	Payload map[string]interface{} `json:"payload" yaml:"payload"`

	// Additional metadata for job processing
	CreatedAt time.Time `json:"created_at" yaml:"created_at"`
	Priority  int       `json:"priority,omitempty" yaml:"priority,omitempty"`
}

// JobResult represents the result of processing a job
type JobResult struct {
	JobID    string                 `json:"job_id" yaml:"job_id"`
	Success  bool                   `json:"success" yaml:"success"`
	Duration int64                  `json:"duration_ms" yaml:"duration_ms"`
	Data     map[string]interface{} `json:"data,omitempty" yaml:"data,omitempty"`
	Error    string                 `json:"error,omitempty" yaml:"error,omitempty"`

	// Additional result metadata
	ProcessedAt time.Time `json:"processed_at" yaml:"processed_at"`
	WorkerID    int       `json:"worker_id,omitempty" yaml:"worker_id,omitempty"`
}

// QueueMetrics tracks job queue performance metrics
type QueueMetrics struct {
	EnqueuedTotal   int64 // Total jobs enqueued
	DequeuedTotal   int64 // Total jobs dequeued
	EnqueueErrors   int64 // Number of enqueue errors
	DequeueErrors   int64 // Number of dequeue errors
	CurrentSize     int64 // Current queue size
	PeakSize        int64 // Peak queue size
	LastEnqueueTime int64 // Unix timestamp of last enqueue
	LastDequeueTime int64 // Unix timestamp of last dequeue
}

// WorkerMetrics tracks individual worker performance metrics
type WorkerMetrics struct {
	JobsProcessed   int64 // Total jobs processed by this worker
	JobsSuccessful  int64 // Successful job completions
	JobsFailed      int64 // Failed job completions
	TotalDuration   int64 // Total processing time in milliseconds
	AverageDuration int64 // Average processing time in milliseconds
	LastJobTime     int64 // Unix timestamp of last job processed
}

// JobQueue manages the queue of jobs to be processed
type JobQueue struct {
	jobs     chan Job
	results  chan JobResult
	capacity int
	closed   int32 // atomic flag for closed state
	metrics  QueueMetrics
	mu       sync.RWMutex
}

// Worker represents a single worker goroutine with HTTP client integration
type Worker struct {
	ID         int
	jobQueue   *JobQueue
	quit       chan bool
	pool       *WorkerPool
	httpClient *client.HTTPClient
	metrics    WorkerMetrics
	running    int32 // atomic flag for running state
}

// WorkerPool manages a pool of worker goroutines
type WorkerPool struct {
	workers    []*Worker
	jobQueue   *JobQueue
	maxWorkers int
	minWorkers int
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup

	// Enhanced lifecycle management
	running     int32 // atomic flag for pool running state
	initialized int32 // atomic flag for initialization state

	// Pool configuration
	config PoolConfig

	// Health monitoring
	healthCheckInterval time.Duration
	healthTicker        *time.Ticker

	// Metrics and monitoring
	mu          sync.RWMutex
	poolMetrics PoolMetrics

	// Channels for pool management
	workerDone   chan int  // Worker ID when worker exits
	healthDone   chan bool // Signal health monitor to stop
	shutdownDone chan bool // Signal when shutdown is complete
}

// PoolConfig contains configuration for worker pool behavior
type PoolConfig struct {
	MinWorkers          int           `json:"min_workers" yaml:"min_workers"`
	MaxWorkers          int           `json:"max_workers" yaml:"max_workers"`
	HealthCheckInterval time.Duration `json:"health_check_interval" yaml:"health_check_interval"`
	WorkerIdleTimeout   time.Duration `json:"worker_idle_timeout" yaml:"worker_idle_timeout"`
	ShutdownTimeout     time.Duration `json:"shutdown_timeout" yaml:"shutdown_timeout"`
	AutoScale           bool          `json:"auto_scale" yaml:"auto_scale"`
	ScaleUpThreshold    float64       `json:"scale_up_threshold" yaml:"scale_up_threshold"`
	ScaleDownThreshold  float64       `json:"scale_down_threshold" yaml:"scale_down_threshold"`
}

// PoolMetrics tracks worker pool performance and health metrics
type PoolMetrics struct {
	ActiveWorkers         int32         `json:"active_workers" yaml:"active_workers"`
	IdleWorkers           int32         `json:"idle_workers" yaml:"idle_workers"`
	TotalJobsProcessed    int64         `json:"total_jobs_processed" yaml:"total_jobs_processed"`
	TotalJobsSuccessful   int64         `json:"total_jobs_successful" yaml:"total_jobs_successful"`
	TotalJobsFailed       int64         `json:"total_jobs_failed" yaml:"total_jobs_failed"`
	AverageProcessingTime int64         `json:"average_processing_time_ms" yaml:"average_processing_time_ms"`
	PoolUptime            time.Duration `json:"pool_uptime" yaml:"pool_uptime"`
	StartTime             time.Time     `json:"start_time" yaml:"start_time"`
	LastScaleEvent        time.Time     `json:"last_scale_event" yaml:"last_scale_event"`
	ScaleUpEvents         int64         `json:"scale_up_events" yaml:"scale_up_events"`
	ScaleDownEvents       int64         `json:"scale_down_events" yaml:"scale_down_events"`
	WorkerFailures        int64         `json:"worker_failures" yaml:"worker_failures"`
	HealthCheckPasses     int64         `json:"health_check_passes" yaml:"health_check_passes"`
	HealthCheckFailures   int64         `json:"health_check_failures" yaml:"health_check_failures"`
}

// DefaultPoolConfig returns a default configuration for the worker pool
func DefaultPoolConfig() PoolConfig {
	return PoolConfig{
		MinWorkers:          1,
		MaxWorkers:          10,
		HealthCheckInterval: 30 * time.Second,
		WorkerIdleTimeout:   5 * time.Minute,
		ShutdownTimeout:     30 * time.Second,
		AutoScale:           true,
		ScaleUpThreshold:    0.8, // Scale up when 80% of workers are busy
		ScaleDownThreshold:  0.3, // Scale down when less than 30% of workers are busy
	}
}

// NewJobQueue creates a new job queue with the specified capacity
func NewJobQueue(capacity int) *JobQueue {
	if capacity <= 0 {
		capacity = 1000 // Default capacity
	}

	return &JobQueue{
		jobs:     make(chan Job, capacity),
		results:  make(chan JobResult, capacity),
		capacity: capacity,
		closed:   0,
		metrics:  QueueMetrics{},
	}
}

// Enqueue adds a job to the queue in a thread-safe manner
func (jq *JobQueue) Enqueue(job Job) error {
	// Check if queue is closed
	if atomic.LoadInt32(&jq.closed) == 1 {
		atomic.AddInt64(&jq.metrics.EnqueueErrors, 1)
		return ErrQueueClosed
	}

	// Validate job data
	if job.ID == "" || job.Type == "" {
		atomic.AddInt64(&jq.metrics.EnqueueErrors, 1)
		return ErrInvalidJob
	}

	// Set creation time if not set
	if job.CreatedAt.IsZero() {
		job.CreatedAt = time.Now()
	}

	// Try to enqueue with non-blocking select
	select {
	case jq.jobs <- job:
		// Successfully enqueued
		atomic.AddInt64(&jq.metrics.EnqueuedTotal, 1)
		currentSize := atomic.AddInt64(&jq.metrics.CurrentSize, 1)
		atomic.StoreInt64(&jq.metrics.LastEnqueueTime, time.Now().Unix())

		// Update peak size if necessary
		for {
			peak := atomic.LoadInt64(&jq.metrics.PeakSize)
			if currentSize <= peak || atomic.CompareAndSwapInt64(&jq.metrics.PeakSize, peak, currentSize) {
				break
			}
		}

		return nil
	default:
		// Queue is full
		atomic.AddInt64(&jq.metrics.EnqueueErrors, 1)
		return ErrQueueFull
	}
}

// EnqueueWithTimeout adds a job to the queue with a timeout
func (jq *JobQueue) EnqueueWithTimeout(job Job, timeout time.Duration) error {
	// Check if queue is closed
	if atomic.LoadInt32(&jq.closed) == 1 {
		atomic.AddInt64(&jq.metrics.EnqueueErrors, 1)
		return ErrQueueClosed
	}

	// Validate job data
	if job.ID == "" || job.Type == "" {
		atomic.AddInt64(&jq.metrics.EnqueueErrors, 1)
		return ErrInvalidJob
	}

	// Set creation time if not set
	if job.CreatedAt.IsZero() {
		job.CreatedAt = time.Now()
	}

	// Try to enqueue with timeout
	select {
	case jq.jobs <- job:
		// Successfully enqueued
		atomic.AddInt64(&jq.metrics.EnqueuedTotal, 1)
		currentSize := atomic.AddInt64(&jq.metrics.CurrentSize, 1)
		atomic.StoreInt64(&jq.metrics.LastEnqueueTime, time.Now().Unix())

		// Update peak size if necessary
		for {
			peak := atomic.LoadInt64(&jq.metrics.PeakSize)
			if currentSize <= peak || atomic.CompareAndSwapInt64(&jq.metrics.PeakSize, peak, currentSize) {
				break
			}
		}

		return nil
	case <-time.After(timeout):
		atomic.AddInt64(&jq.metrics.EnqueueErrors, 1)
		return ErrQueueFull
	}
}

// Dequeue removes and returns a job from the queue
func (jq *JobQueue) Dequeue() (Job, error) {
	select {
	case job := <-jq.jobs:
		// Successfully dequeued
		atomic.AddInt64(&jq.metrics.DequeuedTotal, 1)
		atomic.AddInt64(&jq.metrics.CurrentSize, -1)
		atomic.StoreInt64(&jq.metrics.LastDequeueTime, time.Now().Unix())
		return job, nil
	default:
		// Queue is empty
		atomic.AddInt64(&jq.metrics.DequeueErrors, 1)
		if atomic.LoadInt32(&jq.closed) == 1 {
			return Job{}, ErrQueueClosed
		}
		return Job{}, ErrQueueEmpty
	}
}

// DequeueWithTimeout removes and returns a job from the queue with a timeout
func (jq *JobQueue) DequeueWithTimeout(timeout time.Duration) (Job, error) {
	select {
	case job := <-jq.jobs:
		// Successfully dequeued
		atomic.AddInt64(&jq.metrics.DequeuedTotal, 1)
		atomic.AddInt64(&jq.metrics.CurrentSize, -1)
		atomic.StoreInt64(&jq.metrics.LastDequeueTime, time.Now().Unix())
		return job, nil
	case <-time.After(timeout):
		atomic.AddInt64(&jq.metrics.DequeueErrors, 1)
		if atomic.LoadInt32(&jq.closed) == 1 {
			return Job{}, ErrQueueClosed
		}
		return Job{}, ErrQueueEmpty
	}
}

// DequeueBlocking removes and returns a job from the queue, blocking until available
func (jq *JobQueue) DequeueBlocking() (Job, error) {
	job, ok := <-jq.jobs
	if !ok {
		atomic.AddInt64(&jq.metrics.DequeueErrors, 1)
		return Job{}, ErrQueueClosed
	}

	// Successfully dequeued
	atomic.AddInt64(&jq.metrics.DequeuedTotal, 1)
	atomic.AddInt64(&jq.metrics.CurrentSize, -1)
	atomic.StoreInt64(&jq.metrics.LastDequeueTime, time.Now().Unix())
	return job, nil
}

// EnqueueResult adds a job result to the results queue
func (jq *JobQueue) EnqueueResult(result JobResult) error {
	if atomic.LoadInt32(&jq.closed) == 1 {
		return ErrQueueClosed
	}

	// Set processed time if not set
	if result.ProcessedAt.IsZero() {
		result.ProcessedAt = time.Now()
	}

	select {
	case jq.results <- result:
		return nil
	default:
		return ErrQueueFull
	}
}

// DequeueResult removes and returns a job result from the results queue
func (jq *JobQueue) DequeueResult() (JobResult, error) {
	select {
	case result := <-jq.results:
		return result, nil
	default:
		if atomic.LoadInt32(&jq.closed) == 1 {
			return JobResult{}, ErrQueueClosed
		}
		return JobResult{}, ErrQueueEmpty
	}
}

// Size returns the current number of jobs in the queue
func (jq *JobQueue) Size() int64 {
	return atomic.LoadInt64(&jq.metrics.CurrentSize)
}

// Capacity returns the maximum capacity of the queue
func (jq *JobQueue) Capacity() int {
	return jq.capacity
}

// IsFull returns true if the queue is at capacity
func (jq *JobQueue) IsFull() bool {
	return len(jq.jobs) >= jq.capacity
}

// IsEmpty returns true if the queue has no jobs
func (jq *JobQueue) IsEmpty() bool {
	return len(jq.jobs) == 0
}

// IsClosed returns true if the queue is closed
func (jq *JobQueue) IsClosed() bool {
	return atomic.LoadInt32(&jq.closed) == 1
}

// GetMetrics returns a copy of the current queue metrics
func (jq *JobQueue) GetMetrics() QueueMetrics {
	return QueueMetrics{
		EnqueuedTotal:   atomic.LoadInt64(&jq.metrics.EnqueuedTotal),
		DequeuedTotal:   atomic.LoadInt64(&jq.metrics.DequeuedTotal),
		EnqueueErrors:   atomic.LoadInt64(&jq.metrics.EnqueueErrors),
		DequeueErrors:   atomic.LoadInt64(&jq.metrics.DequeueErrors),
		CurrentSize:     atomic.LoadInt64(&jq.metrics.CurrentSize),
		PeakSize:        atomic.LoadInt64(&jq.metrics.PeakSize),
		LastEnqueueTime: atomic.LoadInt64(&jq.metrics.LastEnqueueTime),
		LastDequeueTime: atomic.LoadInt64(&jq.metrics.LastDequeueTime),
	}
}

// Clear removes all jobs from the queue
func (jq *JobQueue) Clear() {
	jq.mu.Lock()
	defer jq.mu.Unlock()

	// Drain the jobs channel
	for {
		select {
		case <-jq.jobs:
			atomic.AddInt64(&jq.metrics.CurrentSize, -1)
		default:
			goto clearResults
		}
	}

clearResults:
	// Drain the results channel
	for {
		select {
		case <-jq.results:
		default:
			return
		}
	}
}

// Close gracefully shuts down the job queue
func (jq *JobQueue) Close() {
	if atomic.CompareAndSwapInt32(&jq.closed, 0, 1) {
		close(jq.jobs)
		close(jq.results)
	}
}

// NewWorkerPool creates a new worker pool
func NewWorkerPool(maxWorkers int, jobQueue *JobQueue) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())
	config := DefaultPoolConfig()
	config.MaxWorkers = maxWorkers

	return &WorkerPool{
		maxWorkers:          maxWorkers,
		minWorkers:          config.MinWorkers,
		jobQueue:            jobQueue,
		ctx:                 ctx,
		cancel:              cancel,
		config:              config,
		healthCheckInterval: config.HealthCheckInterval,
		poolMetrics:         PoolMetrics{StartTime: time.Now()},
		workerDone:          make(chan int, maxWorkers),
		healthDone:          make(chan bool, 1),
		shutdownDone:        make(chan bool, 1),
		running:             0,
		initialized:         0,
	}
}

// NewWorkerPoolWithConfig creates a new worker pool with custom configuration
func NewWorkerPoolWithConfig(config PoolConfig, jobQueue *JobQueue) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())

	return &WorkerPool{
		maxWorkers:          config.MaxWorkers,
		minWorkers:          config.MinWorkers,
		jobQueue:            jobQueue,
		ctx:                 ctx,
		cancel:              cancel,
		config:              config,
		healthCheckInterval: config.HealthCheckInterval,
		poolMetrics:         PoolMetrics{StartTime: time.Now()},
		workerDone:          make(chan int, config.MaxWorkers),
		healthDone:          make(chan bool, 1),
		shutdownDone:        make(chan bool, 1),
		running:             0,
		initialized:         0,
	}
}

// Initialize sets up the worker pool and spawns initial workers
func (wp *WorkerPool) Initialize() error {
	if !atomic.CompareAndSwapInt32(&wp.initialized, 0, 1) {
		return errors.New("worker pool is already initialized")
	}

	log.Printf("Initializing worker pool with min=%d, max=%d workers", wp.minWorkers, wp.maxWorkers)

	// Spawn initial workers (minimum workers)
	wp.mu.Lock()
	wp.workers = make([]*Worker, 0, wp.maxWorkers)
	wp.mu.Unlock()

	for i := 0; i < wp.minWorkers; i++ {
		if err := wp.addWorker(); err != nil {
			log.Printf("Failed to add initial worker %d: %v", i, err)
			return fmt.Errorf("failed to initialize worker pool: %v", err)
		}
	}

	log.Printf("Worker pool initialized with %d workers", wp.minWorkers)
	return nil
}

// Start begins the worker pool operation including health monitoring
func (wp *WorkerPool) Start() error {
	if !atomic.CompareAndSwapInt32(&wp.running, 0, 1) {
		return errors.New("worker pool is already running")
	}

	if atomic.LoadInt32(&wp.initialized) == 0 {
		if err := wp.Initialize(); err != nil {
			atomic.StoreInt32(&wp.running, 0)
			return err
		}
	}

	log.Printf("Starting worker pool with %d workers", len(wp.workers))

	// Start all workers
	wp.mu.RLock()
	for _, worker := range wp.workers {
		worker.Start()
		wp.wg.Add(1)
	}
	wp.mu.RUnlock()

	// Start health monitoring
	if wp.config.HealthCheckInterval > 0 {
		wp.startHealthMonitoring()
	}

	// Start worker lifecycle management
	go wp.manageWorkerLifecycle()

	log.Printf("Worker pool started successfully")
	return nil
}

// Stop gracefully shuts down the worker pool
func (wp *WorkerPool) Stop() error {
	if !atomic.CompareAndSwapInt32(&wp.running, 1, 0) {
		return errors.New("worker pool is not running")
	}

	log.Printf("Stopping worker pool...")

	// Cancel context to signal all workers to stop
	wp.cancel()

	// Stop health monitoring
	wp.stopHealthMonitoring()

	// Wait for all workers to complete with timeout
	done := make(chan struct{})
	go func() {
		wp.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Printf("All workers stopped gracefully")
	case <-time.After(wp.config.ShutdownTimeout):
		log.Printf("Worker pool shutdown timeout reached, forcing shutdown")
	}

	// Clean up workers
	wp.mu.Lock()
	for _, worker := range wp.workers {
		worker.Stop()
	}
	wp.workers = wp.workers[:0]
	wp.mu.Unlock()

	// Signal shutdown completion
	select {
	case wp.shutdownDone <- true:
	default:
	}

	log.Printf("Worker pool stopped")
	return nil
}

// IsRunning returns true if the worker pool is currently running
func (wp *WorkerPool) IsRunning() bool {
	return atomic.LoadInt32(&wp.running) == 1
}

// IsInitialized returns true if the worker pool has been initialized
func (wp *WorkerPool) IsInitialized() bool {
	return atomic.LoadInt32(&wp.initialized) == 1
}

// NewWorker creates a new worker with HTTP client integration
func NewWorker(id int, jobQueue *JobQueue, pool *WorkerPool) *Worker {
	// Create HTTP client with default configuration for load testing
	httpConfig := client.DefaultConfig()
	httpClient := client.NewHTTPClient(httpConfig)

	return &Worker{
		ID:         id,
		jobQueue:   jobQueue,
		quit:       make(chan bool),
		pool:       pool,
		httpClient: httpClient,
		metrics:    WorkerMetrics{},
		running:    0,
	}
}

// Start begins the worker goroutine to process jobs from the queue
func (w *Worker) Start() {
	if !atomic.CompareAndSwapInt32(&w.running, 0, 1) {
		return // Worker is already running
	}

	go w.run()
}

// Stop gracefully shuts down the worker
func (w *Worker) Stop() {
	if atomic.CompareAndSwapInt32(&w.running, 1, 0) {
		close(w.quit)
	}
}

// IsRunning returns true if the worker is currently running
func (w *Worker) IsRunning() bool {
	return atomic.LoadInt32(&w.running) == 1
}

// GetMetrics returns a copy of the worker's performance metrics
func (w *Worker) GetMetrics() WorkerMetrics {
	return WorkerMetrics{
		JobsProcessed:   atomic.LoadInt64(&w.metrics.JobsProcessed),
		JobsSuccessful:  atomic.LoadInt64(&w.metrics.JobsSuccessful),
		JobsFailed:      atomic.LoadInt64(&w.metrics.JobsFailed),
		TotalDuration:   atomic.LoadInt64(&w.metrics.TotalDuration),
		AverageDuration: atomic.LoadInt64(&w.metrics.AverageDuration),
		LastJobTime:     atomic.LoadInt64(&w.metrics.LastJobTime),
	}
}

// run is the main worker loop that processes jobs
func (w *Worker) run() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Worker %d recovered from panic: %v", w.ID, r)
		}
		atomic.StoreInt32(&w.running, 0)

		// Notify pool that worker is done
		if w.pool != nil {
			select {
			case w.pool.workerDone <- w.ID:
			default:
				// Pool may not be listening, that's ok
			}
		}
	}()

	for {
		select {
		case <-w.quit:
			log.Printf("Worker %d shutting down", w.ID)
			return
		case <-w.pool.ctx.Done():
			log.Printf("Worker %d shutting down due to pool context cancellation", w.ID)
			return
		default:
			// Try to get a job from the queue with timeout
			job, err := w.jobQueue.DequeueWithTimeout(100 * time.Millisecond)
			if err != nil {
				if err == ErrQueueClosed {
					log.Printf("Worker %d shutting down due to closed queue", w.ID)
					return
				}
				// ErrQueueEmpty is expected, continue loop
				continue
			}

			// Process the job
			result := w.processJob(job)

			// Report the result
			if err := w.jobQueue.EnqueueResult(result); err != nil {
				log.Printf("Worker %d failed to enqueue result for job %s: %v", w.ID, job.ID, err)
			}
		}
	}
}

// processJob processes a single job and returns the result
func (w *Worker) processJob(job Job) JobResult {
	startTime := time.Now()

	result := JobResult{
		JobID:       job.ID,
		ProcessedAt: startTime,
		WorkerID:    w.ID,
		Success:     false,
	}

	// Update metrics
	atomic.AddInt64(&w.metrics.JobsProcessed, 1)
	atomic.StoreInt64(&w.metrics.LastJobTime, startTime.Unix())

	// Process different job types
	switch job.Type {
	case "http_request":
		w.processHTTPJob(job, &result)
	case "delay":
		w.processDelayJob(job, &result)
	default:
		result.Error = fmt.Sprintf("Unknown job type: %s", job.Type)
		log.Printf("Worker %d encountered unknown job type: %s", w.ID, job.Type)
	}

	// Calculate duration
	duration := time.Since(startTime)
	result.Duration = duration.Nanoseconds() / int64(time.Millisecond)

	// Update metrics
	atomic.AddInt64(&w.metrics.TotalDuration, result.Duration)
	if result.Success {
		atomic.AddInt64(&w.metrics.JobsSuccessful, 1)
	} else {
		atomic.AddInt64(&w.metrics.JobsFailed, 1)
	}

	// Update average duration
	totalJobs := atomic.LoadInt64(&w.metrics.JobsProcessed)
	totalDuration := atomic.LoadInt64(&w.metrics.TotalDuration)
	if totalJobs > 0 {
		atomic.StoreInt64(&w.metrics.AverageDuration, totalDuration/totalJobs)
	}

	return result
}

// processHTTPJob processes an HTTP request job
func (w *Worker) processHTTPJob(job Job, result *JobResult) {
	// Extract HTTP request parameters from job payload
	url, ok := job.Payload["url"].(string)
	if !ok {
		result.Error = "Missing or invalid 'url' in job payload"
		return
	}

	method, ok := job.Payload["method"].(string)
	if !ok {
		method = "GET" // Default to GET if not specified
	}

	// Extract headers if present
	headers := make(map[string]string)
	if headersData, exists := job.Payload["headers"]; exists {
		if headersMap, ok := headersData.(map[string]interface{}); ok {
			for k, v := range headersMap {
				if strVal, ok := v.(string); ok {
					headers[k] = strVal
				}
			}
		}
	}

	// Extract body if present
	var body []byte
	if bodyData, exists := job.Payload["body"]; exists {
		if bodyStr, ok := bodyData.(string); ok {
			body = []byte(bodyStr)
		}
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Execute HTTP request based on method
	var resp *client.Response
	var err error

	switch method {
	case "GET":
		resp, err = w.httpClient.Get(ctx, url, headers)
	case "POST":
		resp, err = w.httpClient.Post(ctx, url, body, headers)
	case "PUT":
		resp, err = w.httpClient.Put(ctx, url, body, headers)
	case "DELETE":
		resp, err = w.httpClient.Delete(ctx, url, headers)
	case "HEAD":
		resp, err = w.httpClient.Head(ctx, url, headers)
	case "OPTIONS":
		resp, err = w.httpClient.Options(ctx, url, headers)
	case "PATCH":
		resp, err = w.httpClient.Patch(ctx, url, body, headers)
	default:
		// Use Execute method for custom methods
		req := &client.Request{
			Method:  method,
			URL:     url,
			Body:    body,
			Headers: headers,
		}
		resp, err = w.httpClient.Execute(ctx, req)
	}

	if err != nil {
		result.Error = fmt.Sprintf("HTTP request failed: %v", err)
		return
	}

	// Set success and response data
	result.Success = true
	result.Data = map[string]interface{}{
		"status_code":    resp.StatusCode,
		"response_time":  resp.Duration.Nanoseconds() / int64(time.Millisecond),
		"content_length": len(resp.Body),
		"headers":        resp.Headers,
	}

	// Include response body if it's small enough
	if len(resp.Body) < 1024 { // Only include small responses
		result.Data["body"] = string(resp.Body)
	}
}

// processDelayJob processes a delay job (for testing purposes)
func (w *Worker) processDelayJob(job Job, result *JobResult) {
	// Extract delay duration from job payload
	delayMs, ok := job.Payload["delay_ms"].(float64)
	if !ok {
		result.Error = "Missing or invalid 'delay_ms' in job payload"
		return
	}

	// Sleep for the specified duration
	time.Sleep(time.Duration(delayMs) * time.Millisecond)

	result.Success = true
	result.Data = map[string]interface{}{
		"delay_ms": delayMs,
		"message":  "Delay completed successfully",
	}
}

// addWorker adds a new worker to the pool
func (wp *WorkerPool) addWorker() error {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	if len(wp.workers) >= wp.maxWorkers {
		return errors.New("worker pool at maximum capacity")
	}

	workerID := len(wp.workers) + 1
	worker := NewWorker(workerID, wp.jobQueue, wp)
	wp.workers = append(wp.workers, worker)

	atomic.AddInt32(&wp.poolMetrics.ActiveWorkers, 1)
	log.Printf("Added worker %d to pool (total: %d)", workerID, len(wp.workers))

	return nil
}

// removeWorker removes a worker from the pool
func (wp *WorkerPool) removeWorker(workerID int) error {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	for i, worker := range wp.workers {
		if worker.ID == workerID {
			// Stop the worker
			worker.Stop()

			// Remove from slice
			wp.workers = append(wp.workers[:i], wp.workers[i+1:]...)

			atomic.AddInt32(&wp.poolMetrics.ActiveWorkers, -1)
			log.Printf("Removed worker %d from pool (total: %d)", workerID, len(wp.workers))

			return nil
		}
	}

	return fmt.Errorf("worker %d not found in pool", workerID)
}

// startHealthMonitoring begins the health monitoring routine
func (wp *WorkerPool) startHealthMonitoring() {
	if wp.healthCheckInterval <= 0 {
		return
	}

	wp.healthTicker = time.NewTicker(wp.healthCheckInterval)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Health monitoring goroutine recovered from panic: %v", r)
			}
		}()

		for {
			select {
			case <-wp.healthTicker.C:
				wp.performHealthCheck()
			case <-wp.healthDone:
				wp.healthTicker.Stop()
				log.Printf("Health monitoring stopped")
				return
			case <-wp.ctx.Done():
				wp.healthTicker.Stop()
				return
			}
		}
	}()

	log.Printf("Health monitoring started with interval: %v", wp.healthCheckInterval)
}

// stopHealthMonitoring stops the health monitoring routine
func (wp *WorkerPool) stopHealthMonitoring() {
	if wp.healthTicker != nil {
		select {
		case wp.healthDone <- true:
		default:
		}
	}
}

// performHealthCheck checks the health of all workers and pool state
func (wp *WorkerPool) performHealthCheck() {
	wp.mu.RLock()
	activeWorkers := int32(0)
	healthyWorkers := int32(0)

	for _, worker := range wp.workers {
		if worker.IsRunning() {
			activeWorkers++
			healthyWorkers++
		}
	}

	totalWorkers := int32(len(wp.workers))
	wp.mu.RUnlock()

	// Update metrics
	atomic.StoreInt32(&wp.poolMetrics.ActiveWorkers, activeWorkers)
	atomic.StoreInt32(&wp.poolMetrics.IdleWorkers, totalWorkers-activeWorkers)

	if healthyWorkers == totalWorkers {
		atomic.AddInt64(&wp.poolMetrics.HealthCheckPasses, 1)
	} else {
		atomic.AddInt64(&wp.poolMetrics.HealthCheckFailures, 1)
		log.Printf("Health check failed: %d/%d workers healthy", healthyWorkers, totalWorkers)
	}

	// Auto-scaling logic
	if wp.config.AutoScale && atomic.LoadInt32(&wp.running) == 1 {
		wp.autoScale(activeWorkers, totalWorkers)
	}
}

// autoScale adjusts the number of workers based on current load
func (wp *WorkerPool) autoScale(activeWorkers, totalWorkers int32) {
	if totalWorkers == 0 {
		return
	}

	utilizationRate := float64(activeWorkers) / float64(totalWorkers)

	// Scale up if utilization is high and we haven't reached max workers
	if utilizationRate >= wp.config.ScaleUpThreshold && int(totalWorkers) < wp.maxWorkers {
		if err := wp.addWorker(); err == nil {
			if atomic.LoadInt32(&wp.running) == 1 {
				// Start the new worker
				wp.mu.RLock()
				if len(wp.workers) > 0 {
					newWorker := wp.workers[len(wp.workers)-1]
					newWorker.Start()
					wp.wg.Add(1)
				}
				wp.mu.RUnlock()
			}

			wp.mu.Lock()
			wp.poolMetrics.LastScaleEvent = time.Now()
			atomic.AddInt64(&wp.poolMetrics.ScaleUpEvents, 1)
			wp.mu.Unlock()

			log.Printf("Scaled up: added worker (utilization: %.2f, workers: %d)", utilizationRate, totalWorkers+1)
		}
	}

	// Scale down if utilization is low and we're above minimum workers
	if utilizationRate <= wp.config.ScaleDownThreshold && int(totalWorkers) > wp.minWorkers {
		wp.mu.RLock()
		if len(wp.workers) > 0 {
			workerToRemove := wp.workers[len(wp.workers)-1]
			workerID := workerToRemove.ID
			wp.mu.RUnlock()

			if err := wp.removeWorker(workerID); err == nil {
				wp.mu.Lock()
				wp.poolMetrics.LastScaleEvent = time.Now()
				atomic.AddInt64(&wp.poolMetrics.ScaleDownEvents, 1)
				wp.mu.Unlock()

				log.Printf("Scaled down: removed worker %d (utilization: %.2f, workers: %d)", workerID, utilizationRate, totalWorkers-1)
			}
		} else {
			wp.mu.RUnlock()
		}
	}
}

// manageWorkerLifecycle manages the lifecycle of workers
func (wp *WorkerPool) manageWorkerLifecycle() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Worker lifecycle manager recovered from panic: %v", r)
		}
	}()

	for {
		select {
		case workerID := <-wp.workerDone:
			// Worker has exited, handle cleanup
			log.Printf("Worker %d reported completion", workerID)
			wp.wg.Done()

			// If pool is still running, replace failed worker
			if atomic.LoadInt32(&wp.running) == 1 {
				wp.mu.RLock()
				currentWorkers := len(wp.workers)
				wp.mu.RUnlock()

				if currentWorkers < wp.minWorkers {
					if err := wp.addWorker(); err == nil {
						wp.mu.RLock()
						if len(wp.workers) > 0 {
							newWorker := wp.workers[len(wp.workers)-1]
							newWorker.Start()
							wp.wg.Add(1)
							log.Printf("Replaced failed worker with new worker %d", newWorker.ID)
						}
						wp.mu.RUnlock()

						atomic.AddInt64(&wp.poolMetrics.WorkerFailures, 1)
					}
				}
			}

		case <-wp.ctx.Done():
			log.Printf("Worker lifecycle manager stopping")
			return
		}
	}
}

// GetMetrics returns a copy of the current pool metrics
func (wp *WorkerPool) GetMetrics() PoolMetrics {
	wp.mu.RLock()
	defer wp.mu.RUnlock()

	metrics := wp.poolMetrics
	metrics.PoolUptime = time.Since(metrics.StartTime)

	// Aggregate worker metrics
	var totalJobsProcessed, totalJobsSuccessful, totalJobsFailed, totalDuration int64

	for _, worker := range wp.workers {
		workerMetrics := worker.GetMetrics()
		totalJobsProcessed += workerMetrics.JobsProcessed
		totalJobsSuccessful += workerMetrics.JobsSuccessful
		totalJobsFailed += workerMetrics.JobsFailed
		totalDuration += workerMetrics.TotalDuration
	}

	metrics.TotalJobsProcessed = totalJobsProcessed
	metrics.TotalJobsSuccessful = totalJobsSuccessful
	metrics.TotalJobsFailed = totalJobsFailed

	if totalJobsProcessed > 0 {
		metrics.AverageProcessingTime = totalDuration / totalJobsProcessed
	}

	return metrics
}

// GetWorkerCount returns the current number of workers in the pool
func (wp *WorkerPool) GetWorkerCount() int {
	wp.mu.RLock()
	defer wp.mu.RUnlock()
	return len(wp.workers)
}

// GetConfig returns the pool configuration
func (wp *WorkerPool) GetConfig() PoolConfig {
	return wp.config
}

// UpdateConfig updates the pool configuration (some changes require restart)
func (wp *WorkerPool) UpdateConfig(config PoolConfig) error {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	// Validate configuration
	if config.MinWorkers < 1 {
		return errors.New("min workers must be at least 1")
	}
	if config.MaxWorkers < config.MinWorkers {
		return errors.New("max workers must be greater than or equal to min workers")
	}
	if config.ScaleUpThreshold <= config.ScaleDownThreshold {
		return errors.New("scale up threshold must be greater than scale down threshold")
	}

	wp.config = config
	wp.maxWorkers = config.MaxWorkers
	wp.minWorkers = config.MinWorkers
	wp.healthCheckInterval = config.HealthCheckInterval

	log.Printf("Updated worker pool configuration: min=%d, max=%d", config.MinWorkers, config.MaxWorkers)
	return nil
}
