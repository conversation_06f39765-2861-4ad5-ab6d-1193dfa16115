// Package metrics provides sampling strategies for efficient metrics collection
package metrics

import (
	"context"
	"fmt"
	"math/rand"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// SamplerInterface defines the interface for all sampling strategies
type SamplerInterface interface {
	// ShouldSample returns true if the metric should be sampled
	ShouldSample(metricName string, metricType string, tags map[string]string) bool

	// AddSample adds a sample to the sampler's internal storage
	AddSample(sample *MetricSample) bool

	// GetSamples returns the current samples
	GetSamples() []*MetricSample

	// GetStats returns sampling statistics
	GetStats() SamplingStats

	// Reset clears all samples and resets statistics
	Reset()

	// Configure updates the sampler configuration
	Configure(config SamplerConfig) error

	// GetConfig returns the current configuration
	GetConfig() SamplerConfig
}

// MetricSample represents a sampled metric data point
type MetricSample struct {
	Timestamp  time.Time         `json:"timestamp"`
	MetricName string            `json:"metric_name"`
	MetricType string            `json:"metric_type"` // "counter", "gauge", "histogram"
	Value      interface{}       `json:"value"`
	Tags       map[string]string `json:"tags"`
	SamplerID  string            `json:"sampler_id"`
	Weight     float64           `json:"weight"` // Statistical weight for unbiased estimation
}

// SamplerConfig defines configuration for sampling strategies
type SamplerConfig struct {
	SamplerType     string        `json:"sampler_type"`     // "uniform", "reservoir", "adaptive", "stratified"
	SampleRate      float64       `json:"sample_rate"`      // Base sampling rate (0.0-1.0)
	MaxSamples      int           `json:"max_samples"`      // Maximum number of samples to retain
	MinSamples      int           `json:"min_samples"`      // Minimum number of samples to retain
	AdaptiveEnabled bool          `json:"adaptive_enabled"` // Enable adaptive rate adjustment
	MemoryLimit     int64         `json:"memory_limit"`     // Memory limit for samples in bytes
	RetentionTime   time.Duration `json:"retention_time"`   // How long to retain samples
	RandomSeed      int64         `json:"random_seed"`      // Random seed for reproducible sampling
}

// DefaultSamplerConfig provides sensible defaults for sampling
var DefaultSamplerConfig = SamplerConfig{
	SamplerType:     "uniform",
	SampleRate:      0.1,              // 10% sampling rate
	MaxSamples:      10000,            // Maximum 10K samples
	MinSamples:      100,              // Minimum 100 samples
	AdaptiveEnabled: true,             // Enable adaptive sampling
	MemoryLimit:     10 * 1024 * 1024, // 10MB memory limit
	RetentionTime:   time.Hour,        // 1 hour retention
	RandomSeed:      time.Now().UnixNano(),
}

// SamplingStats tracks statistics about the sampling process
type SamplingStats struct {
	TotalMetrics    uint64  `json:"total_metrics"`    // Total metrics seen
	SampledMetrics  uint64  `json:"sampled_metrics"`  // Total metrics sampled
	DroppedMetrics  uint64  `json:"dropped_metrics"`  // Total metrics dropped
	CurrentSamples  int     `json:"current_samples"`  // Current number of samples
	SamplingRate    float64 `json:"sampling_rate"`    // Current effective sampling rate
	MemoryUsage     int64   `json:"memory_usage"`     // Current memory usage in bytes
	LastSampleTime  int64   `json:"last_sample_time"` // Last sample timestamp (unix nano)
	SamplerType     string  `json:"sampler_type"`     // Type of sampler
	AdaptiveAdjusts uint64  `json:"adaptive_adjusts"` // Number of adaptive rate adjustments
}

// SamplingManager manages multiple sampling strategies
type SamplingManager struct {
	mu             sync.RWMutex
	samplers       map[string]SamplerInterface // Samplers by name
	defaultSampler SamplerInterface            // Default sampler for unspecified metrics
	config         SamplingManagerConfig       // Manager configuration
	stats          SamplingManagerStats        // Manager statistics
	isRunning      int32                       // Atomic flag for running state
	ctx            context.Context             // Context for cancellation
	cancel         context.CancelFunc          // Cancel function
	cleanupTicker  *time.Ticker                // Cleanup ticker
	adaptiveTicker *time.Ticker                // Adaptive adjustment ticker
	startTime      time.Time                   // When sampling started
}

// SamplingManagerConfig defines configuration for the sampling manager
type SamplingManagerConfig struct {
	DefaultSamplerConfig SamplerConfig            `json:"default_sampler_config"`
	CleanupInterval      time.Duration            `json:"cleanup_interval"`    // How often to cleanup old samples
	AdaptiveInterval     time.Duration            `json:"adaptive_interval"`   // How often to adjust adaptive sampling
	MaxSamplers          int                      `json:"max_samplers"`        // Maximum number of samplers
	GlobalMemoryLimit    int64                    `json:"global_memory_limit"` // Global memory limit for all samplers
	SamplerConfigs       map[string]SamplerConfig `json:"sampler_configs"`     // Per-sampler configurations
}

// DefaultSamplingManagerConfig provides sensible defaults
var DefaultSamplingManagerConfig = SamplingManagerConfig{
	DefaultSamplerConfig: DefaultSamplerConfig,
	CleanupInterval:      time.Minute * 5,   // Cleanup every 5 minutes
	AdaptiveInterval:     time.Second * 30,  // Adapt every 30 seconds
	MaxSamplers:          100,               // Maximum 100 samplers
	GlobalMemoryLimit:    100 * 1024 * 1024, // 100MB global limit
	SamplerConfigs:       make(map[string]SamplerConfig),
}

// SamplingManagerStats tracks statistics about the sampling manager
type SamplingManagerStats struct {
	ActiveSamplers   int                      `json:"active_samplers"`
	TotalMemoryUsage int64                    `json:"total_memory_usage"`
	SamplerStats     map[string]SamplingStats `json:"sampler_stats"`
	CleanupRuns      uint64                   `json:"cleanup_runs"`
	AdaptiveRuns     uint64                   `json:"adaptive_runs"`
	LastCleanupTime  int64                    `json:"last_cleanup_time"`
	LastAdaptiveTime int64                    `json:"last_adaptive_time"`
}

// NewSamplingManager creates a new sampling manager with default configuration
func NewSamplingManager() *SamplingManager {
	return NewSamplingManagerWithConfig(DefaultSamplingManagerConfig)
}

// NewSamplingManagerWithConfig creates a new sampling manager with custom configuration
func NewSamplingManagerWithConfig(config SamplingManagerConfig) *SamplingManager {
	ctx, cancel := context.WithCancel(context.Background())

	sm := &SamplingManager{
		samplers:  make(map[string]SamplerInterface),
		config:    config,
		stats:     SamplingManagerStats{SamplerStats: make(map[string]SamplingStats)},
		ctx:       ctx,
		cancel:    cancel,
		startTime: time.Now(),
	}

	// Create default sampler
	defaultSampler, err := NewSampler(config.DefaultSamplerConfig)
	if err == nil {
		sm.defaultSampler = defaultSampler
	}

	return sm
}

// Start begins the sampling manager operations
func (sm *SamplingManager) Start() error {
	if !atomic.CompareAndSwapInt32(&sm.isRunning, 0, 1) {
		return nil // Already running
	}

	sm.mu.Lock()
	defer sm.mu.Unlock()

	// Initialize tickers before starting goroutines
	sm.cleanupTicker = time.NewTicker(sm.config.CleanupInterval)
	sm.adaptiveTicker = time.NewTicker(sm.config.AdaptiveInterval)

	// Start goroutines after tickers are initialized
	go sm.runCleanup()
	go sm.runAdaptiveAdjustment()

	return nil
}

// Stop stops the sampling manager operations
func (sm *SamplingManager) Stop() error {
	if !atomic.CompareAndSwapInt32(&sm.isRunning, 1, 0) {
		return nil // Already stopped
	}

	sm.mu.Lock()
	defer sm.mu.Unlock()

	// Stop tickers
	if sm.cleanupTicker != nil {
		sm.cleanupTicker.Stop()
		sm.cleanupTicker = nil
	}

	if sm.adaptiveTicker != nil {
		sm.adaptiveTicker.Stop()
		sm.adaptiveTicker = nil
	}

	// Cancel context
	sm.cancel()

	return nil
}

// IsRunning returns true if the sampling manager is running
func (sm *SamplingManager) IsRunning() bool {
	return atomic.LoadInt32(&sm.isRunning) == 1
}

// RegisterSampler registers a named sampler
func (sm *SamplingManager) RegisterSampler(name string, sampler SamplerInterface) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if len(sm.samplers) >= sm.config.MaxSamplers {
		return fmt.Errorf("maximum number of samplers (%d) reached", sm.config.MaxSamplers)
	}

	sm.samplers[name] = sampler
	return nil
}

// UnregisterSampler removes a named sampler
func (sm *SamplingManager) UnregisterSampler(name string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	delete(sm.samplers, name)
}

// GetSampler returns a named sampler or the default sampler
func (sm *SamplingManager) GetSampler(name string) SamplerInterface {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if sampler, exists := sm.samplers[name]; exists {
		return sampler
	}

	return sm.defaultSampler
}

// ShouldSample determines if a metric should be sampled using the appropriate sampler
func (sm *SamplingManager) ShouldSample(metricName, metricType string, tags map[string]string, samplerName string) bool {
	sampler := sm.GetSampler(samplerName)
	if sampler == nil {
		return false
	}

	return sampler.ShouldSample(metricName, metricType, tags)
}

// AddSample adds a sample using the appropriate sampler
func (sm *SamplingManager) AddSample(sample *MetricSample, samplerName string) bool {
	sampler := sm.GetSampler(samplerName)
	if sampler == nil {
		return false
	}

	return sampler.AddSample(sample)
}

// GetAllSamples returns samples from all samplers
func (sm *SamplingManager) GetAllSamples() map[string][]*MetricSample {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	result := make(map[string][]*MetricSample)

	// Get samples from default sampler
	if sm.defaultSampler != nil {
		result["default"] = sm.defaultSampler.GetSamples()
	}

	// Get samples from named samplers
	for name, sampler := range sm.samplers {
		result[name] = sampler.GetSamples()
	}

	return result
}

// GetStats returns comprehensive sampling statistics
func (sm *SamplingManager) GetStats() SamplingManagerStats {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	stats := sm.stats
	stats.ActiveSamplers = len(sm.samplers)
	if sm.defaultSampler != nil {
		stats.ActiveSamplers++
	}

	stats.TotalMemoryUsage = 0
	stats.SamplerStats = make(map[string]SamplingStats)

	// Get stats from default sampler
	if sm.defaultSampler != nil {
		defaultStats := sm.defaultSampler.GetStats()
		stats.SamplerStats["default"] = defaultStats
		stats.TotalMemoryUsage += defaultStats.MemoryUsage
	}

	// Get stats from named samplers
	for name, sampler := range sm.samplers {
		samplerStats := sampler.GetStats()
		stats.SamplerStats[name] = samplerStats
		stats.TotalMemoryUsage += samplerStats.MemoryUsage
	}

	return stats
}

// UpdateConfig updates the sampling manager configuration
func (sm *SamplingManager) UpdateConfig(config SamplingManagerConfig) error {
	if sm.IsRunning() {
		return fmt.Errorf("cannot update configuration while sampling manager is running")
	}

	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.config = config

	return nil
}

// GetConfig returns the current configuration
func (sm *SamplingManager) GetConfig() SamplingManagerConfig {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.config
}

// runCleanup runs periodic cleanup of old samples
func (sm *SamplingManager) runCleanup() {
	for {
		select {
		case <-sm.ctx.Done():
			return
		default:
			// Check if ticker is initialized to avoid nil pointer dereference
			if sm.cleanupTicker != nil {
				select {
				case <-sm.ctx.Done():
					return
				case <-sm.cleanupTicker.C:
					sm.performCleanup()
				}
			} else {
				// If ticker not initialized yet, wait a bit and retry
				time.Sleep(time.Millisecond * 10)
			}
		}
	}
}

// performCleanup performs cleanup of old samples
func (sm *SamplingManager) performCleanup() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	now := time.Now()

	// Cleanup default sampler
	if sm.defaultSampler != nil {
		sm.cleanupSampler(sm.defaultSampler, now)
	}

	// Cleanup named samplers
	for _, sampler := range sm.samplers {
		sm.cleanupSampler(sampler, now)
	}

	atomic.AddUint64(&sm.stats.CleanupRuns, 1)
	sm.stats.LastCleanupTime = now.UnixNano()
}

// cleanupSampler performs cleanup for a single sampler
func (sm *SamplingManager) cleanupSampler(sampler SamplerInterface, now time.Time) {
	config := sampler.GetConfig()
	samples := sampler.GetSamples()

	// Remove old samples based on retention time
	cutoff := now.Add(-config.RetentionTime)
	validSamples := make([]*MetricSample, 0, len(samples))

	for _, sample := range samples {
		if sample.Timestamp.After(cutoff) {
			validSamples = append(validSamples, sample)
		}
	}

	// If we removed samples, reset and re-add valid ones
	if len(validSamples) < len(samples) {
		sampler.Reset()
		for _, sample := range validSamples {
			sampler.AddSample(sample)
		}
	}
}

// runAdaptiveAdjustment runs periodic adaptive sampling rate adjustment
func (sm *SamplingManager) runAdaptiveAdjustment() {
	for {
		select {
		case <-sm.ctx.Done():
			return
		default:
			// Check if ticker is initialized to avoid nil pointer dereference
			if sm.adaptiveTicker != nil {
				select {
				case <-sm.ctx.Done():
					return
				case <-sm.adaptiveTicker.C:
					sm.performAdaptiveAdjustment()
				}
			} else {
				// If ticker not initialized yet, wait a bit and retry
				time.Sleep(time.Millisecond * 10)
			}
		}
	}
}

// performAdaptiveAdjustment performs adaptive sampling rate adjustment
func (sm *SamplingManager) performAdaptiveAdjustment() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	// Get current memory stats
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	totalMemoryUsage := sm.getTotalMemoryUsage()
	memoryPressure := float64(totalMemoryUsage) / float64(sm.config.GlobalMemoryLimit)

	// Adjust sampling rates based on memory pressure
	if memoryPressure > 0.8 { // High memory pressure
		sm.adjustSamplingRates(0.5) // Reduce sampling rate
	} else if memoryPressure < 0.3 { // Low memory pressure
		sm.adjustSamplingRates(1.2) // Increase sampling rate
	}

	atomic.AddUint64(&sm.stats.AdaptiveRuns, 1)
	sm.stats.LastAdaptiveTime = time.Now().UnixNano()
}

// getTotalMemoryUsage calculates total memory usage across all samplers
func (sm *SamplingManager) getTotalMemoryUsage() int64 {
	var total int64

	if sm.defaultSampler != nil {
		total += sm.defaultSampler.GetStats().MemoryUsage
	}

	for _, sampler := range sm.samplers {
		total += sampler.GetStats().MemoryUsage
	}

	return total
}

// adjustSamplingRates adjusts sampling rates for all adaptive samplers
func (sm *SamplingManager) adjustSamplingRates(factor float64) {
	// Adjust default sampler
	if sm.defaultSampler != nil {
		sm.adjustSamplerRate(sm.defaultSampler, factor)
	}

	// Adjust named samplers
	for _, sampler := range sm.samplers {
		sm.adjustSamplerRate(sampler, factor)
	}
}

// adjustSamplerRate adjusts the sampling rate for a single sampler
func (sm *SamplingManager) adjustSamplerRate(sampler SamplerInterface, factor float64) {
	config := sampler.GetConfig()
	if !config.AdaptiveEnabled {
		return
	}

	newRate := config.SampleRate * factor

	// Clamp to reasonable bounds
	if newRate > 1.0 {
		newRate = 1.0
	} else if newRate < 0.001 {
		newRate = 0.001 // Minimum 0.1% sampling rate
	}

	config.SampleRate = newRate
	sampler.Configure(config)
}

// NewSampler creates a new sampler based on the configuration
func NewSampler(config SamplerConfig) (SamplerInterface, error) {
	switch config.SamplerType {
	case "uniform":
		return NewUniformSampler(config), nil
	case "reservoir":
		return NewReservoirSampler(config), nil
	case "adaptive":
		return NewAdaptiveSampler(config), nil
	case "stratified":
		return NewStratifiedSampler(config), nil
	default:
		return nil, fmt.Errorf("unknown sampler type: %s", config.SamplerType)
	}
}

// EstimateMemoryUsage estimates memory usage for a metric sample
func EstimateMemoryUsage(sample *MetricSample) int64 {
	size := int64(0)

	// Base struct size
	size += 64 // Approximate base struct overhead

	// String fields
	size += int64(len(sample.MetricName))
	size += int64(len(sample.MetricType))
	size += int64(len(sample.SamplerID))

	// Tags map
	for k, v := range sample.Tags {
		size += int64(len(k) + len(v) + 16) // String overhead
	}

	// Value field (estimate based on type)
	switch sample.Value.(type) {
	case int64, float64:
		size += 8
	case []float64:
		if slice, ok := sample.Value.([]float64); ok {
			size += int64(len(slice) * 8)
		}
	case string:
		if str, ok := sample.Value.(string); ok {
			size += int64(len(str))
		}
	default:
		size += 32 // Conservative estimate
	}

	return size
}

// =============================================================================
// Individual Sampler Implementations
// =============================================================================

// UniformSampler implements uniform random sampling
type UniformSampler struct {
	mu          sync.RWMutex
	config      SamplerConfig
	samples     []*MetricSample
	stats       SamplingStats
	rng         *rand.Rand
	memoryUsage int64
}

// NewUniformSampler creates a new uniform sampler
func NewUniformSampler(config SamplerConfig) *UniformSampler {
	return &UniformSampler{
		config:  config,
		samples: make([]*MetricSample, 0, config.MaxSamples),
		stats: SamplingStats{
			SamplerType: "uniform",
		},
		rng: rand.New(rand.NewSource(config.RandomSeed)),
	}
}

func (us *UniformSampler) ShouldSample(metricName, metricType string, tags map[string]string) bool {
	us.mu.Lock()
	defer us.mu.Unlock()

	atomic.AddUint64(&us.stats.TotalMetrics, 1)

	// Simple uniform random sampling
	shouldSample := us.rng.Float64() < us.config.SampleRate

	if shouldSample {
		atomic.AddUint64(&us.stats.SampledMetrics, 1)
	} else {
		atomic.AddUint64(&us.stats.DroppedMetrics, 1)
	}

	return shouldSample
}

func (us *UniformSampler) AddSample(sample *MetricSample) bool {
	us.mu.Lock()
	defer us.mu.Unlock()

	// Check memory limit
	sampleSize := EstimateMemoryUsage(sample)
	if us.memoryUsage+sampleSize > us.config.MemoryLimit {
		return false
	}

	// Add sample
	sample.SamplerID = "uniform"
	sample.Weight = 1.0 / us.config.SampleRate // Statistical weight for unbiased estimation

	us.samples = append(us.samples, sample)
	us.memoryUsage += sampleSize
	us.stats.LastSampleTime = sample.Timestamp.UnixNano()

	// Trim if over max samples
	if len(us.samples) > us.config.MaxSamples {
		// Remove oldest sample
		oldSample := us.samples[0]
		us.samples = us.samples[1:]
		us.memoryUsage -= EstimateMemoryUsage(oldSample)
	}

	return true
}

func (us *UniformSampler) GetSamples() []*MetricSample {
	us.mu.RLock()
	defer us.mu.RUnlock()

	// Return copy to avoid race conditions
	result := make([]*MetricSample, len(us.samples))
	copy(result, us.samples)
	return result
}

func (us *UniformSampler) GetStats() SamplingStats {
	us.mu.RLock()
	defer us.mu.RUnlock()

	stats := us.stats
	stats.CurrentSamples = len(us.samples)
	stats.SamplingRate = us.config.SampleRate
	stats.MemoryUsage = us.memoryUsage
	return stats
}

func (us *UniformSampler) Reset() {
	us.mu.Lock()
	defer us.mu.Unlock()

	us.samples = us.samples[:0]
	us.memoryUsage = 0
	us.stats = SamplingStats{SamplerType: "uniform"}
}

func (us *UniformSampler) Configure(config SamplerConfig) error {
	us.mu.Lock()
	defer us.mu.Unlock()

	us.config = config
	us.rng = rand.New(rand.NewSource(config.RandomSeed))
	return nil
}

func (us *UniformSampler) GetConfig() SamplerConfig {
	us.mu.RLock()
	defer us.mu.RUnlock()
	return us.config
}

// ReservoirSampler implements reservoir sampling (Algorithm R)
type ReservoirSampler struct {
	mu          sync.RWMutex
	config      SamplerConfig
	samples     []*MetricSample
	stats       SamplingStats
	rng         *rand.Rand
	memoryUsage int64
	totalSeen   uint64
}

// NewReservoirSampler creates a new reservoir sampler
func NewReservoirSampler(config SamplerConfig) *ReservoirSampler {
	return &ReservoirSampler{
		config:  config,
		samples: make([]*MetricSample, 0, config.MaxSamples),
		stats: SamplingStats{
			SamplerType: "reservoir",
		},
		rng: rand.New(rand.NewSource(config.RandomSeed)),
	}
}

func (rs *ReservoirSampler) ShouldSample(metricName, metricType string, tags map[string]string) bool {
	// Reservoir sampling always considers samples, decision is made in AddSample
	atomic.AddUint64(&rs.stats.TotalMetrics, 1)
	return true
}

func (rs *ReservoirSampler) AddSample(sample *MetricSample) bool {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	rs.totalSeen++
	sampleSize := EstimateMemoryUsage(sample)

	sample.SamplerID = "reservoir"
	sample.Weight = 1.0 // Reservoir sampling maintains uniform probability

	// Reservoir sampling algorithm
	if len(rs.samples) < rs.config.MaxSamples {
		// Reservoir not full, add sample
		rs.samples = append(rs.samples, sample)
		rs.memoryUsage += sampleSize
		atomic.AddUint64(&rs.stats.SampledMetrics, 1)
		rs.stats.LastSampleTime = sample.Timestamp.UnixNano()
		return true
	} else {
		// Reservoir full, decide whether to replace
		j := rs.rng.Intn(int(rs.totalSeen))
		if j < rs.config.MaxSamples {
			// Replace sample at position j
			oldSample := rs.samples[j]
			rs.memoryUsage -= EstimateMemoryUsage(oldSample)
			rs.samples[j] = sample
			rs.memoryUsage += sampleSize
			atomic.AddUint64(&rs.stats.SampledMetrics, 1)
			rs.stats.LastSampleTime = sample.Timestamp.UnixNano()
			return true
		} else {
			atomic.AddUint64(&rs.stats.DroppedMetrics, 1)
			return false
		}
	}
}

func (rs *ReservoirSampler) GetSamples() []*MetricSample {
	rs.mu.RLock()
	defer rs.mu.RUnlock()

	result := make([]*MetricSample, len(rs.samples))
	copy(result, rs.samples)
	return result
}

func (rs *ReservoirSampler) GetStats() SamplingStats {
	rs.mu.RLock()
	defer rs.mu.RUnlock()

	stats := rs.stats
	stats.CurrentSamples = len(rs.samples)
	if rs.totalSeen > 0 {
		stats.SamplingRate = float64(len(rs.samples)) / float64(rs.totalSeen)
	}
	stats.MemoryUsage = rs.memoryUsage
	return stats
}

func (rs *ReservoirSampler) Reset() {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	rs.samples = rs.samples[:0]
	rs.memoryUsage = 0
	rs.totalSeen = 0
	rs.stats = SamplingStats{SamplerType: "reservoir"}
}

func (rs *ReservoirSampler) Configure(config SamplerConfig) error {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	rs.config = config
	rs.rng = rand.New(rand.NewSource(config.RandomSeed))
	return nil
}

func (rs *ReservoirSampler) GetConfig() SamplerConfig {
	rs.mu.RLock()
	defer rs.mu.RUnlock()
	return rs.config
}

// AdaptiveSampler implements adaptive sampling with dynamic rate adjustment
type AdaptiveSampler struct {
	mu              sync.RWMutex
	config          SamplerConfig
	samples         []*MetricSample
	stats           SamplingStats
	rng             *rand.Rand
	memoryUsage     int64
	currentRate     float64
	lastAdjustTime  time.Time
	metricFrequency map[string]uint64 // Track frequency per metric
}

// NewAdaptiveSampler creates a new adaptive sampler
func NewAdaptiveSampler(config SamplerConfig) *AdaptiveSampler {
	return &AdaptiveSampler{
		config:          config,
		samples:         make([]*MetricSample, 0, config.MaxSamples),
		stats:           SamplingStats{SamplerType: "adaptive"},
		rng:             rand.New(rand.NewSource(config.RandomSeed)),
		currentRate:     config.SampleRate,
		lastAdjustTime:  time.Now(),
		metricFrequency: make(map[string]uint64),
	}
}

func (as *AdaptiveSampler) ShouldSample(metricName, metricType string, tags map[string]string) bool {
	as.mu.Lock()
	defer as.mu.Unlock()

	atomic.AddUint64(&as.stats.TotalMetrics, 1)

	// Track metric frequency
	key := metricName + ":" + metricType
	as.metricFrequency[key]++

	// Adjust sampling rate based on frequency (high frequency = lower rate)
	frequency := as.metricFrequency[key]
	adaptiveRate := as.currentRate

	if frequency > 1000 { // High frequency metric
		adaptiveRate *= 0.1 // Reduce sampling rate
	} else if frequency < 10 { // Low frequency metric
		adaptiveRate *= 2.0 // Increase sampling rate
		if adaptiveRate > 1.0 {
			adaptiveRate = 1.0
		}
	}

	shouldSample := as.rng.Float64() < adaptiveRate

	if shouldSample {
		atomic.AddUint64(&as.stats.SampledMetrics, 1)
	} else {
		atomic.AddUint64(&as.stats.DroppedMetrics, 1)
	}

	// Periodic rate adjustment
	now := time.Now()
	if now.Sub(as.lastAdjustTime) > time.Minute {
		as.adjustSamplingRate()
		as.lastAdjustTime = now
	}

	return shouldSample
}

func (as *AdaptiveSampler) adjustSamplingRate() {
	// Adjust rate based on memory usage
	memoryPressure := float64(as.memoryUsage) / float64(as.config.MemoryLimit)

	if memoryPressure > 0.8 {
		as.currentRate *= 0.8 // Reduce rate
	} else if memoryPressure < 0.3 {
		as.currentRate *= 1.2 // Increase rate
		if as.currentRate > 1.0 {
			as.currentRate = 1.0
		}
	}

	atomic.AddUint64(&as.stats.AdaptiveAdjusts, 1)
}

func (as *AdaptiveSampler) AddSample(sample *MetricSample) bool {
	as.mu.Lock()
	defer as.mu.Unlock()

	sampleSize := EstimateMemoryUsage(sample)
	if as.memoryUsage+sampleSize > as.config.MemoryLimit {
		return false
	}

	sample.SamplerID = "adaptive"
	sample.Weight = 1.0 / as.currentRate

	as.samples = append(as.samples, sample)
	as.memoryUsage += sampleSize
	as.stats.LastSampleTime = sample.Timestamp.UnixNano()

	// Trim if over max samples
	if len(as.samples) > as.config.MaxSamples {
		oldSample := as.samples[0]
		as.samples = as.samples[1:]
		as.memoryUsage -= EstimateMemoryUsage(oldSample)
	}

	return true
}

func (as *AdaptiveSampler) GetSamples() []*MetricSample {
	as.mu.RLock()
	defer as.mu.RUnlock()

	result := make([]*MetricSample, len(as.samples))
	copy(result, as.samples)
	return result
}

func (as *AdaptiveSampler) GetStats() SamplingStats {
	as.mu.RLock()
	defer as.mu.RUnlock()

	stats := as.stats
	stats.CurrentSamples = len(as.samples)
	stats.SamplingRate = as.currentRate
	stats.MemoryUsage = as.memoryUsage
	return stats
}

func (as *AdaptiveSampler) Reset() {
	as.mu.Lock()
	defer as.mu.Unlock()

	as.samples = as.samples[:0]
	as.memoryUsage = 0
	as.metricFrequency = make(map[string]uint64)
	as.stats = SamplingStats{SamplerType: "adaptive"}
}

func (as *AdaptiveSampler) Configure(config SamplerConfig) error {
	as.mu.Lock()
	defer as.mu.Unlock()

	as.config = config
	as.currentRate = config.SampleRate
	as.rng = rand.New(rand.NewSource(config.RandomSeed))
	return nil
}

func (as *AdaptiveSampler) GetConfig() SamplerConfig {
	as.mu.RLock()
	defer as.mu.RUnlock()
	return as.config
}

// StratifiedSampler implements stratified sampling by metric categories
type StratifiedSampler struct {
	mu          sync.RWMutex
	config      SamplerConfig
	samples     []*MetricSample
	stats       SamplingStats
	rng         *rand.Rand
	memoryUsage int64
	strata      map[string]*StratumSampler // Samplers per stratum (metric type)
}

// StratumSampler represents a sampler for a specific stratum
type StratumSampler struct {
	samples     []*MetricSample
	maxSamples  int
	sampleRate  float64
	totalSeen   uint64
	memoryUsage int64
}

// NewStratifiedSampler creates a new stratified sampler
func NewStratifiedSampler(config SamplerConfig) *StratifiedSampler {
	return &StratifiedSampler{
		config:  config,
		samples: make([]*MetricSample, 0, config.MaxSamples),
		stats:   SamplingStats{SamplerType: "stratified"},
		rng:     rand.New(rand.NewSource(config.RandomSeed)),
		strata:  make(map[string]*StratumSampler),
	}
}

func (ss *StratifiedSampler) ShouldSample(metricName, metricType string, tags map[string]string) bool {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	atomic.AddUint64(&ss.stats.TotalMetrics, 1)

	// Get or create stratum for this metric type
	stratum, exists := ss.strata[metricType]
	if !exists {
		// Allocate samples proportionally across strata
		maxSamplesPerStratum := ss.config.MaxSamples / 3 // Assume 3 main types: counter, gauge, histogram
		if maxSamplesPerStratum < 10 {
			maxSamplesPerStratum = 10
		}

		stratum = &StratumSampler{
			samples:    make([]*MetricSample, 0, maxSamplesPerStratum),
			maxSamples: maxSamplesPerStratum,
			sampleRate: ss.config.SampleRate,
		}
		ss.strata[metricType] = stratum
	}

	stratum.totalSeen++
	shouldSample := ss.rng.Float64() < stratum.sampleRate

	if shouldSample {
		atomic.AddUint64(&ss.stats.SampledMetrics, 1)
	} else {
		atomic.AddUint64(&ss.stats.DroppedMetrics, 1)
	}

	return shouldSample
}

func (ss *StratifiedSampler) AddSample(sample *MetricSample) bool {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	sampleSize := EstimateMemoryUsage(sample)
	if ss.memoryUsage+sampleSize > ss.config.MemoryLimit {
		return false
	}

	sample.SamplerID = "stratified"
	sample.Weight = 1.0 / ss.config.SampleRate // Statistical weight for unbiased estimation

	// Get or create stratum for this metric type
	stratum, exists := ss.strata[sample.MetricType]
	if !exists {
		// Create stratum if it doesn't exist
		maxSamplesPerStratum := ss.config.MaxSamples / 3 // Assume 3 main types: counter, gauge, histogram
		if maxSamplesPerStratum < 10 {
			maxSamplesPerStratum = 10
		}

		stratum = &StratumSampler{
			samples:    make([]*MetricSample, 0, maxSamplesPerStratum),
			maxSamples: maxSamplesPerStratum,
			sampleRate: ss.config.SampleRate,
		}
		ss.strata[sample.MetricType] = stratum
	}

	// Add to stratum
	if len(stratum.samples) < stratum.maxSamples {
		stratum.samples = append(stratum.samples, sample)
		stratum.memoryUsage += sampleSize
	} else {
		// Replace random sample in stratum
		idx := ss.rng.Intn(len(stratum.samples))
		oldSample := stratum.samples[idx]
		stratum.memoryUsage -= EstimateMemoryUsage(oldSample)
		stratum.samples[idx] = sample
		stratum.memoryUsage += sampleSize
	}

	// Update global samples list
	ss.rebuildGlobalSamples()
	ss.stats.LastSampleTime = sample.Timestamp.UnixNano()

	return true
}

func (ss *StratifiedSampler) rebuildGlobalSamples() {
	ss.samples = ss.samples[:0]
	totalMemory := int64(0)

	for _, stratum := range ss.strata {
		ss.samples = append(ss.samples, stratum.samples...)
		totalMemory += stratum.memoryUsage
	}

	ss.memoryUsage = totalMemory
}

func (ss *StratifiedSampler) GetSamples() []*MetricSample {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	result := make([]*MetricSample, len(ss.samples))
	copy(result, ss.samples)
	return result
}

func (ss *StratifiedSampler) GetStats() SamplingStats {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	stats := ss.stats
	stats.CurrentSamples = len(ss.samples)
	stats.SamplingRate = ss.config.SampleRate
	stats.MemoryUsage = ss.memoryUsage
	return stats
}

func (ss *StratifiedSampler) Reset() {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	ss.samples = ss.samples[:0]
	ss.memoryUsage = 0
	ss.strata = make(map[string]*StratumSampler)
	ss.stats = SamplingStats{SamplerType: "stratified"}
}

func (ss *StratifiedSampler) Configure(config SamplerConfig) error {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	ss.config = config
	ss.rng = rand.New(rand.NewSource(config.RandomSeed))

	// Update all strata sample rates
	for _, stratum := range ss.strata {
		stratum.sampleRate = config.SampleRate
	}

	return nil
}

func (ss *StratifiedSampler) GetConfig() SamplerConfig {
	ss.mu.RLock()
	defer ss.mu.RUnlock()
	return ss.config
}
