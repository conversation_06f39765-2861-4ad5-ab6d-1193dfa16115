// Package metrics provides time-based bucketing functionality for aggregating metrics
package metrics

import (
	"context"
	"errors"
	"fmt"
	"math"
	"sort"
	"sync"
	"sync/atomic"
	"time"
)

// Common errors for time bucketing
var (
	ErrInvalidBucketSize    = errors.New("time buckets: bucket size must be greater than 0")
	ErrInvalidRetentionSize = errors.New("time buckets: retention size must be greater than 0")
	ErrBucketNotFound       = errors.New("time buckets: bucket not found")
	ErrClockSkewTooLarge    = errors.New("time buckets: clock skew exceeds maximum allowed")
	ErrBucketManagerStopped = errors.New("time buckets: bucket manager is stopped")
)

// TimeBucketGranularity defines the granularity of time buckets
type TimeBucketGranularity int

const (
	GranularitySecond TimeBucketGranularity = iota
	GranularityMinute
	GranularityHour
	GranularityDay
	GranularityWeek
)

// String returns the string representation of granularity
func (g TimeBucketGranularity) String() string {
	switch g {
	case GranularitySecond:
		return "second"
	case GranularityMinute:
		return "minute"
	case GranularityHour:
		return "hour"
	case GranularityDay:
		return "day"
	case GranularityWeek:
		return "week"
	default:
		return "unknown"
	}
}

// Duration returns the time.Duration for the granularity
func (g TimeBucketGranularity) Duration() time.Duration {
	switch g {
	case GranularitySecond:
		return time.Second
	case GranularityMinute:
		return time.Minute
	case GranularityHour:
		return time.Hour
	case GranularityDay:
		return time.Hour * 24
	case GranularityWeek:
		return time.Hour * 24 * 7
	default:
		return time.Second
	}
}

// TimeBucketConfig defines configuration for time-based bucketing
type TimeBucketConfig struct {
	Granularity        TimeBucketGranularity // Size of each bucket
	RetentionBuckets   int                   // How many buckets to retain
	MaxClockSkew       time.Duration         // Maximum allowed clock skew for late data
	EnableDownsampling bool                  // Enable downsampling from smaller to larger buckets
	EnableUpsampling   bool                  // Enable upsampling from larger to smaller buckets
	CleanupInterval    time.Duration         // How often to cleanup old buckets
	EnableAsync        bool                  // Run cleanup asynchronously
	EnableCompression  bool                  // Compress old bucket data
	MaxMemoryUsage     int64                 // Maximum memory usage for all buckets
	FlushOnShutdown    bool                  // Flush data when shutting down
}

// DefaultTimeBucketConfig provides sensible defaults for load testing scenarios
func DefaultTimeBucketConfig(granularity TimeBucketGranularity) TimeBucketConfig {
	return TimeBucketConfig{
		Granularity:        granularity,
		RetentionBuckets:   100,                    // Keep 100 buckets by default
		MaxClockSkew:       time.Minute * 5,        // Allow 5 minutes of clock skew
		EnableDownsampling: true,                   // Enable downsampling by default
		EnableUpsampling:   false,                  // Disable upsampling by default (can be expensive)
		CleanupInterval:    granularity.Duration(), // Cleanup every bucket duration
		EnableAsync:        true,                   // Async cleanup for performance
		EnableCompression:  false,                  // Disabled by default (can use storage compression)
		MaxMemoryUsage:     1024 * 1024 * 100,      // 100MB default limit
		FlushOnShutdown:    true,                   // Flush on shutdown
	}
}

// TimeBucketEntry represents a single metric entry in a time bucket
type TimeBucketEntry struct {
	Timestamp time.Time         `json:"timestamp"`
	Value     float64           `json:"value"`
	Tags      map[string]string `json:"tags"`
	MetricID  string            `json:"metric_id"`
	Source    string            `json:"source"` // Which component generated this metric
}

// TimeBucket represents a single time bucket containing aggregated metrics
type TimeBucket struct {
	StartTime    time.Time                     `json:"start_time"`
	EndTime      time.Time                     `json:"end_time"`
	Granularity  TimeBucketGranularity         `json:"granularity"`
	Entries      []TimeBucketEntry             `json:"entries"`
	Aggregations map[string]*BucketAggregation `json:"aggregations"` // Per-metric aggregations
	mu           sync.RWMutex                  `json:"-"`
	memoryUsage  int64                         `json:"-"`
}

// BucketAggregation contains aggregated statistics for a metric within a bucket
type BucketAggregation struct {
	Count             uint64    `json:"count"`
	Sum               float64   `json:"sum"`
	Min               float64   `json:"min"`
	Max               float64   `json:"max"`
	Mean              float64   `json:"mean"`
	Variance          float64   `json:"variance"`
	StandardDeviation float64   `json:"standard_deviation"`
	P50               float64   `json:"p50"`
	P95               float64   `json:"p95"`
	P99               float64   `json:"p99"`
	FirstSeen         time.Time `json:"first_seen"`
	LastSeen          time.Time `json:"last_seen"`
}

// NewTimeBucket creates a new time bucket for the specified time range
func NewTimeBucket(startTime time.Time, granularity TimeBucketGranularity) *TimeBucket {
	endTime := startTime.Add(granularity.Duration())

	return &TimeBucket{
		StartTime:    startTime,
		EndTime:      endTime,
		Granularity:  granularity,
		Entries:      make([]TimeBucketEntry, 0),
		Aggregations: make(map[string]*BucketAggregation),
		memoryUsage:  0,
	}
}

// AddEntry adds a metric entry to the bucket if it falls within the time range
func (tb *TimeBucket) AddEntry(entry TimeBucketEntry) error {
	// Check if entry falls within bucket time range
	if entry.Timestamp.Before(tb.StartTime) || entry.Timestamp.After(tb.EndTime) {
		return fmt.Errorf("entry timestamp %v outside bucket range [%v, %v]",
			entry.Timestamp, tb.StartTime, tb.EndTime)
	}

	tb.mu.Lock()
	defer tb.mu.Unlock()

	// Add entry
	tb.Entries = append(tb.Entries, entry)

	// Update aggregations
	tb.updateAggregation(entry)

	// Update memory usage estimate
	tb.memoryUsage += tb.estimateEntrySize(entry)

	return nil
}

// updateAggregation updates the running aggregation for a metric
func (tb *TimeBucket) updateAggregation(entry TimeBucketEntry) {
	agg, exists := tb.Aggregations[entry.MetricID]
	if !exists {
		agg = &BucketAggregation{
			Count:     0,
			Sum:       0,
			Min:       entry.Value,
			Max:       entry.Value,
			FirstSeen: entry.Timestamp,
			LastSeen:  entry.Timestamp,
		}
		tb.Aggregations[entry.MetricID] = agg
	}

	// Update basic stats
	agg.Count++
	agg.Sum += entry.Value
	agg.LastSeen = entry.Timestamp

	if entry.Value < agg.Min {
		agg.Min = entry.Value
	}
	if entry.Value > agg.Max {
		agg.Max = entry.Value
	}

	// Update mean
	agg.Mean = agg.Sum / float64(agg.Count)

	// Calculate variance incrementally (Welford's online algorithm)
	if agg.Count == 1 {
		agg.Variance = 0
	} else {
		// Simplified variance calculation - could be optimized with Welford's algorithm
		var sumSquaredDiffs float64
		for _, e := range tb.Entries {
			if e.MetricID == entry.MetricID {
				diff := e.Value - agg.Mean
				sumSquaredDiffs += diff * diff
			}
		}
		agg.Variance = sumSquaredDiffs / float64(agg.Count)
	}

	agg.StandardDeviation = math.Sqrt(agg.Variance)

	// Calculate percentiles (simplified - could use full percentile calculator for accuracy)
	tb.calculatePercentiles(entry.MetricID, agg)
}

// calculatePercentiles calculates percentiles for a metric in the bucket
func (tb *TimeBucket) calculatePercentiles(metricID string, agg *BucketAggregation) {
	var values []float64
	for _, e := range tb.Entries {
		if e.MetricID == metricID {
			values = append(values, e.Value)
		}
	}

	if len(values) == 0 {
		return
	}

	// Sort values for percentile calculation
	sort.Float64s(values)

	// Calculate percentiles using linear interpolation
	agg.P50 = percentileValue(values, 0.50)
	agg.P95 = percentileValue(values, 0.95)
	agg.P99 = percentileValue(values, 0.99)
}

// percentileValue calculates a percentile value using linear interpolation
func percentileValue(sortedValues []float64, p float64) float64 {
	if len(sortedValues) == 0 {
		return 0
	}
	if len(sortedValues) == 1 {
		return sortedValues[0]
	}

	index := p * float64(len(sortedValues)-1)
	lower := int(index)
	upper := lower + 1

	if upper >= len(sortedValues) {
		return sortedValues[len(sortedValues)-1]
	}

	// Linear interpolation
	weight := index - float64(lower)
	return sortedValues[lower]*(1-weight) + sortedValues[upper]*weight
}

// estimateEntrySize estimates the memory usage of an entry
func (tb *TimeBucket) estimateEntrySize(entry TimeBucketEntry) int64 {
	size := int64(64) // Base size for timestamp, value, and pointers
	size += int64(len(entry.MetricID))
	size += int64(len(entry.Source))
	for k, v := range entry.Tags {
		size += int64(len(k) + len(v))
	}
	return size
}

// GetEntries returns all entries in the bucket
func (tb *TimeBucket) GetEntries() []TimeBucketEntry {
	tb.mu.RLock()
	defer tb.mu.RUnlock()

	result := make([]TimeBucketEntry, len(tb.Entries))
	copy(result, tb.Entries)
	return result
}

// GetEntriesForMetric returns entries for a specific metric
func (tb *TimeBucket) GetEntriesForMetric(metricID string) []TimeBucketEntry {
	tb.mu.RLock()
	defer tb.mu.RUnlock()

	var result []TimeBucketEntry
	for _, entry := range tb.Entries {
		if entry.MetricID == metricID {
			result = append(result, entry)
		}
	}
	return result
}

// GetAggregation returns the aggregation for a specific metric
func (tb *TimeBucket) GetAggregation(metricID string) (*BucketAggregation, bool) {
	tb.mu.RLock()
	defer tb.mu.RUnlock()

	agg, exists := tb.Aggregations[metricID]
	if !exists {
		return nil, false
	}

	// Return a copy to avoid race conditions
	result := *agg
	return &result, true
}

// GetAllAggregations returns all aggregations in the bucket
func (tb *TimeBucket) GetAllAggregations() map[string]*BucketAggregation {
	tb.mu.RLock()
	defer tb.mu.RUnlock()

	result := make(map[string]*BucketAggregation)
	for metricID, agg := range tb.Aggregations {
		aggCopy := *agg
		result[metricID] = &aggCopy
	}
	return result
}

// MemoryUsage returns the estimated memory usage of the bucket
func (tb *TimeBucket) MemoryUsage() int64 {
	tb.mu.RLock()
	defer tb.mu.RUnlock()
	return tb.memoryUsage
}

// EntryCount returns the number of entries in the bucket
func (tb *TimeBucket) EntryCount() int {
	tb.mu.RLock()
	defer tb.mu.RUnlock()
	return len(tb.Entries)
}

// IsEmpty returns true if the bucket contains no entries
func (tb *TimeBucket) IsEmpty() bool {
	return tb.EntryCount() == 0
}

// Clear removes all entries from the bucket
func (tb *TimeBucket) Clear() {
	tb.mu.Lock()
	defer tb.mu.Unlock()

	tb.Entries = tb.Entries[:0]
	tb.Aggregations = make(map[string]*BucketAggregation)
	tb.memoryUsage = 0
}

// TimeBucketManager manages a collection of time buckets with automatic cleanup and aggregation
type TimeBucketManager struct {
	config        TimeBucketConfig
	buckets       map[int64]*TimeBucket // Key: bucket start timestamp (Unix seconds)
	mu            sync.RWMutex
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	isRunning     int32
	stats         TimeBucketStats
	cleanupTicker *time.Ticker
}

// TimeBucketStats contains statistics about the bucket manager
type TimeBucketStats struct {
	TotalBuckets    int64 `json:"total_buckets"`
	TotalEntries    int64 `json:"total_entries"`
	MemoryUsage     int64 `json:"memory_usage"`
	OldestBucket    int64 `json:"oldest_bucket"`     // Unix timestamp
	NewestBucket    int64 `json:"newest_bucket"`     // Unix timestamp
	LateDataDropped int64 `json:"late_data_dropped"` // Entries dropped due to clock skew
	CleanupsRun     int64 `json:"cleanups_run"`
	LastCleanupTime int64 `json:"last_cleanup_time"` // Unix timestamp
}

// NewTimeBucketManager creates a new time bucket manager
func NewTimeBucketManager(config TimeBucketConfig) (*TimeBucketManager, error) {
	if config.Granularity.Duration() <= 0 {
		return nil, ErrInvalidBucketSize
	}
	if config.RetentionBuckets <= 0 {
		return nil, ErrInvalidRetentionSize
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &TimeBucketManager{
		config:  config,
		buckets: make(map[int64]*TimeBucket),
		ctx:     ctx,
		cancel:  cancel,
	}, nil
}

// Start begins the bucket manager operations
func (tbm *TimeBucketManager) Start() error {
	if !atomic.CompareAndSwapInt32(&tbm.isRunning, 0, 1) {
		return errors.New("bucket manager already running")
	}

	if tbm.config.EnableAsync {
		tbm.cleanupTicker = time.NewTicker(tbm.config.CleanupInterval)
		tbm.wg.Add(1)
		go tbm.runCleanup()
	}

	return nil
}

// Stop gracefully stops the bucket manager
func (tbm *TimeBucketManager) Stop() error {
	if !atomic.CompareAndSwapInt32(&tbm.isRunning, 1, 0) {
		return errors.New("bucket manager not running")
	}

	// Cancel context and wait for goroutines
	tbm.cancel()

	if tbm.cleanupTicker != nil {
		tbm.cleanupTicker.Stop()
	}

	tbm.wg.Wait()

	// Flush data if enabled
	if tbm.config.FlushOnShutdown {
		tbm.flushAllBuckets()
	}

	return nil
}

// AddEntry adds a metric entry to the appropriate time bucket
func (tbm *TimeBucketManager) AddEntry(entry TimeBucketEntry) error {
	if atomic.LoadInt32(&tbm.isRunning) == 0 {
		return ErrBucketManagerStopped
	}

	// Check clock skew
	now := time.Now()
	if entry.Timestamp.After(now.Add(tbm.config.MaxClockSkew)) ||
		entry.Timestamp.Before(now.Add(-tbm.config.MaxClockSkew)) {
		atomic.AddInt64(&tbm.stats.LateDataDropped, 1)
		return ErrClockSkewTooLarge
	}

	// Calculate bucket key (start time as Unix timestamp)
	bucketKey := tbm.calculateBucketKey(entry.Timestamp)

	tbm.mu.Lock()
	bucket, exists := tbm.buckets[bucketKey]
	if !exists {
		// Create new bucket
		bucketStartTime := time.Unix(bucketKey, 0).UTC()
		bucket = NewTimeBucket(bucketStartTime, tbm.config.Granularity)
		tbm.buckets[bucketKey] = bucket
		atomic.AddInt64(&tbm.stats.TotalBuckets, 1)
	}
	tbm.mu.Unlock()

	// Add entry to bucket
	if err := bucket.AddEntry(entry); err != nil {
		return err
	}

	// Update stats
	atomic.AddInt64(&tbm.stats.TotalEntries, 1)
	atomic.StoreInt64(&tbm.stats.MemoryUsage, tbm.calculateTotalMemoryUsage())

	// Update bucket range stats
	tbm.updateBucketRangeStats(bucketKey)

	return nil
}

// calculateBucketKey calculates the bucket key (start timestamp) for a given time
func (tbm *TimeBucketManager) calculateBucketKey(timestamp time.Time) int64 {
	// Align to bucket boundaries
	switch tbm.config.Granularity {
	case GranularitySecond:
		return timestamp.Truncate(time.Second).Unix()
	case GranularityMinute:
		return timestamp.Truncate(time.Minute).Unix()
	case GranularityHour:
		return timestamp.Truncate(time.Hour).Unix()
	case GranularityDay:
		// Truncate to start of day in UTC
		year, month, day := timestamp.UTC().Date()
		return time.Date(year, month, day, 0, 0, 0, 0, time.UTC).Unix()
	case GranularityWeek:
		// Truncate to start of week (Monday in UTC)
		year, month, day := timestamp.UTC().Date()
		startOfDay := time.Date(year, month, day, 0, 0, 0, 0, time.UTC)
		// Go to start of week (Monday)
		daysFromMonday := int(startOfDay.Weekday()+6) % 7
		startOfWeek := startOfDay.AddDate(0, 0, -daysFromMonday)
		return startOfWeek.Unix()
	default:
		return timestamp.Truncate(tbm.config.Granularity.Duration()).Unix()
	}
}

// GetBucket retrieves a bucket by timestamp
func (tbm *TimeBucketManager) GetBucket(timestamp time.Time) (*TimeBucket, bool) {
	bucketKey := tbm.calculateBucketKey(timestamp)

	tbm.mu.RLock()
	bucket, exists := tbm.buckets[bucketKey]
	tbm.mu.RUnlock()

	return bucket, exists
}

// GetBucketRange retrieves buckets within a time range
func (tbm *TimeBucketManager) GetBucketRange(startTime, endTime time.Time) []*TimeBucket {
	tbm.mu.RLock()
	defer tbm.mu.RUnlock()

	var result []*TimeBucket
	for _, bucket := range tbm.buckets {
		if (bucket.StartTime.Equal(startTime) || bucket.StartTime.After(startTime)) &&
			(bucket.EndTime.Equal(endTime) || bucket.EndTime.Before(endTime)) {
			result = append(result, bucket)
		}
	}

	// Sort by start time
	sort.Slice(result, func(i, j int) bool {
		return result[i].StartTime.Before(result[j].StartTime)
	})

	return result
}

// GetLatestBuckets retrieves the N most recent buckets
func (tbm *TimeBucketManager) GetLatestBuckets(count int) []*TimeBucket {
	tbm.mu.RLock()
	defer tbm.mu.RUnlock()

	// Get all buckets and sort by start time (newest first)
	var buckets []*TimeBucket
	for _, bucket := range tbm.buckets {
		buckets = append(buckets, bucket)
	}

	sort.Slice(buckets, func(i, j int) bool {
		return buckets[i].StartTime.After(buckets[j].StartTime)
	})

	// Return up to 'count' buckets
	if count > len(buckets) {
		count = len(buckets)
	}
	return buckets[:count]
}

// GetStats returns bucket manager statistics
func (tbm *TimeBucketManager) GetStats() TimeBucketStats {
	return tbm.stats
}

// updateBucketRangeStats updates the oldest/newest bucket statistics
func (tbm *TimeBucketManager) updateBucketRangeStats(bucketKey int64) {
	oldestBucket := atomic.LoadInt64(&tbm.stats.OldestBucket)
	if oldestBucket == 0 || bucketKey < oldestBucket {
		atomic.StoreInt64(&tbm.stats.OldestBucket, bucketKey)
	}

	newestBucket := atomic.LoadInt64(&tbm.stats.NewestBucket)
	if bucketKey > newestBucket {
		atomic.StoreInt64(&tbm.stats.NewestBucket, bucketKey)
	}
}

// calculateTotalMemoryUsage calculates total memory usage across all buckets
func (tbm *TimeBucketManager) calculateTotalMemoryUsage() int64 {
	tbm.mu.RLock()
	defer tbm.mu.RUnlock()

	var total int64
	for _, bucket := range tbm.buckets {
		total += bucket.MemoryUsage()
	}
	return total
}

// runCleanup runs periodic cleanup operations
func (tbm *TimeBucketManager) runCleanup() {
	defer tbm.wg.Done()

	for {
		select {
		case <-tbm.ctx.Done():
			return
		case <-tbm.cleanupTicker.C:
			tbm.cleanup()
		}
	}
}

// cleanup removes old buckets that exceed retention limits
func (tbm *TimeBucketManager) cleanup() {
	tbm.mu.Lock()
	defer tbm.mu.Unlock()

	// Check memory limit
	if tbm.stats.MemoryUsage > tbm.config.MaxMemoryUsage {
		tbm.cleanupByMemory()
	}

	// Check retention limit
	if len(tbm.buckets) > tbm.config.RetentionBuckets {
		tbm.cleanupByRetention()
	}

	atomic.AddInt64(&tbm.stats.CleanupsRun, 1)
	atomic.StoreInt64(&tbm.stats.LastCleanupTime, time.Now().Unix())
}

// cleanupByMemory removes oldest buckets to reduce memory usage
func (tbm *TimeBucketManager) cleanupByMemory() {
	// Get all bucket keys sorted by timestamp (oldest first)
	var keys []int64
	for key := range tbm.buckets {
		keys = append(keys, key)
	}
	sort.Slice(keys, func(i, j int) bool { return keys[i] < keys[j] })

	// Remove oldest buckets until memory is under limit
	for _, key := range keys {
		if tbm.stats.MemoryUsage <= tbm.config.MaxMemoryUsage {
			break
		}

		bucket := tbm.buckets[key]
		tbm.stats.MemoryUsage -= bucket.MemoryUsage()
		delete(tbm.buckets, key)
		atomic.AddInt64(&tbm.stats.TotalBuckets, -1)
	}
}

// cleanupByRetention removes oldest buckets beyond retention limit
func (tbm *TimeBucketManager) cleanupByRetention() {
	// Get all bucket keys sorted by timestamp (oldest first)
	var keys []int64
	for key := range tbm.buckets {
		keys = append(keys, key)
	}
	sort.Slice(keys, func(i, j int) bool { return keys[i] < keys[j] })

	// Remove oldest buckets beyond retention limit
	bucketsToRemove := len(tbm.buckets) - tbm.config.RetentionBuckets
	for i := 0; i < bucketsToRemove && i < len(keys); i++ {
		key := keys[i]
		bucket := tbm.buckets[key]
		tbm.stats.MemoryUsage -= bucket.MemoryUsage()
		delete(tbm.buckets, key)
		atomic.AddInt64(&tbm.stats.TotalBuckets, -1)
	}
}

// flushAllBuckets flushes all bucket data (placeholder for persistence integration)
func (tbm *TimeBucketManager) flushAllBuckets() {
	tbm.mu.RLock()
	defer tbm.mu.RUnlock()

	// TODO: Integrate with persistence system when available
	// For now, this is a placeholder for future persistence integration
	for _, bucket := range tbm.buckets {
		_ = bucket // Placeholder: would persist bucket data
	}
}

// Downsampling and Upsampling functionality

// DownsampleBucket creates a downsampled bucket from multiple smaller granularity buckets
func (tbm *TimeBucketManager) DownsampleBucket(buckets []*TimeBucket, targetGranularity TimeBucketGranularity) (*TimeBucket, error) {
	if len(buckets) == 0 {
		return nil, errors.New("no buckets to downsample")
	}

	// Find the time range for the downsampled bucket
	startTime := buckets[0].StartTime
	for _, bucket := range buckets {
		if bucket.StartTime.Before(startTime) {
			startTime = bucket.StartTime
		}
	}

	// Create new bucket with target granularity
	downsampled := NewTimeBucket(startTime, targetGranularity)

	// Merge entries from all source buckets
	for _, bucket := range buckets {
		entries := bucket.GetEntries()
		for _, entry := range entries {
			if err := downsampled.AddEntry(entry); err != nil {
				// Log error but continue processing other entries
				continue
			}
		}
	}

	return downsampled, nil
}

// UpsampleBucket creates multiple upsampled buckets from a larger granularity bucket
func (tbm *TimeBucketManager) UpsampleBucket(bucket *TimeBucket, targetGranularity TimeBucketGranularity) ([]*TimeBucket, error) {
	if targetGranularity.Duration() >= bucket.Granularity.Duration() {
		return nil, errors.New("target granularity must be smaller than source granularity")
	}

	var result []*TimeBucket
	targetDuration := targetGranularity.Duration()

	// Create buckets for the upsampled granularity
	currentTime := bucket.StartTime
	for currentTime.Before(bucket.EndTime) {
		upsampledBucket := NewTimeBucket(currentTime, targetGranularity)

		// Add entries that fall within this upsampled bucket
		entries := bucket.GetEntries()
		for _, entry := range entries {
			if entry.Timestamp.Equal(currentTime) ||
				(entry.Timestamp.After(currentTime) && entry.Timestamp.Before(currentTime.Add(targetDuration))) {
				if err := upsampledBucket.AddEntry(entry); err != nil {
					continue
				}
			}
		}

		result = append(result, upsampledBucket)
		currentTime = currentTime.Add(targetDuration)
	}

	return result, nil
}
