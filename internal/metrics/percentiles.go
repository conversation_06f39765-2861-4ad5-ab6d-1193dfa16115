// Package metrics provides advanced percentile calculation algorithms for streaming data
package metrics

import (
	"errors"
	"math"
	"sort"
	"sync"
	"sync/atomic"
)

// PercentileAlgorithm represents different percentile calculation algorithms
type PercentileAlgorithm int

const (
	AlgorithmExact PercentileAlgorithm = iota
	AlgorithmTDigest
	AlgorithmP2
	AlgorithmQuantileTree
)

var (
	ErrAlgorithmNotSupported = errors.New("percentiles: algorithm not supported")
	ErrInvalidQuantile       = errors.New("percentiles: quantile must be between 0 and 1")
)

// PercentileCalculator interface for different percentile algorithms
type PercentileCalculator interface {
	Add(value float64) error
	Quantile(q float64) (float64, error)
	Count() uint64
	Reset()
	Algorithm() PercentileAlgorithm
	MemoryUsage() int64 // Estimated memory usage in bytes
}

// ExactPercentileCalculator stores all values for exact percentile calculation
// Best for small datasets where memory is not a concern
type ExactPercentileCalculator struct {
	mu     sync.RWMutex
	values []float64
	sorted bool
	count  uint64
}

// NewExactPercentileCalculator creates a new exact percentile calculator
func NewExactPercentileCalculator() *ExactPercentileCalculator {
	return &ExactPercentileCalculator{
		values: make([]float64, 0, 1000), // Pre-allocate for 1000 values
		sorted: true,
	}
}

func (e *ExactPercentileCalculator) Add(value float64) error {
	if math.IsNaN(value) || math.IsInf(value, 0) {
		return nil // Silently ignore invalid values
	}

	e.mu.Lock()
	defer e.mu.Unlock()

	e.values = append(e.values, value)
	e.sorted = false
	atomic.AddUint64(&e.count, 1)
	return nil
}

func (e *ExactPercentileCalculator) Quantile(q float64) (float64, error) {
	if q < 0 || q > 1 {
		return 0, ErrInvalidQuantile
	}

	e.mu.Lock()
	defer e.mu.Unlock()

	if len(e.values) == 0 {
		return 0, ErrEmptyDataset
	}

	if !e.sorted {
		sort.Float64s(e.values)
		e.sorted = true
	}

	return e.calculateQuantile(q), nil
}

func (e *ExactPercentileCalculator) calculateQuantile(q float64) float64 {
	n := len(e.values)
	if n == 1 {
		return e.values[0]
	}

	if q == 0 {
		return e.values[0]
	}
	if q == 1 {
		return e.values[n-1]
	}

	// Use linear interpolation method
	index := q * float64(n-1)
	lower := int(math.Floor(index))
	upper := int(math.Ceil(index))

	if lower == upper {
		return e.values[lower]
	}

	weight := index - float64(lower)
	return e.values[lower]*(1-weight) + e.values[upper]*weight
}

func (e *ExactPercentileCalculator) Count() uint64 {
	return atomic.LoadUint64(&e.count)
}

func (e *ExactPercentileCalculator) Reset() {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.values = e.values[:0]
	e.sorted = true
	atomic.StoreUint64(&e.count, 0)
}

func (e *ExactPercentileCalculator) Algorithm() PercentileAlgorithm {
	return AlgorithmExact
}

func (e *ExactPercentileCalculator) MemoryUsage() int64 {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return int64(len(e.values) * 8) // 8 bytes per float64
}

// TDigestPercentileCalculator implements t-digest algorithm for approximate percentiles
// Memory-efficient with good accuracy for extreme percentiles
type TDigestPercentileCalculator struct {
	mu          sync.RWMutex
	centroids   []centroid
	count       uint64
	compression float64
	min         float64
	max         float64
	totalWeight float64
}

type centroid struct {
	mean   float64
	weight float64
}

// NewTDigestPercentileCalculator creates a new t-digest percentile calculator
func NewTDigestPercentileCalculator(compression float64) *TDigestPercentileCalculator {
	if compression <= 0 {
		compression = 100 // Default compression
	}

	return &TDigestPercentileCalculator{
		centroids:   make([]centroid, 0, int(compression*2)),
		compression: compression,
		min:         math.Inf(1),
		max:         math.Inf(-1),
	}
}

func (t *TDigestPercentileCalculator) Add(value float64) error {
	if math.IsNaN(value) || math.IsInf(value, 0) {
		return nil // Silently ignore invalid values
	}

	t.mu.Lock()
	defer t.mu.Unlock()

	t.addValue(value, 1.0)
	atomic.AddUint64(&t.count, 1)
	return nil
}

func (t *TDigestPercentileCalculator) addValue(value, weight float64) {
	if value < t.min {
		t.min = value
	}
	if value > t.max {
		t.max = value
	}

	t.totalWeight += weight

	// Find insertion point
	insertIndex := t.findInsertionPoint(value)

	// Try to merge with existing centroids
	merged := false

	// Check merge with previous centroid
	if insertIndex > 0 {
		prev := &t.centroids[insertIndex-1]
		if t.canMerge(prev, value, weight) {
			t.mergeCentroid(prev, value, weight)
			merged = true
		}
	}

	// Check merge with next centroid if not merged
	if !merged && insertIndex < len(t.centroids) {
		next := &t.centroids[insertIndex]
		if t.canMerge(next, value, weight) {
			t.mergeCentroid(next, value, weight)
			merged = true
		}
	}

	// If not merged, create new centroid
	if !merged {
		newCentroid := centroid{mean: value, weight: weight}
		t.centroids = append(t.centroids, centroid{})
		copy(t.centroids[insertIndex+1:], t.centroids[insertIndex:])
		t.centroids[insertIndex] = newCentroid
	}

	// Compress if needed
	if len(t.centroids) > int(t.compression*2) {
		t.compress()
	}
}

func (t *TDigestPercentileCalculator) findInsertionPoint(value float64) int {
	left, right := 0, len(t.centroids)
	for left < right {
		mid := (left + right) / 2
		if t.centroids[mid].mean < value {
			left = mid + 1
		} else {
			right = mid
		}
	}
	return left
}

func (t *TDigestPercentileCalculator) canMerge(c *centroid, value, weight float64) bool {
	if c.weight == 0 {
		return true
	}

	// Calculate the q value for this centroid's position
	cumulativeWeight := float64(0)
	for i := range t.centroids {
		if &t.centroids[i] == c {
			break
		}
		cumulativeWeight += t.centroids[i].weight
	}

	q := cumulativeWeight / t.totalWeight
	k := 4 * t.totalWeight * q * (1 - q)

	return c.weight+weight <= k/t.compression
}

func (t *TDigestPercentileCalculator) mergeCentroid(c *centroid, value, weight float64) {
	totalWeight := c.weight + weight
	c.mean = (c.mean*c.weight + value*weight) / totalWeight
	c.weight = totalWeight
}

func (t *TDigestPercentileCalculator) compress() {
	if len(t.centroids) <= 1 {
		return
	}

	newCentroids := make([]centroid, 0, len(t.centroids))
	current := t.centroids[0]

	for i := 1; i < len(t.centroids); i++ {
		next := t.centroids[i]
		if t.canMergeCentroids(&current, &next) {
			current.mean = (current.mean*current.weight + next.mean*next.weight) / (current.weight + next.weight)
			current.weight += next.weight
		} else {
			newCentroids = append(newCentroids, current)
			current = next
		}
	}

	newCentroids = append(newCentroids, current)
	t.centroids = newCentroids
}

func (t *TDigestPercentileCalculator) canMergeCentroids(c1, c2 *centroid) bool {
	return math.Abs(c1.mean-c2.mean) < 1e-10 // Merge very close centroids
}

func (t *TDigestPercentileCalculator) Quantile(q float64) (float64, error) {
	if q < 0 || q > 1 {
		return 0, ErrInvalidQuantile
	}

	t.mu.RLock()
	defer t.mu.RUnlock()

	if len(t.centroids) == 0 {
		return 0, ErrEmptyDataset
	}

	if len(t.centroids) == 1 {
		return t.centroids[0].mean, nil
	}

	if q == 0 {
		return t.min, nil
	}
	if q == 1 {
		return t.max, nil
	}

	targetWeight := q * t.totalWeight
	cumulativeWeight := float64(0)

	for i, c := range t.centroids {
		cumulativeWeight += c.weight

		if cumulativeWeight >= targetWeight {
			if i == 0 {
				return c.mean, nil
			}

			// Interpolate between this centroid and the previous one
			prevCentroid := t.centroids[i-1]
			prevCumulativeWeight := cumulativeWeight - c.weight

			if cumulativeWeight == prevCumulativeWeight {
				return c.mean, nil
			}

			// Linear interpolation
			alpha := (targetWeight - prevCumulativeWeight) / (cumulativeWeight - prevCumulativeWeight)
			return prevCentroid.mean + alpha*(c.mean-prevCentroid.mean), nil
		}
	}

	// Should not reach here
	return t.centroids[len(t.centroids)-1].mean, nil
}

func (t *TDigestPercentileCalculator) Count() uint64 {
	return atomic.LoadUint64(&t.count)
}

func (t *TDigestPercentileCalculator) Reset() {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.centroids = t.centroids[:0]
	t.min = math.Inf(1)
	t.max = math.Inf(-1)
	t.totalWeight = 0
	atomic.StoreUint64(&t.count, 0)
}

func (t *TDigestPercentileCalculator) Algorithm() PercentileAlgorithm {
	return AlgorithmTDigest
}

func (t *TDigestPercentileCalculator) MemoryUsage() int64 {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return int64(len(t.centroids) * 16) // 16 bytes per centroid (2 float64s)
}

// P2PercentileCalculator implements the P² algorithm for approximate quantiles
// Very memory efficient but less accurate for extreme quantiles
type P2PercentileCalculator struct {
	mu     sync.RWMutex
	q      [5]float64 // Heights
	n      [5]int     // Positions
	np     [5]float64 // Desired positions
	dn     [5]float64 // Desired position increments
	count  uint64
	target float64 // Target quantile
}

// NewP2PercentileCalculator creates a new P² algorithm percentile calculator
func NewP2PercentileCalculator(quantile float64) *P2PercentileCalculator {
	if quantile < 0 || quantile > 1 {
		quantile = 0.5 // Default to median
	}

	p := &P2PercentileCalculator{
		target: quantile,
	}

	// Initialize desired position increments
	p.dn[0] = 0
	p.dn[1] = quantile / 2
	p.dn[2] = quantile
	p.dn[3] = (1 + quantile) / 2
	p.dn[4] = 1

	return p
}

func (p *P2PercentileCalculator) Add(value float64) error {
	if math.IsNaN(value) || math.IsInf(value, 0) {
		return nil // Silently ignore invalid values
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	count := atomic.AddUint64(&p.count, 1)

	if count <= 5 {
		// Initialize the first 5 observations
		p.q[count-1] = value
		if count == 5 {
			// Sort the first 5 values
			sort.Float64s(p.q[:])
			for i := 0; i < 5; i++ {
				p.n[i] = i
				p.np[i] = p.dn[i] * 4
			}
		}
		return nil
	}

	// Find cell k
	k := p.findCell(value)

	// Update marker positions
	for i := k + 1; i < 5; i++ {
		p.n[i]++
	}

	// Update desired positions
	for i := 0; i < 5; i++ {
		p.np[i] += p.dn[i]
	}

	// Adjust heights of markers 2-4 if necessary
	for i := 1; i < 4; i++ {
		ni := float64(p.n[i])
		npi := p.np[i]
		d := npi - ni

		if (d >= 1 && p.n[i+1]-p.n[i] > 1) || (d <= -1 && p.n[i-1]-p.n[i] < -1) {
			d = math.Copysign(1, d)
			newQ := p.parabolicFormula(i, d)

			// Check if parabolic formula is valid
			if p.q[i-1] < newQ && newQ < p.q[i+1] {
				p.q[i] = newQ
			} else {
				// Use linear formula
				p.q[i] = p.linearFormula(i, d)
			}
			p.n[i] += int(d)
		}
	}

	return nil
}

func (p *P2PercentileCalculator) findCell(value float64) int {
	if value < p.q[0] {
		p.q[0] = value
		return 0
	}
	if value >= p.q[4] {
		p.q[4] = value
		return 3
	}

	for i := 0; i < 4; i++ {
		if value >= p.q[i] && value < p.q[i+1] {
			return i
		}
	}
	return 3
}

func (p *P2PercentileCalculator) parabolicFormula(i int, d float64) float64 {
	ni := float64(p.n[i])
	niMinus1 := float64(p.n[i-1])
	niPlus1 := float64(p.n[i+1])

	return p.q[i] + d/(niPlus1-niMinus1)*
		((ni-niMinus1+d)*(p.q[i+1]-p.q[i])/(niPlus1-ni)+
			(niPlus1-ni-d)*(p.q[i]-p.q[i-1])/(ni-niMinus1))
}

func (p *P2PercentileCalculator) linearFormula(i int, d float64) float64 {
	if d > 0 {
		return p.q[i] + d*(p.q[i+1]-p.q[i])/float64(p.n[i+1]-p.n[i])
	}
	return p.q[i] + d*(p.q[i]-p.q[i-1])/float64(p.n[i]-p.n[i-1])
}

func (p *P2PercentileCalculator) Quantile(q float64) (float64, error) {
	if q < 0 || q > 1 {
		return 0, ErrInvalidQuantile
	}

	p.mu.RLock()
	defer p.mu.RUnlock()

	count := p.Count()
	if count == 0 {
		return 0, ErrEmptyDataset
	}

	// For P² algorithm, we only calculate the target quantile
	if math.Abs(q-p.target) > 1e-10 {
		return 0, errors.New("P² algorithm only calculates the configured target quantile")
	}

	if count <= 5 {
		// Not enough data for P² algorithm, use exact calculation
		values := make([]float64, count)
		copy(values, p.q[:count])
		sort.Float64s(values)

		if len(values) == 1 {
			return values[0], nil
		}

		index := q * float64(len(values)-1)
		lower := int(math.Floor(index))
		upper := int(math.Ceil(index))

		if lower == upper {
			return values[lower], nil
		}

		weight := index - float64(lower)
		return values[lower]*(1-weight) + values[upper]*weight, nil
	}

	return p.q[2], nil // The middle marker contains our target quantile
}

func (p *P2PercentileCalculator) Count() uint64 {
	return atomic.LoadUint64(&p.count)
}

func (p *P2PercentileCalculator) Reset() {
	p.mu.Lock()
	defer p.mu.Unlock()

	for i := range p.q {
		p.q[i] = 0
		p.n[i] = 0
		p.np[i] = 0
	}

	// Reinitialize desired position increments
	p.dn[0] = 0
	p.dn[1] = p.target / 2
	p.dn[2] = p.target
	p.dn[3] = (1 + p.target) / 2
	p.dn[4] = 1

	atomic.StoreUint64(&p.count, 0)
}

func (p *P2PercentileCalculator) Algorithm() PercentileAlgorithm {
	return AlgorithmP2
}

func (p *P2PercentileCalculator) MemoryUsage() int64 {
	return 5*8 + 5*8 + 5*8 + 5*8 + 8 + 8 // All the arrays plus count and target
}

// PercentileCalculatorFactory creates percentile calculators based on algorithm type
func NewPercentileCalculator(algorithm PercentileAlgorithm, params ...interface{}) (PercentileCalculator, error) {
	switch algorithm {
	case AlgorithmExact:
		return NewExactPercentileCalculator(), nil
	case AlgorithmTDigest:
		compression := 100.0
		if len(params) > 0 {
			if c, ok := params[0].(float64); ok && c > 0 {
				compression = c
			}
		}
		return NewTDigestPercentileCalculator(compression), nil
	case AlgorithmP2:
		quantile := 0.5
		if len(params) > 0 {
			if q, ok := params[0].(float64); ok && q >= 0 && q <= 1 {
				quantile = q
			}
		}
		return NewP2PercentileCalculator(quantile), nil
	default:
		return nil, ErrAlgorithmNotSupported
	}
}

// MultiQuantileCalculator manages multiple percentile calculators for different quantiles
type MultiQuantileCalculator struct {
	mu         sync.RWMutex
	calculator PercentileCalculator
	quantiles  []float64
	algorithm  PercentileAlgorithm
	count      uint64
}

// NewMultiQuantileCalculator creates a calculator that can compute multiple quantiles
func NewMultiQuantileCalculator(algorithm PercentileAlgorithm, quantiles []float64) (*MultiQuantileCalculator, error) {
	for _, q := range quantiles {
		if q < 0 || q > 1 {
			return nil, ErrInvalidQuantile
		}
	}

	var calc PercentileCalculator

	// For multi-quantile scenarios, use algorithms that can handle multiple quantiles efficiently
	switch algorithm {
	case AlgorithmExact:
		calc = NewExactPercentileCalculator()
	case AlgorithmTDigest:
		calc = NewTDigestPercentileCalculator(100) // Default compression
	case AlgorithmP2:
		// P2 algorithm can only handle one quantile, so we'll use T-Digest instead
		// This provides better performance for multi-quantile scenarios
		calc = NewTDigestPercentileCalculator(100)
		algorithm = AlgorithmTDigest // Update algorithm type
	default:
		return nil, ErrAlgorithmNotSupported
	}

	return &MultiQuantileCalculator{
		calculator: calc,
		quantiles:  quantiles,
		algorithm:  algorithm,
	}, nil
}

func (m *MultiQuantileCalculator) Add(value float64) error {
	if math.IsNaN(value) || math.IsInf(value, 0) {
		return nil // Silently ignore invalid values
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	atomic.AddUint64(&m.count, 1)
	return m.calculator.Add(value)
}

func (m *MultiQuantileCalculator) Quantile(q float64) (float64, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Check if quantile is configured
	found := false
	for _, configuredQ := range m.quantiles {
		if math.Abs(configuredQ-q) < 1e-10 {
			found = true
			break
		}
	}

	if !found {
		return 0, errors.New("quantile not configured")
	}

	return m.calculator.Quantile(q)
}

func (m *MultiQuantileCalculator) AllQuantiles() (map[float64]float64, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	results := make(map[float64]float64)

	for _, q := range m.quantiles {
		value, err := m.calculator.Quantile(q)
		if err != nil {
			return nil, err
		}
		results[q] = value
	}

	return results, nil
}

func (m *MultiQuantileCalculator) Count() uint64 {
	return atomic.LoadUint64(&m.count)
}

func (m *MultiQuantileCalculator) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.calculator.Reset()
	atomic.StoreUint64(&m.count, 0)
}

func (m *MultiQuantileCalculator) TotalMemoryUsage() int64 {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.calculator.MemoryUsage()
}
