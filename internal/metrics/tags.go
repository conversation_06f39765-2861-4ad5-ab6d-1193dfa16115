// Package metrics provides metrics collection and reporting functionality
package metrics

import (
	"fmt"
	"sort"
	"strings"
	"sync"
)

// TagSet represents an immutable set of key-value tags
type TagSet struct {
	tags   map[string]string
	sorted []string // Cached sorted representation for efficient comparison
	hash   uint64   // Cached hash for fast equality checks
}

// NewTagSet creates a new TagSet from a map of tags
func NewTagSet(tags map[string]string) *TagSet {
	if tags == nil {
		tags = make(map[string]string)
	}

	// Create a copy to ensure immutability
	tagsCopy := make(map[string]string, len(tags))
	for k, v := range tags {
		if k != "" { // Skip empty keys
			tagsCopy[k] = v
		}
	}

	ts := &TagSet{
		tags: tagsCopy,
	}

	ts.computeSortedAndHash()
	return ts
}

// NewTagSetFromPairs creates a TagSet from alternating key-value pairs
func NewTagSetFromPairs(pairs ...string) *TagSet {
	if len(pairs)%2 != 0 {
		pairs = pairs[:len(pairs)-1] // Remove last element if odd count
	}

	tags := make(map[string]string, len(pairs)/2)
	for i := 0; i < len(pairs); i += 2 {
		if pairs[i] != "" { // Skip empty keys
			tags[pairs[i]] = pairs[i+1]
		}
	}

	return NewTagSet(tags)
}

// computeSortedAndHash computes the sorted representation and hash
func (ts *TagSet) computeSortedAndHash() {
	// Create sorted representation for consistent comparison
	keys := make([]string, 0, len(ts.tags))
	for k := range ts.tags {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	ts.sorted = make([]string, 0, len(ts.tags)*2)
	var hashBuilder strings.Builder
	for _, k := range keys {
		v := ts.tags[k]
		ts.sorted = append(ts.sorted, k, v)
		hashBuilder.WriteString(k)
		hashBuilder.WriteByte('=')
		hashBuilder.WriteString(v)
		hashBuilder.WriteByte(',')
	}

	// Simple hash computation (could be improved with a proper hash function)
	hashStr := hashBuilder.String()
	ts.hash = simpleHash(hashStr)
}

// simpleHash computes a simple hash for a string
func simpleHash(s string) uint64 {
	var hash uint64 = 5381
	for _, c := range s {
		hash = ((hash << 5) + hash) + uint64(c)
	}
	return hash
}

// Get returns the value for a given key, and whether the key exists
func (ts *TagSet) Get(key string) (string, bool) {
	value, exists := ts.tags[key]
	return value, exists
}

// Has returns whether the TagSet contains the given key
func (ts *TagSet) Has(key string) bool {
	_, exists := ts.tags[key]
	return exists
}

// Len returns the number of tags in the set
func (ts *TagSet) Len() int {
	return len(ts.tags)
}

// Keys returns all keys in the TagSet
func (ts *TagSet) Keys() []string {
	keys := make([]string, 0, len(ts.tags))
	for k := range ts.tags {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	return keys
}

// ToMap returns a copy of the underlying tag map
func (ts *TagSet) ToMap() map[string]string {
	result := make(map[string]string, len(ts.tags))
	for k, v := range ts.tags {
		result[k] = v
	}
	return result
}

// String returns a string representation of the TagSet
func (ts *TagSet) String() string {
	if len(ts.tags) == 0 {
		return "{}"
	}

	var builder strings.Builder
	builder.WriteByte('{')
	first := true
	for i := 0; i < len(ts.sorted); i += 2 {
		if !first {
			builder.WriteString(", ")
		}
		builder.WriteString(ts.sorted[i])
		builder.WriteByte('=')
		builder.WriteString(ts.sorted[i+1])
		first = false
	}
	builder.WriteByte('}')
	return builder.String()
}

// Equals returns whether two TagSets are equal
func (ts *TagSet) Equals(other *TagSet) bool {
	if ts == other {
		return true
	}
	if other == nil {
		return false
	}
	if ts.hash != other.hash {
		return false
	}
	if len(ts.tags) != len(other.tags) {
		return false
	}

	for k, v := range ts.tags {
		if otherV, exists := other.tags[k]; !exists || v != otherV {
			return false
		}
	}
	return true
}

// With returns a new TagSet with the additional tag
func (ts *TagSet) With(key, value string) *TagSet {
	if key == "" {
		return ts
	}

	newTags := make(map[string]string, len(ts.tags)+1)
	for k, v := range ts.tags {
		newTags[k] = v
	}
	newTags[key] = value
	return NewTagSet(newTags)
}

// Without returns a new TagSet without the specified key
func (ts *TagSet) Without(key string) *TagSet {
	if !ts.Has(key) {
		return ts
	}

	newTags := make(map[string]string, len(ts.tags)-1)
	for k, v := range ts.tags {
		if k != key {
			newTags[k] = v
		}
	}
	return NewTagSet(newTags)
}

// Merge returns a new TagSet with tags from both sets (other takes precedence)
func (ts *TagSet) Merge(other *TagSet) *TagSet {
	if other == nil || other.Len() == 0 {
		return ts
	}
	if ts.Len() == 0 {
		return other
	}

	newTags := make(map[string]string, len(ts.tags)+len(other.tags))
	for k, v := range ts.tags {
		newTags[k] = v
	}
	for k, v := range other.tags {
		newTags[k] = v
	}
	return NewTagSet(newTags)
}

// TagFilter represents a filter for matching TagSets
type TagFilter struct {
	conditions []TagCondition
	logic      FilterLogic
}

// FilterLogic represents the logical operation between conditions
type FilterLogic int

const (
	FilterLogicAND FilterLogic = iota
	FilterLogicOR
)

// TagCondition represents a single tag matching condition
type TagCondition struct {
	Key       string
	Value     string
	Operation ConditionOperation
}

// ConditionOperation represents the type of condition operation
type ConditionOperation int

const (
	ConditionEquals ConditionOperation = iota
	ConditionNotEquals
	ConditionExists
	ConditionNotExists
	ConditionContains
	ConditionStartsWith
	ConditionEndsWith
)

// NewTagFilter creates a new TagFilter with AND logic
func NewTagFilter() *TagFilter {
	return &TagFilter{
		conditions: make([]TagCondition, 0),
		logic:      FilterLogicAND,
	}
}

// NewTagFilterOR creates a new TagFilter with OR logic
func NewTagFilterOR() *TagFilter {
	return &TagFilter{
		conditions: make([]TagCondition, 0),
		logic:      FilterLogicOR,
	}
}

// Equals adds an equality condition
func (tf *TagFilter) Equals(key, value string) *TagFilter {
	tf.conditions = append(tf.conditions, TagCondition{
		Key:       key,
		Value:     value,
		Operation: ConditionEquals,
	})
	return tf
}

// NotEquals adds a not-equals condition
func (tf *TagFilter) NotEquals(key, value string) *TagFilter {
	tf.conditions = append(tf.conditions, TagCondition{
		Key:       key,
		Value:     value,
		Operation: ConditionNotEquals,
	})
	return tf
}

// Exists adds a key-exists condition
func (tf *TagFilter) Exists(key string) *TagFilter {
	tf.conditions = append(tf.conditions, TagCondition{
		Key:       key,
		Operation: ConditionExists,
	})
	return tf
}

// NotExists adds a key-not-exists condition
func (tf *TagFilter) NotExists(key string) *TagFilter {
	tf.conditions = append(tf.conditions, TagCondition{
		Key:       key,
		Operation: ConditionNotExists,
	})
	return tf
}

// Contains adds a value-contains condition
func (tf *TagFilter) Contains(key, substring string) *TagFilter {
	tf.conditions = append(tf.conditions, TagCondition{
		Key:       key,
		Value:     substring,
		Operation: ConditionContains,
	})
	return tf
}

// StartsWith adds a value-starts-with condition
func (tf *TagFilter) StartsWith(key, prefix string) *TagFilter {
	tf.conditions = append(tf.conditions, TagCondition{
		Key:       key,
		Value:     prefix,
		Operation: ConditionStartsWith,
	})
	return tf
}

// EndsWith adds a value-ends-with condition
func (tf *TagFilter) EndsWith(key, suffix string) *TagFilter {
	tf.conditions = append(tf.conditions, TagCondition{
		Key:       key,
		Value:     suffix,
		Operation: ConditionEndsWith,
	})
	return tf
}

// Matches returns whether the TagSet matches this filter
func (tf *TagFilter) Matches(tags *TagSet) bool {
	if len(tf.conditions) == 0 {
		return true // Empty filter matches everything
	}

	if tf.logic == FilterLogicAND {
		// All conditions must match
		for _, condition := range tf.conditions {
			if !tf.matchesCondition(tags, condition) {
				return false
			}
		}
		return true
	} else {
		// At least one condition must match
		for _, condition := range tf.conditions {
			if tf.matchesCondition(tags, condition) {
				return true
			}
		}
		return false
	}
}

// matchesCondition checks if a TagSet matches a single condition
func (tf *TagFilter) matchesCondition(tags *TagSet, condition TagCondition) bool {
	value, exists := tags.Get(condition.Key)

	switch condition.Operation {
	case ConditionEquals:
		return exists && value == condition.Value
	case ConditionNotEquals:
		return !exists || value != condition.Value
	case ConditionExists:
		return exists
	case ConditionNotExists:
		return !exists
	case ConditionContains:
		return exists && strings.Contains(value, condition.Value)
	case ConditionStartsWith:
		return exists && strings.HasPrefix(value, condition.Value)
	case ConditionEndsWith:
		return exists && strings.HasSuffix(value, condition.Value)
	default:
		return false
	}
}

// String returns a string representation of the filter
func (tf *TagFilter) String() string {
	if len(tf.conditions) == 0 {
		return "match-all"
	}

	var builder strings.Builder
	logicStr := " AND "
	if tf.logic == FilterLogicOR {
		logicStr = " OR "
	}

	for i, condition := range tf.conditions {
		if i > 0 {
			builder.WriteString(logicStr)
		}
		builder.WriteString(tf.conditionString(condition))
	}
	return builder.String()
}

// conditionString returns a string representation of a condition
func (tf *TagFilter) conditionString(condition TagCondition) string {
	switch condition.Operation {
	case ConditionEquals:
		return fmt.Sprintf("%s=%s", condition.Key, condition.Value)
	case ConditionNotEquals:
		return fmt.Sprintf("%s!=%s", condition.Key, condition.Value)
	case ConditionExists:
		return fmt.Sprintf("exists(%s)", condition.Key)
	case ConditionNotExists:
		return fmt.Sprintf("!exists(%s)", condition.Key)
	case ConditionContains:
		return fmt.Sprintf("%s~=%s", condition.Key, condition.Value)
	case ConditionStartsWith:
		return fmt.Sprintf("%s^=%s", condition.Key, condition.Value)
	case ConditionEndsWith:
		return fmt.Sprintf("%s$=%s", condition.Key, condition.Value)
	default:
		return fmt.Sprintf("unknown(%s)", condition.Key)
	}
}

// TagIndex provides efficient lookup of metrics by tags
type TagIndex struct {
	mu      sync.RWMutex
	entries map[string]*TagIndexEntry      // metric name -> entry
	byTag   map[string]map[string][]string // tag key -> tag value -> metric names
}

// TagIndexEntry represents an entry in the tag index
type TagIndexEntry struct {
	MetricName string
	Tags       *TagSet
	MetricType string
}

// NewTagIndex creates a new tag index
func NewTagIndex() *TagIndex {
	return &TagIndex{
		entries: make(map[string]*TagIndexEntry),
		byTag:   make(map[string]map[string][]string),
	}
}

// Register adds a metric to the tag index
func (ti *TagIndex) Register(metricName string, tags *TagSet, metricType string) {
	ti.mu.Lock()
	defer ti.mu.Unlock()

	// Remove existing entry if it exists
	if existing, exists := ti.entries[metricName]; exists {
		ti.removeFromIndex(existing)
	}

	// Create new entry
	entry := &TagIndexEntry{
		MetricName: metricName,
		Tags:       tags,
		MetricType: metricType,
	}

	ti.entries[metricName] = entry
	ti.addToIndex(entry)
}

// Unregister removes a metric from the tag index
func (ti *TagIndex) Unregister(metricName string) {
	ti.mu.Lock()
	defer ti.mu.Unlock()

	if entry, exists := ti.entries[metricName]; exists {
		ti.removeFromIndex(entry)
		delete(ti.entries, metricName)
	}
}

// Find returns metric names that match the given filter
func (ti *TagIndex) Find(filter *TagFilter) []string {
	ti.mu.RLock()
	defer ti.mu.RUnlock()

	var result []string
	for metricName, entry := range ti.entries {
		if filter.Matches(entry.Tags) {
			result = append(result, metricName)
		}
	}

	sort.Strings(result)
	return result
}

// FindByTag returns metric names that have the specified tag key-value pair
func (ti *TagIndex) FindByTag(key, value string) []string {
	ti.mu.RLock()
	defer ti.mu.RUnlock()

	if valueMap, exists := ti.byTag[key]; exists {
		if metricNames, exists := valueMap[value]; exists {
			// Return a copy
			result := make([]string, len(metricNames))
			copy(result, metricNames)
			sort.Strings(result)
			return result
		}
	}

	return []string{}
}

// GetTags returns the tags for a given metric name
func (ti *TagIndex) GetTags(metricName string) *TagSet {
	ti.mu.RLock()
	defer ti.mu.RUnlock()

	if entry, exists := ti.entries[metricName]; exists {
		return entry.Tags
	}
	return nil
}

// GetMetricType returns the metric type for a given metric name
func (ti *TagIndex) GetMetricType(metricName string) string {
	ti.mu.RLock()
	defer ti.mu.RUnlock()

	if entry, exists := ti.entries[metricName]; exists {
		return entry.MetricType
	}
	return ""
}

// ListAll returns all registered metric names
func (ti *TagIndex) ListAll() []string {
	ti.mu.RLock()
	defer ti.mu.RUnlock()

	result := make([]string, 0, len(ti.entries))
	for metricName := range ti.entries {
		result = append(result, metricName)
	}
	sort.Strings(result)
	return result
}

// addToIndex adds an entry to the tag index
func (ti *TagIndex) addToIndex(entry *TagIndexEntry) {
	for key, value := range entry.Tags.ToMap() {
		if ti.byTag[key] == nil {
			ti.byTag[key] = make(map[string][]string)
		}
		ti.byTag[key][value] = append(ti.byTag[key][value], entry.MetricName)
	}
}

// removeFromIndex removes an entry from the tag index
func (ti *TagIndex) removeFromIndex(entry *TagIndexEntry) {
	for key, value := range entry.Tags.ToMap() {
		if valueMap, exists := ti.byTag[key]; exists {
			if metricNames, exists := valueMap[value]; exists {
				// Remove the metric name from the slice
				for i, name := range metricNames {
					if name == entry.MetricName {
						valueMap[value] = append(metricNames[:i], metricNames[i+1:]...)
						break
					}
				}
				// Clean up empty slices
				if len(valueMap[value]) == 0 {
					delete(valueMap, value)
				}
			}
			// Clean up empty maps
			if len(valueMap) == 0 {
				delete(ti.byTag, key)
			}
		}
	}
}
