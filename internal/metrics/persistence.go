package metrics

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sync"
	"sync/atomic"
	"time"
)

// PersistenceManager handles disk-based storage for metrics
type PersistenceManager struct {
	path            string
	retentionPeriod time.Duration
	ctx             context.Context
	cancel          context.CancelFunc
	cleanupTicker   *time.Ticker
	persistQueue    chan *StorageEntry
	running         int32
	stats           PersistenceStats
	mu              sync.RWMutex
}

// PersistenceStats tracks persistence statistics
type PersistenceStats struct {
	TotalPersisted int64     `json:"total_persisted"`
	TotalBytes     int64     `json:"total_bytes"`
	FilesCreated   int64     `json:"files_created"`
	FilesDeleted   int64     `json:"files_deleted"`
	LastPersist    time.Time `json:"last_persist"`
	LastCleanup    time.Time `json:"last_cleanup"`
	QueueSize      int       `json:"queue_size"`
	PersistErrors  int64     `json:"persist_errors"`
	CleanupErrors  int64     `json:"cleanup_errors"`
}

// NewPersistenceManager creates a new persistence manager
func NewPersistenceManager(path string, retentionPeriod time.Duration) *PersistenceManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &PersistenceManager{
		path:            path,
		retentionPeriod: retentionPeriod,
		ctx:             ctx,
		cancel:          cancel,
		persistQueue:    make(chan *StorageEntry, 10000), // Buffer 10K entries
		stats:           PersistenceStats{},
	}
}

// Start starts the persistence manager
func (pm *PersistenceManager) Start() error {
	if !atomic.CompareAndSwapInt32(&pm.running, 0, 1) {
		return nil // Already running
	}

	// Create storage directory if it doesn't exist
	if err := os.MkdirAll(pm.path, 0755); err != nil {
		atomic.StoreInt32(&pm.running, 0)
		return fmt.Errorf("failed to create storage directory: %w", err)
	}

	// Start cleanup ticker (cleanup every hour)
	pm.cleanupTicker = time.NewTicker(time.Hour)

	// Start background goroutines
	go pm.runPersistWorker()
	go pm.runCleanupWorker()

	return nil
}

// Stop stops the persistence manager
func (pm *PersistenceManager) Stop() error {
	if !atomic.CompareAndSwapInt32(&pm.running, 1, 0) {
		return nil // Already stopped
	}

	// Cancel context
	pm.cancel()

	// Stop cleanup ticker
	if pm.cleanupTicker != nil {
		pm.cleanupTicker.Stop()
	}

	// Close persist queue
	close(pm.persistQueue)

	// Perform final cleanup
	pm.cleanup()

	return nil
}

// Persist queues an entry for persistence
func (pm *PersistenceManager) Persist(entry *StorageEntry) {
	if atomic.LoadInt32(&pm.running) == 0 {
		return
	}

	select {
	case pm.persistQueue <- entry:
		// Entry queued successfully
	default:
		// Queue is full, increment error counter
		atomic.AddInt64(&pm.stats.PersistErrors, 1)
	}
}

// Load loads persisted entries for a given key and time range
func (pm *PersistenceManager) Load(key string, maxAge time.Duration) ([]*StorageEntry, error) {
	if atomic.LoadInt32(&pm.running) == 0 {
		return nil, fmt.Errorf("persistence manager is not running")
	}

	var entries []*StorageEntry
	now := time.Now()

	// Walk through storage directory
	err := filepath.WalkDir(pm.path, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.IsDir() {
			return nil
		}

		// Check file extension
		if filepath.Ext(path) != ".json" {
			return nil
		}

		// Check file age
		info, err := d.Info()
		if err != nil {
			return nil // Skip files we can't stat
		}

		if maxAge > 0 && now.Sub(info.ModTime()) > maxAge {
			return nil // Skip old files
		}

		// Load and parse file
		fileEntries, err := pm.loadFile(path, key)
		if err != nil {
			return nil // Skip files we can't parse
		}

		entries = append(entries, fileEntries...)
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to walk storage directory: %w", err)
	}

	return entries, nil
}

// GetStats returns persistence statistics
func (pm *PersistenceManager) GetStats() PersistenceStats {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	stats := pm.stats
	stats.QueueSize = len(pm.persistQueue)
	return stats
}

// runPersistWorker runs the background persistence worker
func (pm *PersistenceManager) runPersistWorker() {
	batch := make([]*StorageEntry, 0, 100)
	ticker := time.NewTicker(5 * time.Second) // Flush every 5 seconds
	defer ticker.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			// Flush remaining entries
			if len(batch) > 0 {
				pm.persistBatch(batch)
			}
			return

		case entry, ok := <-pm.persistQueue:
			if !ok {
				// Channel closed, flush remaining entries
				if len(batch) > 0 {
					pm.persistBatch(batch)
				}
				return
			}

			batch = append(batch, entry)

			// Flush if batch is full
			if len(batch) >= 100 {
				pm.persistBatch(batch)
				batch = batch[:0] // Reset slice but keep capacity
			}

		case <-ticker.C:
			// Periodic flush
			if len(batch) > 0 {
				pm.persistBatch(batch)
				batch = batch[:0] // Reset slice but keep capacity
			}
		}
	}
}

// runCleanupWorker runs the background cleanup worker
func (pm *PersistenceManager) runCleanupWorker() {
	for {
		select {
		case <-pm.ctx.Done():
			return
		case <-pm.cleanupTicker.C:
			pm.cleanup()
		}
	}
}

// persistBatch persists a batch of entries to disk
func (pm *PersistenceManager) persistBatch(entries []*StorageEntry) {
	if len(entries) == 0 {
		return
	}

	// Group entries by day for organization
	dayGroups := make(map[string][]*StorageEntry)

	for _, entry := range entries {
		day := entry.Timestamp.Format("2006-01-02")
		dayGroups[day] = append(dayGroups[day], entry)
	}

	// Persist each day group
	for day, dayEntries := range dayGroups {
		if err := pm.persistDayGroup(day, dayEntries); err != nil {
			atomic.AddInt64(&pm.stats.PersistErrors, 1)
		}
	}

	// Update stats
	atomic.AddInt64(&pm.stats.TotalPersisted, int64(len(entries)))
	pm.mu.Lock()
	pm.stats.LastPersist = time.Now()
	pm.mu.Unlock()
}

// persistDayGroup persists entries for a specific day
func (pm *PersistenceManager) persistDayGroup(day string, entries []*StorageEntry) error {
	// Create day directory
	dayDir := filepath.Join(pm.path, day)
	if err := os.MkdirAll(dayDir, 0755); err != nil {
		return fmt.Errorf("failed to create day directory: %w", err)
	}

	// Create file with timestamp
	timestamp := time.Now().Format("15-04-05.000")
	filename := filepath.Join(dayDir, fmt.Sprintf("metrics_%s.json", timestamp))

	// Open file for writing
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	// Write entries as JSON
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")

	for _, entry := range entries {
		if err := encoder.Encode(entry); err != nil {
			return fmt.Errorf("failed to encode entry: %w", err)
		}
	}

	// Update stats
	info, err := file.Stat()
	if err == nil {
		atomic.AddInt64(&pm.stats.TotalBytes, info.Size())
	}
	atomic.AddInt64(&pm.stats.FilesCreated, 1)

	return nil
}

// loadFile loads entries from a persistence file
func (pm *PersistenceManager) loadFile(filename, key string) ([]*StorageEntry, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var entries []*StorageEntry
	decoder := json.NewDecoder(file)

	for decoder.More() {
		var entry StorageEntry
		if err := decoder.Decode(&entry); err != nil {
			continue // Skip malformed entries
		}

		// Filter by key if specified
		if key != "" && entry.Key != key {
			continue
		}

		entries = append(entries, &entry)
	}

	return entries, nil
}

// cleanup removes old files based on retention period
func (pm *PersistenceManager) cleanup() {
	cutoff := time.Now().Add(-pm.retentionPeriod)
	deletedCount := int64(0)

	err := filepath.WalkDir(pm.path, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.IsDir() {
			return nil
		}

		// Check file age
		info, err := d.Info()
		if err != nil {
			return nil // Skip files we can't stat
		}

		if info.ModTime().Before(cutoff) {
			if err := os.Remove(path); err == nil {
				deletedCount++
			} else {
				atomic.AddInt64(&pm.stats.CleanupErrors, 1)
			}
		}

		return nil
	})

	if err != nil {
		atomic.AddInt64(&pm.stats.CleanupErrors, 1)
	}

	// Update stats
	atomic.AddInt64(&pm.stats.FilesDeleted, deletedCount)
	pm.mu.Lock()
	pm.stats.LastCleanup = time.Now()
	pm.mu.Unlock()

	// Remove empty directories
	pm.removeEmptyDirs()
}

// removeEmptyDirs removes empty directories in the storage path
func (pm *PersistenceManager) removeEmptyDirs() {
	filepath.WalkDir(pm.path, func(path string, d fs.DirEntry, err error) error {
		if err != nil || !d.IsDir() || path == pm.path {
			return err
		}

		// Try to remove directory (will fail if not empty)
		os.Remove(path)
		return nil
	})
}

// Compact compacts storage by merging and compressing old files
func (pm *PersistenceManager) Compact() error {
	if atomic.LoadInt32(&pm.running) == 0 {
		return fmt.Errorf("persistence manager is not running")
	}

	// This is a placeholder for a more sophisticated compaction algorithm
	// In a real implementation, this would:
	// 1. Identify old files that can be compacted
	// 2. Merge multiple small files into larger ones
	// 3. Apply compression to reduce disk usage
	// 4. Remove duplicate entries
	// 5. Update indexes if any

	return nil
}

// Export exports all persisted data to a single file
func (pm *PersistenceManager) Export(outputPath string) error {
	if atomic.LoadInt32(&pm.running) == 0 {
		return fmt.Errorf("persistence manager is not running")
	}

	// Load all entries
	entries, err := pm.Load("", 0) // No key filter, no age limit
	if err != nil {
		return fmt.Errorf("failed to load entries: %w", err)
	}

	// Create output file
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer file.Close()

	// Write entries as JSON
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")

	for _, entry := range entries {
		if err := encoder.Encode(entry); err != nil {
			return fmt.Errorf("failed to encode entry: %w", err)
		}
	}

	return nil
}
