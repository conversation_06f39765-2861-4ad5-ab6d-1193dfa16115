package metrics

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"sync"
	"sync/atomic"
)

// CompressionManager handles data compression and decompression
type CompressionManager struct {
	level             int
	compressedBytes   int64
	uncompressedBytes int64
	mu                sync.RWMutex
}

// CompressionStats tracks compression statistics
type CompressionStats struct {
	CompressedBytes   int64   `json:"compressed_bytes"`
	UncompressedBytes int64   `json:"uncompressed_bytes"`
	CompressionRatio  float64 `json:"compression_ratio"`
	Operations        int64   `json:"operations"`
}

// NewCompressionManager creates a new compression manager
func NewCompressionManager(level int) *CompressionManager {
	// Validate compression level (1-9 for gzip)
	if level < 1 || level > 9 {
		level = 6 // Default to balanced compression
	}

	return &CompressionManager{
		level: level,
	}
}

// Compress compresses a storage entry
func (cm *CompressionManager) Compress(entry *StorageEntry) (*StorageEntry, error) {
	if entry == nil {
		return nil, fmt.Errorf("entry cannot be nil")
	}

	// Don't compress if already compressed
	if entry.Compressed {
		return entry, nil
	}

	// Serialize the value to JSON
	valueBytes, err := json.Marshal(entry.Value)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal value: %w", err)
	}

	// Compress the data
	var compressedBuf bytes.Buffer
	writer, err := gzip.NewWriterLevel(&compressedBuf, cm.level)
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip writer: %w", err)
	}

	if _, err := writer.Write(valueBytes); err != nil {
		writer.Close()
		return nil, fmt.Errorf("failed to write compressed data: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close gzip writer: %w", err)
	}

	// Create compressed entry
	compressedEntry := &StorageEntry{
		Key:        entry.Key,
		Value:      compressedBuf.Bytes(),
		Tags:       entry.Tags,
		Timestamp:  entry.Timestamp,
		TTL:        entry.TTL,
		Compressed: true,
	}

	// Update statistics
	uncompressedSize := int64(len(valueBytes))
	compressedSize := int64(compressedBuf.Len())

	atomic.AddInt64(&cm.uncompressedBytes, uncompressedSize)
	atomic.AddInt64(&cm.compressedBytes, compressedSize)

	return compressedEntry, nil
}

// Decompress decompresses a storage entry
func (cm *CompressionManager) Decompress(entry *StorageEntry) (*StorageEntry, error) {
	if entry == nil {
		return nil, fmt.Errorf("entry cannot be nil")
	}

	// Don't decompress if not compressed
	if !entry.Compressed {
		return entry, nil
	}

	// Get compressed data
	compressedData, ok := entry.Value.([]byte)
	if !ok {
		return nil, fmt.Errorf("compressed entry value must be []byte")
	}

	// Decompress the data
	reader, err := gzip.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer reader.Close()

	var decompressedBuf bytes.Buffer
	if _, err := io.Copy(&decompressedBuf, reader); err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}

	// Deserialize the value from JSON
	var value interface{}
	if err := json.Unmarshal(decompressedBuf.Bytes(), &value); err != nil {
		return nil, fmt.Errorf("failed to unmarshal value: %w", err)
	}

	// Create decompressed entry
	decompressedEntry := &StorageEntry{
		Key:        entry.Key,
		Value:      value,
		Tags:       entry.Tags,
		Timestamp:  entry.Timestamp,
		TTL:        entry.TTL,
		Compressed: false,
	}

	return decompressedEntry, nil
}

// CompressBatch compresses multiple entries in batch
func (cm *CompressionManager) CompressBatch(entries []*StorageEntry) ([]*StorageEntry, error) {
	if len(entries) == 0 {
		return entries, nil
	}

	result := make([]*StorageEntry, len(entries))
	var wg sync.WaitGroup
	errChan := make(chan error, len(entries))

	// Compress entries in parallel
	for i, entry := range entries {
		wg.Add(1)
		go func(idx int, e *StorageEntry) {
			defer wg.Done()

			compressed, err := cm.Compress(e)
			if err != nil {
				errChan <- fmt.Errorf("failed to compress entry %d: %w", idx, err)
				return
			}

			result[idx] = compressed
		}(i, entry)
	}

	wg.Wait()
	close(errChan)

	// Check for errors
	if len(errChan) > 0 {
		return nil, <-errChan
	}

	return result, nil
}

// DecompressBatch decompresses multiple entries in batch
func (cm *CompressionManager) DecompressBatch(entries []*StorageEntry) ([]*StorageEntry, error) {
	if len(entries) == 0 {
		return entries, nil
	}

	result := make([]*StorageEntry, len(entries))
	var wg sync.WaitGroup
	errChan := make(chan error, len(entries))

	// Decompress entries in parallel
	for i, entry := range entries {
		wg.Add(1)
		go func(idx int, e *StorageEntry) {
			defer wg.Done()

			decompressed, err := cm.Decompress(e)
			if err != nil {
				errChan <- fmt.Errorf("failed to decompress entry %d: %w", idx, err)
				return
			}

			result[idx] = decompressed
		}(i, entry)
	}

	wg.Wait()
	close(errChan)

	// Check for errors
	if len(errChan) > 0 {
		return nil, <-errChan
	}

	return result, nil
}

// SetLevel sets the compression level
func (cm *CompressionManager) SetLevel(level int) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// Validate compression level
	if level >= 1 && level <= 9 {
		cm.level = level
	}
}

// GetLevel returns the current compression level
func (cm *CompressionManager) GetLevel() int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.level
}

// GetStats returns compression statistics
func (cm *CompressionManager) GetStats() CompressionStats {
	compressedBytes := atomic.LoadInt64(&cm.compressedBytes)
	uncompressedBytes := atomic.LoadInt64(&cm.uncompressedBytes)

	var ratio float64
	if uncompressedBytes > 0 {
		ratio = 1.0 - (float64(compressedBytes) / float64(uncompressedBytes))
	}

	return CompressionStats{
		CompressedBytes:   compressedBytes,
		UncompressedBytes: uncompressedBytes,
		CompressionRatio:  ratio,
		Operations:        uncompressedBytes / 1024, // Rough estimate of operations
	}
}

// Reset resets compression statistics
func (cm *CompressionManager) Reset() {
	atomic.StoreInt64(&cm.compressedBytes, 0)
	atomic.StoreInt64(&cm.uncompressedBytes, 0)
}

// EstimateCompressionRatio estimates the compression ratio for given data
func (cm *CompressionManager) EstimateCompressionRatio(data []byte) float64 {
	if len(data) == 0 {
		return 0
	}

	// Quick compression test
	var buf bytes.Buffer
	writer, err := gzip.NewWriterLevel(&buf, cm.level)
	if err != nil {
		return 0
	}

	writer.Write(data)
	writer.Close()

	if buf.Len() == 0 {
		return 0
	}

	return 1.0 - (float64(buf.Len()) / float64(len(data)))
}

// ShouldCompress determines if data should be compressed based on size and type
func (cm *CompressionManager) ShouldCompress(entry *StorageEntry) bool {
	if entry == nil || entry.Compressed {
		return false
	}

	// Don't compress small entries (overhead not worth it)
	estimatedSize := cm.estimateEntrySize(entry)
	if estimatedSize < 100 { // Less than 100 bytes
		return false
	}

	// Don't compress binary data that's likely already compressed
	if data, ok := entry.Value.([]byte); ok {
		// Simple heuristic: if data has low entropy, it's likely compressible
		return cm.hasHighEntropy(data)
	}

	return true
}

// estimateEntrySize estimates the size of an entry
func (cm *CompressionManager) estimateEntrySize(entry *StorageEntry) int64 {
	if entry == nil {
		return 0
	}

	size := int64(len(entry.Key))

	switch v := entry.Value.(type) {
	case string:
		size += int64(len(v))
	case []byte:
		size += int64(len(v))
	case int, int32, int64, float32, float64:
		size += 8
	default:
		// Rough estimate for complex types
		size += 64
	}

	return size
}

// hasHighEntropy checks if data has high entropy (is compressible)
func (cm *CompressionManager) hasHighEntropy(data []byte) bool {
	if len(data) < 10 {
		return false
	}

	// Simple entropy check: count unique bytes
	uniqueBytes := make(map[byte]bool)
	for _, b := range data[:min(100, len(data))] { // Check first 100 bytes
		uniqueBytes[b] = true
	}

	// If more than 50% of bytes are unique, consider it high entropy
	entropy := float64(len(uniqueBytes)) / float64(min(100, len(data)))
	return entropy > 0.5
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
