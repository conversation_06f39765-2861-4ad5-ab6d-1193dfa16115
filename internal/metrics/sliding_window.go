// Package metrics provides sliding window aggregation functionality for time-series metrics
package metrics

import (
	"errors"
	"math"
	"sync"
	"sync/atomic"
	"time"
	"unsafe"
)

// Common errors
var (
	ErrEmptyWindow     = errors.New("sliding window: window is empty")
	ErrInvalidCapacity = errors.New("sliding window: capacity must be greater than 0")
	ErrInvalidDuration = errors.New("sliding window: duration must be greater than 0")
	ErrWindowFull      = errors.New("sliding window: window is at maximum capacity")
)

// WindowType defines the type of sliding window
type WindowType int

const (
	WindowTypeCount WindowType = iota // Count-based sliding window
	WindowTypeTime                    // Time-based sliding window
)

// SlidingWindowConfig defines configuration for sliding windows
type SlidingWindowConfig struct {
	Type        WindowType    // Type of window (count or time-based)
	Capacity    int           // Maximum number of elements (for count-based)
	Duration    time.Duration // Time window duration (for time-based)
	InitialSize int           // Initial buffer size for optimization
	EnableStats bool          // Whether to maintain running statistics
	EnableAsync bool          // Whether to run cleanup asynchronously
	CleanupTick time.Duration // How often to cleanup old entries (time-based only)
}

// DefaultSlidingWindowConfig provides sensible defaults for load testing scenarios
func DefaultSlidingWindowConfig(windowType WindowType) SlidingWindowConfig {
	switch windowType {
	case WindowTypeCount:
		return SlidingWindowConfig{
			Type:        WindowTypeCount,
			Capacity:    1000,
			InitialSize: 100,
			EnableStats: true,
			EnableAsync: false,
		}
	case WindowTypeTime:
		return SlidingWindowConfig{
			Type:        WindowTypeTime,
			Duration:    time.Minute * 5, // 5 minute window
			InitialSize: 100,
			EnableStats: true,
			EnableAsync: true,
			CleanupTick: time.Second * 10, // Cleanup every 10 seconds
		}
	default:
		return SlidingWindowConfig{
			Type:        WindowTypeCount,
			Capacity:    1000,
			InitialSize: 100,
			EnableStats: true,
			EnableAsync: false,
		}
	}
}

// WindowElement represents a single element in the sliding window
type WindowElement struct {
	Value     float64   // The metric value
	Timestamp time.Time // When the value was added
}

// CircularBuffer implements an efficient circular buffer for count-based windows
type CircularBuffer struct {
	buffer   []WindowElement
	capacity int
	head     int // Position of the oldest element
	tail     int // Position for the next insert
	count    int // Current number of elements
	mu       sync.RWMutex
}

// NewCircularBuffer creates a new circular buffer with the specified capacity
func NewCircularBuffer(capacity int) (*CircularBuffer, error) {
	if capacity <= 0 {
		return nil, ErrInvalidCapacity
	}

	return &CircularBuffer{
		buffer:   make([]WindowElement, capacity),
		capacity: capacity,
		head:     0,
		tail:     0,
		count:    0,
	}, nil
}

// Add inserts a new element into the circular buffer
func (cb *CircularBuffer) Add(element WindowElement) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.buffer[cb.tail] = element

	if cb.count == cb.capacity {
		// Buffer is full, move head (overwrite oldest)
		cb.head = (cb.head + 1) % cb.capacity
	} else {
		cb.count++
	}

	cb.tail = (cb.tail + 1) % cb.capacity
}

// GetAll returns all elements in the buffer in chronological order
func (cb *CircularBuffer) GetAll() []WindowElement {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	if cb.count == 0 {
		return nil
	}

	result := make([]WindowElement, cb.count)
	for i := 0; i < cb.count; i++ {
		result[i] = cb.buffer[(cb.head+i)%cb.capacity]
	}

	return result
}

// Size returns the current number of elements in the buffer
func (cb *CircularBuffer) Size() int {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.count
}

// Capacity returns the maximum capacity of the buffer
func (cb *CircularBuffer) Capacity() int {
	return cb.capacity
}

// Clear removes all elements from the buffer
func (cb *CircularBuffer) Clear() {
	cb.mu.Lock()
	defer cb.mu.Unlock()
	cb.head = 0
	cb.tail = 0
	cb.count = 0
}

// TimeBasedWindow implements a time-based sliding window using a slice
type TimeBasedWindow struct {
	elements    []WindowElement
	duration    time.Duration
	mu          sync.RWMutex
	stopCh      chan struct{}
	cleanupTick time.Duration
	running     int32
}

// NewTimeBasedWindow creates a new time-based sliding window
func NewTimeBasedWindow(duration time.Duration, initialSize int, cleanupTick time.Duration) (*TimeBasedWindow, error) {
	if duration <= 0 {
		return nil, ErrInvalidDuration
	}

	if initialSize <= 0 {
		initialSize = 100
	}

	if cleanupTick <= 0 {
		cleanupTick = time.Second * 10
	}

	return &TimeBasedWindow{
		elements:    make([]WindowElement, 0, initialSize),
		duration:    duration,
		stopCh:      make(chan struct{}),
		cleanupTick: cleanupTick,
		running:     0,
	}, nil
}

// Add inserts a new element into the time-based window
func (tw *TimeBasedWindow) Add(element WindowElement) {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	tw.elements = append(tw.elements, element)
	// Don't evict here - let GetAll() handle eviction for consistency
	// This prevents timing issues between Add() and GetAll() calls
}

// evictOld removes elements older than the window duration
// Must be called with write lock held
func (tw *TimeBasedWindow) evictOld(currentTime time.Time) {
	cutoff := currentTime.Add(-tw.duration)

	// Find the first element within the window
	// Elements at or after cutoff should be kept
	start := 0
	for start < len(tw.elements) && tw.elements[start].Timestamp.Before(cutoff) {
		start++
	}

	// Keep only elements within the window
	if start > 0 {
		// Shift elements to beginning of slice to avoid reallocation
		copy(tw.elements, tw.elements[start:])
		tw.elements = tw.elements[:len(tw.elements)-start]
	}
}

// GetAll returns all elements in the window within the time duration
func (tw *TimeBasedWindow) GetAll() []WindowElement {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	// If async cleanup is running, use real-time eviction for consistency
	// Otherwise, use element timestamp-based eviction for test predictability
	if atomic.LoadInt32(&tw.running) == 1 {
		tw.evictOld(time.Now())
	} else if len(tw.elements) > 0 {
		// Use the latest element timestamp as reference for deterministic testing
		latestTime := tw.elements[0].Timestamp
		for _, elem := range tw.elements {
			if elem.Timestamp.After(latestTime) {
				latestTime = elem.Timestamp
			}
		}
		tw.evictOld(latestTime)
	}

	// Return a copy to avoid external modifications
	result := make([]WindowElement, len(tw.elements))
	copy(result, tw.elements)
	return result
}

// Size returns the current number of elements in the window
func (tw *TimeBasedWindow) Size() int {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	// Use the latest element timestamp as reference for eviction
	if len(tw.elements) > 0 {
		latestTime := tw.elements[0].Timestamp
		for _, elem := range tw.elements {
			if elem.Timestamp.After(latestTime) {
				latestTime = elem.Timestamp
			}
		}
		tw.evictOld(latestTime)
	}

	return len(tw.elements)
}

// Duration returns the time duration of the window
func (tw *TimeBasedWindow) Duration() time.Duration {
	return tw.duration
}

// StartAsyncCleanup starts asynchronous cleanup of old elements
func (tw *TimeBasedWindow) StartAsyncCleanup() {
	if !atomic.CompareAndSwapInt32(&tw.running, 0, 1) {
		return // Already running
	}

	go tw.cleanupLoop()
}

// StopAsyncCleanup stops the asynchronous cleanup
func (tw *TimeBasedWindow) StopAsyncCleanup() {
	if atomic.CompareAndSwapInt32(&tw.running, 1, 0) {
		close(tw.stopCh)
	}
}

// cleanupLoop runs periodic cleanup of old elements
func (tw *TimeBasedWindow) cleanupLoop() {
	ticker := time.NewTicker(tw.cleanupTick)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			tw.mu.Lock()
			// For async cleanup, use current time as it's a background process
			tw.evictOld(time.Now())
			tw.mu.Unlock()
		case <-tw.stopCh:
			return
		}
	}
}

// Clear removes all elements from the window
func (tw *TimeBasedWindow) Clear() {
	tw.mu.Lock()
	defer tw.mu.Unlock()
	tw.elements = tw.elements[:0] // Keep capacity
}

// RunningStat maintains running statistics for efficient computation
type RunningStat struct {
	count uint64
	sum   uint64 // Stored as uint64 for atomic operations
	min   uint64 // Stored as uint64 for atomic operations
	max   uint64 // Stored as uint64 for atomic operations
}

// NewRunningStat creates a new running statistics tracker
func NewRunningStat() *RunningStat {
	return &RunningStat{
		count: 0,
		sum:   0,
		min:   math.Float64bits(math.Inf(1)),  // Initialize to +Inf
		max:   math.Float64bits(math.Inf(-1)), // Initialize to -Inf
	}
}

// Update updates the running statistics with a new value
func (rs *RunningStat) Update(value float64) {
	if math.IsNaN(value) || math.IsInf(value, 0) {
		return // Ignore invalid values
	}

	atomic.AddUint64(&rs.count, 1)

	// Update sum
	valueBits := math.Float64bits(value)
	for {
		oldSumBits := atomic.LoadUint64(&rs.sum)
		oldSum := math.Float64frombits(oldSumBits)
		newSum := oldSum + value
		newSumBits := math.Float64bits(newSum)

		if atomic.CompareAndSwapUint64(&rs.sum, oldSumBits, newSumBits) {
			break
		}
	}

	// Update min
	for {
		oldMinBits := atomic.LoadUint64(&rs.min)
		oldMin := math.Float64frombits(oldMinBits)
		if value >= oldMin {
			break // Current min is smaller
		}
		if atomic.CompareAndSwapUint64(&rs.min, oldMinBits, valueBits) {
			break
		}
	}

	// Update max
	for {
		oldMaxBits := atomic.LoadUint64(&rs.max)
		oldMax := math.Float64frombits(oldMaxBits)
		if value <= oldMax {
			break // Current max is larger
		}
		if atomic.CompareAndSwapUint64(&rs.max, oldMaxBits, valueBits) {
			break
		}
	}
}

// Reset resets all statistics
func (rs *RunningStat) Reset() {
	atomic.StoreUint64(&rs.count, 0)
	atomic.StoreUint64(&rs.sum, 0)
	atomic.StoreUint64(&rs.min, math.Float64bits(math.Inf(1)))
	atomic.StoreUint64(&rs.max, math.Float64bits(math.Inf(-1)))
}

// Count returns the current count
func (rs *RunningStat) Count() uint64 {
	return atomic.LoadUint64(&rs.count)
}

// Sum returns the current sum
func (rs *RunningStat) Sum() float64 {
	return math.Float64frombits(atomic.LoadUint64(&rs.sum))
}

// Min returns the current minimum value
func (rs *RunningStat) Min() float64 {
	min := math.Float64frombits(atomic.LoadUint64(&rs.min))
	if math.IsInf(min, 1) { // If still +Inf, no values recorded
		return math.NaN()
	}
	return min
}

// Max returns the current maximum value
func (rs *RunningStat) Max() float64 {
	max := math.Float64frombits(atomic.LoadUint64(&rs.max))
	if math.IsInf(max, -1) { // If still -Inf, no values recorded
		return math.NaN()
	}
	return max
}

// Mean returns the current mean value
func (rs *RunningStat) Mean() float64 {
	count := rs.Count()
	if count == 0 {
		return math.NaN()
	}
	return rs.Sum() / float64(count)
}

// SlidingWindow provides a unified interface for both count-based and time-based sliding windows
type SlidingWindow struct {
	config      SlidingWindowConfig
	countBuffer *CircularBuffer
	timeWindow  *TimeBasedWindow
	runningStat *RunningStat
	mu          sync.RWMutex
	percentCalc PercentileCalculator // From our percentiles.go implementation
}

// NewSlidingWindow creates a new sliding window with the specified configuration
func NewSlidingWindow(config SlidingWindowConfig) (*SlidingWindow, error) {
	sw := &SlidingWindow{
		config: config,
	}

	switch config.Type {
	case WindowTypeCount:
		if config.Capacity <= 0 {
			return nil, ErrInvalidCapacity
		}
		buffer, err := NewCircularBuffer(config.Capacity)
		if err != nil {
			return nil, err
		}
		sw.countBuffer = buffer

	case WindowTypeTime:
		if config.Duration <= 0 {
			return nil, ErrInvalidDuration
		}
		window, err := NewTimeBasedWindow(config.Duration, config.InitialSize, config.CleanupTick)
		if err != nil {
			return nil, err
		}
		sw.timeWindow = window

		if config.EnableAsync {
			sw.timeWindow.StartAsyncCleanup()
		}

	default:
		return nil, errors.New("sliding window: invalid window type")
	}

	if config.EnableStats {
		sw.runningStat = NewRunningStat()
	}

	// Initialize percentile calculator for advanced aggregations
	percentCalc, err := NewPercentileCalculator(AlgorithmTDigest, 200.0) // Good balance for load testing
	if err != nil {
		return nil, err
	}
	sw.percentCalc = percentCalc

	return sw, nil
}

// Add inserts a new value into the sliding window
func (sw *SlidingWindow) Add(value float64) error {
	if math.IsNaN(value) || math.IsInf(value, 0) {
		return ErrNaNValue
	}

	element := WindowElement{
		Value:     value,
		Timestamp: time.Now(),
	}

	sw.mu.Lock()
	defer sw.mu.Unlock()

	switch sw.config.Type {
	case WindowTypeCount:
		sw.countBuffer.Add(element)
		// For count-based windows, recalculate running stats based on current window
		if sw.config.EnableStats {
			sw.updateRunningStatsFromWindow()
		}
	case WindowTypeTime:
		sw.timeWindow.Add(element)
		// For time-based windows, recalculate running stats based on current window
		if sw.config.EnableStats {
			sw.updateRunningStatsFromWindow()
		}
	}

	// Reset and rebuild percentile calculator with current window values
	percentCalc, err := NewPercentileCalculator(AlgorithmTDigest, 200.0)
	if err != nil {
		return err
	}
	sw.percentCalc = percentCalc

	// Add all current window values to percentile calculator
	elements := sw.getCurrentElements()
	for _, elem := range elements {
		sw.percentCalc.Add(elem.Value)
	}

	return nil
}

// GetValues returns all values currently in the window
func (sw *SlidingWindow) GetValues() []float64 {
	sw.mu.RLock()
	defer sw.mu.RUnlock()

	var elements []WindowElement

	switch sw.config.Type {
	case WindowTypeCount:
		elements = sw.countBuffer.GetAll()
	case WindowTypeTime:
		elements = sw.timeWindow.GetAll()
	}

	values := make([]float64, len(elements))
	for i, elem := range elements {
		values[i] = elem.Value
	}

	return values
}

// getCurrentElements returns the current window elements (internal helper)
func (sw *SlidingWindow) getCurrentElements() []WindowElement {
	switch sw.config.Type {
	case WindowTypeCount:
		return sw.countBuffer.GetAll()
	case WindowTypeTime:
		return sw.timeWindow.GetAll()
	default:
		return nil
	}
}

// updateRunningStatsFromWindow recalculates running stats based on current window
func (sw *SlidingWindow) updateRunningStatsFromWindow() {
	if sw.runningStat == nil {
		return
	}

	// Reset stats
	sw.runningStat.Reset()

	// Recalculate based on current window
	elements := sw.getCurrentElements()
	for _, elem := range elements {
		sw.runningStat.Update(elem.Value)
	}
}

// GetElements returns all elements currently in the window with timestamps
func (sw *SlidingWindow) GetElements() []WindowElement {
	sw.mu.RLock()
	defer sw.mu.RUnlock()

	switch sw.config.Type {
	case WindowTypeCount:
		return sw.countBuffer.GetAll()
	case WindowTypeTime:
		return sw.timeWindow.GetAll()
	default:
		return nil
	}
}

// Size returns the current number of elements in the window
func (sw *SlidingWindow) Size() int {
	sw.mu.RLock()
	defer sw.mu.RUnlock()

	switch sw.config.Type {
	case WindowTypeCount:
		return sw.countBuffer.Size()
	case WindowTypeTime:
		return sw.timeWindow.Size()
	default:
		return 0
	}
}

// IsEmpty returns true if the window contains no elements
func (sw *SlidingWindow) IsEmpty() bool {
	return sw.Size() == 0
}

// Clear removes all elements from the window
func (sw *SlidingWindow) Clear() {
	sw.mu.Lock()
	defer sw.mu.Unlock()

	switch sw.config.Type {
	case WindowTypeCount:
		sw.countBuffer.Clear()
	case WindowTypeTime:
		sw.timeWindow.Clear()
	}

	if sw.config.EnableStats {
		sw.runningStat.Reset()
	}

	sw.percentCalc.Reset()
}

// Close properly closes the sliding window and stops any background processes
func (sw *SlidingWindow) Close() {
	if sw.config.Type == WindowTypeTime && sw.config.EnableAsync {
		sw.timeWindow.StopAsyncCleanup()
	}
}

// Aggregation methods that leverage both running stats and full computation

// Count returns the number of elements in the window
func (sw *SlidingWindow) Count() uint64 {
	if sw.config.EnableStats {
		return sw.runningStat.Count()
	}
	return uint64(sw.Size())
}

// Sum returns the sum of all values in the window
func (sw *SlidingWindow) Sum() float64 {
	if sw.config.EnableStats {
		return sw.runningStat.Sum()
	}

	values := sw.GetValues()
	if len(values) == 0 {
		return math.NaN()
	}

	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum
}

// Mean returns the mean of all values in the window
func (sw *SlidingWindow) Mean() float64 {
	if sw.config.EnableStats {
		return sw.runningStat.Mean()
	}

	values := sw.GetValues()
	if len(values) == 0 {
		return math.NaN()
	}

	mean, err := Mean(values)
	if err != nil {
		return math.NaN()
	}
	return mean
}

// Min returns the minimum value in the window
func (sw *SlidingWindow) Min() float64 {
	if sw.config.EnableStats {
		return sw.runningStat.Min()
	}

	values := sw.GetValues()
	if len(values) == 0 {
		return math.NaN()
	}

	min, err := Min(values)
	if err != nil {
		return math.NaN()
	}
	return min
}

// Max returns the maximum value in the window
func (sw *SlidingWindow) Max() float64 {
	if sw.config.EnableStats {
		return sw.runningStat.Max()
	}

	values := sw.GetValues()
	if len(values) == 0 {
		return math.NaN()
	}

	max, err := Max(values)
	if err != nil {
		return math.NaN()
	}
	return max
}

// Median returns the median value in the window
func (sw *SlidingWindow) Median() float64 {
	values := sw.GetValues()
	if len(values) == 0 {
		return math.NaN()
	}

	median, err := Median(values)
	if err != nil {
		return math.NaN()
	}
	return median
}

// Percentile returns the specified percentile of values in the window
func (sw *SlidingWindow) Percentile(p float64) float64 {
	if p < 0 || p > 1 {
		return math.NaN()
	}

	// Get current window values and calculate percentile directly
	values := sw.GetValues()
	if len(values) == 0 {
		return math.NaN()
	}

	// Use our basic percentile calculation for reliability
	percentile, err := Percentile(values, p)
	if err != nil {
		return math.NaN()
	}
	return percentile
}

// Variance returns the population variance of values in the window
func (sw *SlidingWindow) Variance() float64 {
	values := sw.GetValues()
	if len(values) == 0 {
		return math.NaN()
	}

	variance, err := Variance(values)
	if err != nil {
		return math.NaN()
	}
	return variance
}

// StandardDeviation returns the population standard deviation of values in the window
func (sw *SlidingWindow) StandardDeviation() float64 {
	values := sw.GetValues()
	if len(values) == 0 {
		return math.NaN()
	}

	stdDev, err := StandardDeviation(values)
	if err != nil {
		return math.NaN()
	}
	return stdDev
}

// Rate returns the rate of additions per unit time (time-based windows only)
func (sw *SlidingWindow) Rate() float64 {
	if sw.config.Type != WindowTypeTime {
		return math.NaN()
	}

	count := float64(sw.Count())
	duration := sw.config.Duration.Seconds()
	return count / duration
}

// SlidingWindowStatistics provides a comprehensive summary of window statistics
type SlidingWindowStatistics struct {
	Count             uint64    `json:"count"`
	Sum               float64   `json:"sum"`
	Mean              float64   `json:"mean"`
	Median            float64   `json:"median"`
	Min               float64   `json:"min"`
	Max               float64   `json:"max"`
	Variance          float64   `json:"variance"`
	StandardDeviation float64   `json:"standard_deviation"`
	P50               float64   `json:"p50"`
	P95               float64   `json:"p95"`
	P99               float64   `json:"p99"`
	P999              float64   `json:"p999"`
	Rate              float64   `json:"rate,omitempty"` // Only for time-based windows
	WindowType        string    `json:"window_type"`
	WindowCapacity    int       `json:"window_capacity,omitempty"` // For count-based
	WindowDuration    string    `json:"window_duration,omitempty"` // For time-based
	Timestamp         time.Time `json:"timestamp"`
}

// GetStatistics returns comprehensive statistics for the current window
func (sw *SlidingWindow) GetStatistics() SlidingWindowStatistics {
	stats := SlidingWindowStatistics{
		Count:             sw.Count(),
		Sum:               sw.Sum(),
		Mean:              sw.Mean(),
		Median:            sw.Median(),
		Min:               sw.Min(),
		Max:               sw.Max(),
		Variance:          sw.Variance(),
		StandardDeviation: sw.StandardDeviation(),
		P50:               sw.Percentile(0.50),
		P95:               sw.Percentile(0.95),
		P99:               sw.Percentile(0.99),
		P999:              sw.Percentile(0.999),
		Timestamp:         time.Now(),
	}

	switch sw.config.Type {
	case WindowTypeCount:
		stats.WindowType = "count"
		stats.WindowCapacity = sw.config.Capacity
	case WindowTypeTime:
		stats.WindowType = "time"
		stats.WindowDuration = sw.config.Duration.String()
		stats.Rate = sw.Rate()
	}

	return stats
}

// MemoryUsage returns the approximate memory usage of the sliding window in bytes
func (sw *SlidingWindow) MemoryUsage() int {
	baseSize := int(unsafe.Sizeof(*sw))

	switch sw.config.Type {
	case WindowTypeCount:
		if sw.countBuffer != nil {
			elementSize := int(unsafe.Sizeof(WindowElement{}))
			return baseSize + (sw.countBuffer.Capacity() * elementSize)
		}
	case WindowTypeTime:
		if sw.timeWindow != nil {
			elementSize := int(unsafe.Sizeof(WindowElement{}))
			return baseSize + (cap(sw.timeWindow.elements) * elementSize)
		}
	}

	return baseSize
}
