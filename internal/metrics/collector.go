// Package metrics provides metrics collection and reporting functionality
package metrics

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// CollectionEvent represents a single metric collection event
type CollectionEvent struct {
	Timestamp  time.Time         `json:"timestamp"`
	MetricName string            `json:"metric_name"`
	MetricType string            `json:"metric_type"` // "counter", "gauge", "histogram"
	Value      interface{}       `json:"value"`       // Counter: int64, Gauge: float64, Histogram: []float64
	Tags       map[string]string `json:"tags"`
	WorkerID   int               `json:"worker_id"`
}

// CollectionConfig defines configuration for background collection
type CollectionConfig struct {
	WorkerCount     int           `json:"worker_count"`     // Number of collection workers
	CollectionRate  time.Duration `json:"collection_rate"`  // How often each worker collects
	ChannelBuffer   int           `json:"channel_buffer"`   // Size of collection event channels
	BatchSize       int           `json:"batch_size"`       // Number of events to batch before processing
	EnableBatching  bool          `json:"enable_batching"`  // Whether to batch collection events
	MaxRetries      int           `json:"max_retries"`      // Maximum retries for failed collections
	RetryDelay      time.Duration `json:"retry_delay"`      // Delay between retries
	EnableMetrics   bool          `json:"enable_metrics"`   // Whether to collect metrics about collection
	ShutdownTimeout time.Duration `json:"shutdown_timeout"` // Maximum time to wait for graceful shutdown
}

// DefaultCollectionConfig provides sensible defaults for load testing scenarios
var DefaultCollectionConfig = CollectionConfig{
	WorkerCount:     runtime.NumCPU(),       // One worker per CPU core
	CollectionRate:  time.Millisecond * 100, // Collect every 100ms
	ChannelBuffer:   1000,                   // Buffer 1000 events
	BatchSize:       50,                     // Process 50 events at a time
	EnableBatching:  true,                   // Enable batching for efficiency
	MaxRetries:      3,                      // Retry failed collections 3 times
	RetryDelay:      time.Millisecond * 10,  // 10ms between retries
	EnableMetrics:   true,                   // Collect collection metrics
	ShutdownTimeout: time.Second * 5,        // 5 second shutdown timeout
}

// CollectionStats tracks statistics about the collection process
type CollectionStats struct {
	TotalEvents      uint64 `json:"total_events"`
	ProcessedEvents  uint64 `json:"processed_events"`
	FailedEvents     uint64 `json:"failed_events"`
	RetryAttempts    uint64 `json:"retry_attempts"`
	ActiveWorkers    int32  `json:"active_workers"`
	QueuedEvents     int32  `json:"queued_events"`
	AverageLatency   int64  `json:"average_latency_ns"`
	MaxLatency       int64  `json:"max_latency_ns"`
	CollectionErrors uint64 `json:"collection_errors"`
}

// MetricSource represents a source of metrics that can be collected
type MetricSource struct {
	Name      string            `json:"name"`
	Type      string            `json:"type"` // "counter", "gauge", "histogram"
	Source    interface{}       `json:"-"`    // The actual metric (Counter, Gauge, or Histogram)
	Tags      map[string]string `json:"tags"`
	Enabled   bool              `json:"enabled"`
	LastValue interface{}       `json:"last_value"`
	LastTime  time.Time         `json:"last_time"`
}

// CollectionManager manages background collection goroutines
type CollectionManager struct {
	config     CollectionConfig
	mu         sync.RWMutex
	sources    map[string]*MetricSource // Registered metric sources
	aggregator *MetricAggregator        // Integration with aggregation system
	eventCh    chan CollectionEvent     // Main event channel
	batchCh    chan []CollectionEvent   // Batch processing channel
	workers    []*CollectionWorker      // Collection workers
	processor  *CollectionProcessor     // Event processor
	stats      CollectionStats          // Collection statistics
	isRunning  int32                    // Atomic flag for running state
	ctx        context.Context          // Context for cancellation
	cancel     context.CancelFunc       // Cancel function
	wg         sync.WaitGroup           // WaitGroup for worker coordination
	startTime  time.Time                // When collection started
}

// CollectionWorker represents a single background collection worker
type CollectionWorker struct {
	id       int
	manager  *CollectionManager
	ticker   *time.Ticker
	ctx      context.Context
	isActive int32 // Atomic flag for active state
}

// CollectionProcessor handles processing of collection events
type CollectionProcessor struct {
	manager  *CollectionManager
	ctx      context.Context
	isActive int32 // Atomic flag for active state
}

// NewCollectionManager creates a new collection manager with default configuration
func NewCollectionManager() *CollectionManager {
	return NewCollectionManagerWithConfig(DefaultCollectionConfig)
}

// NewCollectionManagerWithConfig creates a new collection manager with custom configuration
func NewCollectionManagerWithConfig(config CollectionConfig) *CollectionManager {
	ctx, cancel := context.WithCancel(context.Background())

	eventChSize := config.ChannelBuffer
	batchChSize := config.ChannelBuffer / config.BatchSize
	if batchChSize < 10 {
		batchChSize = 10 // Minimum batch channel size
	}

	cm := &CollectionManager{
		config:    config,
		sources:   make(map[string]*MetricSource),
		eventCh:   make(chan CollectionEvent, eventChSize),
		batchCh:   make(chan []CollectionEvent, batchChSize),
		workers:   make([]*CollectionWorker, config.WorkerCount),
		ctx:       ctx,
		cancel:    cancel,
		startTime: time.Now(),
	}

	return cm
}

// SetAggregator sets the metric aggregator for integration
func (cm *CollectionManager) SetAggregator(aggregator *MetricAggregator) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.aggregator = aggregator
}

// RegisterCounter registers a counter for background collection
func (cm *CollectionManager) RegisterCounter(name string, counter *Counter, tags map[string]string) {
	cm.registerSource(name, "counter", counter, tags)
}

// RegisterGauge registers a gauge for background collection
func (cm *CollectionManager) RegisterGauge(name string, gauge *Gauge, tags map[string]string) {
	cm.registerSource(name, "gauge", gauge, tags)
}

// RegisterHistogram registers a histogram for background collection
func (cm *CollectionManager) RegisterHistogram(name string, histogram *Histogram, tags map[string]string) {
	cm.registerSource(name, "histogram", histogram, tags)
}

// registerSource is a helper method to register any metric source
func (cm *CollectionManager) registerSource(name, metricType string, source interface{}, tags map[string]string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if tags == nil {
		tags = make(map[string]string)
	}

	cm.sources[name] = &MetricSource{
		Name:    name,
		Type:    metricType,
		Source:  source,
		Tags:    tags,
		Enabled: true,
	}
}

// UnregisterSource removes a metric source from collection
func (cm *CollectionManager) UnregisterSource(name string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	delete(cm.sources, name)
}

// EnableSource enables collection for a specific metric source
func (cm *CollectionManager) EnableSource(name string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	if source, exists := cm.sources[name]; exists {
		source.Enabled = true
	}
}

// DisableSource disables collection for a specific metric source
func (cm *CollectionManager) DisableSource(name string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	if source, exists := cm.sources[name]; exists {
		source.Enabled = false
	}
}

// Start begins the background collection process
func (cm *CollectionManager) Start() error {
	if !atomic.CompareAndSwapInt32(&cm.isRunning, 0, 1) {
		return nil // Already running, not an error
	}

	cm.startTime = time.Now()

	// Reset context if it was cancelled
	if cm.ctx.Err() != nil {
		cm.ctx, cm.cancel = context.WithCancel(context.Background())
	}

	// Start the event processor
	cm.processor = &CollectionProcessor{
		manager: cm,
		ctx:     cm.ctx,
	}
	cm.wg.Add(1)
	go cm.processor.run()

	// Start collection workers
	for i := 0; i < cm.config.WorkerCount; i++ {
		worker := &CollectionWorker{
			id:      i,
			manager: cm,
			ticker:  time.NewTicker(cm.config.CollectionRate),
			ctx:     cm.ctx,
		}
		cm.workers[i] = worker
		cm.wg.Add(1)
		go worker.run()
	}

	return nil
}

// Stop stops the background collection process
func (cm *CollectionManager) Stop() error {
	if !atomic.CompareAndSwapInt32(&cm.isRunning, 1, 0) {
		return nil // Already stopped, not an error
	}

	// Cancel context to signal all workers to stop
	cm.cancel()

	// Stop worker tickers first to prevent new events
	for _, worker := range cm.workers {
		if worker != nil && worker.ticker != nil {
			worker.ticker.Stop()
		}
	}

	// Wait for all workers to finish with timeout
	done := make(chan struct{})
	go func() {
		cm.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// All workers finished gracefully
	case <-time.After(cm.config.ShutdownTimeout):
		// Timeout occurred, workers may still be running
		return fmt.Errorf("shutdown timeout exceeded")
	}

	// Close channels after workers are done
	if cm.eventCh != nil {
		close(cm.eventCh)
		cm.eventCh = nil
	}
	if cm.batchCh != nil {
		close(cm.batchCh)
		cm.batchCh = nil
	}

	return nil
}

// IsRunning returns whether the collection manager is currently running
func (cm *CollectionManager) IsRunning() bool {
	return atomic.LoadInt32(&cm.isRunning) == 1
}

// GetStats returns current collection statistics
func (cm *CollectionManager) GetStats() CollectionStats {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	stats := cm.stats
	if cm.eventCh != nil {
		stats.QueuedEvents = int32(len(cm.eventCh))
	}
	stats.ActiveWorkers = 0

	if cm.workers != nil {
		for _, worker := range cm.workers {
			if worker != nil && atomic.LoadInt32(&worker.isActive) == 1 {
				stats.ActiveWorkers++
			}
		}
	}

	return stats
}

// GetSources returns a copy of all registered metric sources
func (cm *CollectionManager) GetSources() map[string]MetricSource {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	sources := make(map[string]MetricSource)
	for name, source := range cm.sources {
		sources[name] = *source // Copy the source
	}
	return sources
}

// UpdateConfig updates the collection configuration
func (cm *CollectionManager) UpdateConfig(config CollectionConfig) error {
	if cm.IsRunning() {
		return fmt.Errorf("cannot update configuration while collection is running")
	}

	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.config = config
	return nil
}

// GetConfig returns the current collection configuration
func (cm *CollectionManager) GetConfig() CollectionConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.config
}

// run is the main worker loop for CollectionWorker
func (cw *CollectionWorker) run() {
	defer cw.manager.wg.Done()
	atomic.StoreInt32(&cw.isActive, 1)
	defer atomic.StoreInt32(&cw.isActive, 0)

	for {
		select {
		case <-cw.ticker.C:
			cw.collectMetrics()
		case <-cw.ctx.Done():
			return
		}
	}
}

// collectMetrics collects metrics from all registered sources
func (cw *CollectionWorker) collectMetrics() {
	cw.manager.mu.RLock()
	sources := make([]*MetricSource, 0, len(cw.manager.sources))
	for _, source := range cw.manager.sources {
		if source.Enabled {
			sources = append(sources, source)
		}
	}
	cw.manager.mu.RUnlock()

	timestamp := time.Now()

	for _, source := range sources {
		event := cw.collectFromSource(source, timestamp)
		if event != nil {
			select {
			case cw.manager.eventCh <- *event:
				atomic.AddUint64(&cw.manager.stats.TotalEvents, 1)
			default:
				// Channel is full, drop the event and record error
				atomic.AddUint64(&cw.manager.stats.CollectionErrors, 1)
			}
		}
	}
}

// collectFromSource collects a metric value from a specific source
func (cw *CollectionWorker) collectFromSource(source *MetricSource, timestamp time.Time) *CollectionEvent {
	var value interface{}

	switch source.Type {
	case "counter":
		if counter, ok := source.Source.(*Counter); ok {
			value = counter.Value()
		}
	case "gauge":
		if gauge, ok := source.Source.(*Gauge); ok {
			value = gauge.Value()
		}
	case "histogram":
		if histogram, ok := source.Source.(*Histogram); ok {
			// For histogram, we collect summary statistics
			value = map[string]interface{}{
				"count": histogram.Count(),
				"sum":   histogram.Sum(),
				"p50":   histogram.Quantile(0.5),
				"p95":   histogram.Quantile(0.95),
				"p99":   histogram.Quantile(0.99),
			}
		}
	default:
		return nil
	}

	if value == nil {
		return nil
	}

	// Update source's last value and time
	source.LastValue = value
	source.LastTime = timestamp

	return &CollectionEvent{
		Timestamp:  timestamp,
		MetricName: source.Name,
		MetricType: source.Type,
		Value:      value,
		Tags:       source.Tags,
		WorkerID:   cw.id,
	}
}

// run is the main processor loop for CollectionProcessor
func (cp *CollectionProcessor) run() {
	defer cp.manager.wg.Done()
	atomic.StoreInt32(&cp.isActive, 1)
	defer atomic.StoreInt32(&cp.isActive, 0)

	if cp.manager.config.EnableBatching {
		cp.runBatchProcessor()
	} else {
		cp.runEventProcessor()
	}
}

// runEventProcessor processes events one by one
func (cp *CollectionProcessor) runEventProcessor() {
	for {
		select {
		case event, ok := <-cp.manager.eventCh:
			if !ok {
				return // Channel closed
			}
			cp.processEvent(event)
		case <-cp.ctx.Done():
			return
		}
	}
}

// runBatchProcessor processes events in batches
func (cp *CollectionProcessor) runBatchProcessor() {
	batch := make([]CollectionEvent, 0, cp.manager.config.BatchSize)
	ticker := time.NewTicker(cp.manager.config.CollectionRate)
	defer ticker.Stop()

	for {
		select {
		case event, ok := <-cp.manager.eventCh:
			if !ok {
				if len(batch) > 0 {
					cp.processBatch(batch)
				}
				return // Channel closed
			}
			batch = append(batch, event)
			if len(batch) >= cp.manager.config.BatchSize {
				cp.processBatch(batch)
				batch = batch[:0] // Reset batch
			}
		case <-ticker.C:
			if len(batch) > 0 {
				cp.processBatch(batch)
				batch = batch[:0] // Reset batch
			}
		case <-cp.ctx.Done():
			if len(batch) > 0 {
				cp.processBatch(batch)
			}
			return
		}
	}
}

// processEvent processes a single collection event
func (cp *CollectionProcessor) processEvent(event CollectionEvent) {
	startTime := time.Now()

	// Process the event (integrate with aggregator if available)
	if cp.manager.aggregator != nil {
		cp.integrateWithAggregator(event)
	}

	// Update statistics
	atomic.AddUint64(&cp.manager.stats.ProcessedEvents, 1)

	// Update latency statistics
	latency := time.Since(startTime).Nanoseconds()
	atomic.StoreInt64(&cp.manager.stats.AverageLatency, latency)

	maxLatency := atomic.LoadInt64(&cp.manager.stats.MaxLatency)
	if latency > maxLatency {
		atomic.CompareAndSwapInt64(&cp.manager.stats.MaxLatency, maxLatency, latency)
	}
}

// processBatch processes a batch of collection events
func (cp *CollectionProcessor) processBatch(batch []CollectionEvent) {
	for _, event := range batch {
		cp.processEvent(event)
	}
}

// integrateWithAggregator integrates collection events with the aggregation system
func (cp *CollectionProcessor) integrateWithAggregator(event CollectionEvent) {
	// This method provides integration with the MetricAggregator from Task 38.1
	// The aggregator will handle the actual aggregation logic

	switch event.MetricType {
	case "counter":
		if counter, ok := event.Value.(int64); ok {
			// Counter values are already collected, aggregator will handle aggregation
			_ = counter // Use the value as needed
		}
	case "gauge":
		if gauge, ok := event.Value.(float64); ok {
			// Gauge values are already collected, aggregator will handle aggregation
			_ = gauge // Use the value as needed
		}
	case "histogram":
		if histData, ok := event.Value.(map[string]interface{}); ok {
			// Histogram summary data is already collected, aggregator will handle aggregation
			_ = histData // Use the data as needed
		}
	}

	// The actual integration with MetricAggregator is handled by the aggregator's
	// own collection mechanisms. This collector provides the background collection
	// infrastructure while the aggregator handles the aggregation logic.
}
