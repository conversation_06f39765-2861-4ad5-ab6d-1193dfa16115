package metrics

import (
	"sync"
	"sync/atomic"
	"time"
)

// MemoryPool provides object pooling for frequent allocations
type MemoryPool struct {
	pool      sync.Pool
	size      int
	hits      int64
	misses    int64
	allocated int64
	mu        sync.RWMutex
}

// PoolStats tracks memory pool statistics
type PoolStats struct {
	Size        int     `json:"size"`
	Hits        int64   `json:"hits"`
	Misses      int64   `json:"misses"`
	Allocated   int64   `json:"allocated"`
	Utilization float64 `json:"utilization"`
}

// NewMemoryPool creates a new memory pool
func NewMemoryPool(size int) *MemoryPool {
	return &MemoryPool{
		pool: sync.Pool{
			New: func() interface{} {
				return make([]*StorageEntry, 0, 100) // Pre-allocate slice capacity
			},
		},
		size: size,
	}
}

// Get gets an object from the pool
func (mp *MemoryPool) Get() []*StorageEntry {
	obj := mp.pool.Get().([]*StorageEntry)

	if len(obj) == 0 {
		atomic.AddInt64(&mp.hits, 1)
	} else {
		atomic.AddInt64(&mp.misses, 1)
		// Clear the slice but keep capacity
		obj = obj[:0]
	}

	atomic.AddInt64(&mp.allocated, 1)
	return obj
}

// Put returns an object to the pool
func (mp *MemoryPool) Put(obj []*StorageEntry) {
	if obj != nil {
		// Clear the slice but keep capacity
		obj = obj[:0]
		mp.pool.Put(obj)
		atomic.AddInt64(&mp.allocated, -1)
	}
}

// GetStats returns pool statistics
func (mp *MemoryPool) GetStats() PoolStats {
	hits := atomic.LoadInt64(&mp.hits)
	misses := atomic.LoadInt64(&mp.misses)
	allocated := atomic.LoadInt64(&mp.allocated)

	var utilization float64
	if hits+misses > 0 {
		utilization = float64(hits) / float64(hits+misses)
	}

	return PoolStats{
		Size:        mp.size,
		Hits:        hits,
		Misses:      misses,
		Allocated:   allocated,
		Utilization: utilization,
	}
}

// Cleanup performs pool cleanup operations
func (mp *MemoryPool) Cleanup() {
	// Pool cleanup is handled automatically by sync.Pool
	// This method is provided for interface compatibility
}

// StorageBuffer provides circular buffer management for metric entries
type StorageBuffer struct {
	entries   []*StorageEntry
	size      int
	head      int
	tail      int
	count     int
	mu        sync.RWMutex
	pool      *MemoryPool
	totalSize int64
}

// NewStorageBuffer creates a new storage buffer
func NewStorageBuffer(size int, pool *MemoryPool) *StorageBuffer {
	return &StorageBuffer{
		entries: make([]*StorageEntry, size),
		size:    size,
		pool:    pool,
	}
}

// Add adds an entry to the buffer
func (sb *StorageBuffer) Add(entry *StorageEntry) error {
	sb.mu.Lock()
	defer sb.mu.Unlock()

	// Calculate entry size
	entrySize := sb.estimateEntrySize(entry)

	// If buffer is full, remove oldest entry
	if sb.count == sb.size {
		oldEntry := sb.entries[sb.tail]
		if oldEntry != nil {
			sb.totalSize -= sb.estimateEntrySize(oldEntry)
		}
		sb.tail = (sb.tail + 1) % sb.size
		sb.count--
	}

	// Add new entry
	sb.entries[sb.head] = entry
	sb.head = (sb.head + 1) % sb.size
	sb.count++
	sb.totalSize += entrySize

	return nil
}

// GetAll returns all entries in the buffer
func (sb *StorageBuffer) GetAll() []*StorageEntry {
	sb.mu.RLock()
	defer sb.mu.RUnlock()

	if sb.count == 0 {
		return nil
	}

	// Get slice from pool
	var result []*StorageEntry
	if sb.pool != nil {
		result = sb.pool.Get()
	} else {
		result = make([]*StorageEntry, 0, sb.count)
	}

	// Copy entries
	for i := 0; i < sb.count; i++ {
		idx := (sb.tail + i) % sb.size
		if sb.entries[idx] != nil {
			result = append(result, sb.entries[idx])
		}
	}

	return result
}

// GetLatest returns the latest n entries
func (sb *StorageBuffer) GetLatest(n int) []*StorageEntry {
	sb.mu.RLock()
	defer sb.mu.RUnlock()

	if sb.count == 0 || n <= 0 {
		return nil
	}

	if n > sb.count {
		n = sb.count
	}

	result := make([]*StorageEntry, 0, n)

	// Get latest n entries (starting from head backwards)
	for i := 0; i < n; i++ {
		idx := (sb.head - 1 - i + sb.size) % sb.size
		if sb.entries[idx] != nil {
			result = append(result, sb.entries[idx])
		}
	}

	return result
}

// Cleanup removes expired entries and returns freed memory size
func (sb *StorageBuffer) Cleanup(now time.Time) int64 {
	sb.mu.Lock()
	defer sb.mu.Unlock()

	freedSize := int64(0)
	removedCount := 0

	// Scan all entries and mark expired ones for removal
	for i := 0; i < sb.count; i++ {
		idx := (sb.tail + i) % sb.size
		entry := sb.entries[idx]

		if entry == nil {
			continue
		}

		// Check if entry is expired
		expired := false

		// Check TTL
		if entry.TTL > 0 && now.Sub(entry.Timestamp) > entry.TTL {
			expired = true
		}

		if expired {
			freedSize += sb.estimateEntrySize(entry)
			sb.entries[idx] = nil
			removedCount++
		}
	}

	// Compact buffer if needed
	if removedCount > 0 {
		sb.compactBuffer()
		sb.totalSize -= freedSize
	}

	return freedSize
}

// compactBuffer removes nil entries and compacts the buffer
func (sb *StorageBuffer) compactBuffer() {
	if sb.count == 0 {
		return
	}

	writeIdx := sb.tail
	readIdx := sb.tail
	newCount := 0

	for i := 0; i < sb.count; i++ {
		if sb.entries[readIdx] != nil {
			if writeIdx != readIdx {
				sb.entries[writeIdx] = sb.entries[readIdx]
				sb.entries[readIdx] = nil
			}
			writeIdx = (writeIdx + 1) % sb.size
			newCount++
		}
		readIdx = (readIdx + 1) % sb.size
	}

	sb.count = newCount
	sb.head = (sb.tail + newCount) % sb.size
}

// IsEmpty returns true if the buffer is empty
func (sb *StorageBuffer) IsEmpty() bool {
	sb.mu.RLock()
	defer sb.mu.RUnlock()
	return sb.count == 0
}

// Size returns the current number of entries in the buffer
func (sb *StorageBuffer) Size() int {
	sb.mu.RLock()
	defer sb.mu.RUnlock()
	return sb.count
}

// TotalSize returns the total memory size of entries in the buffer
func (sb *StorageBuffer) TotalSize() int64 {
	sb.mu.RLock()
	defer sb.mu.RUnlock()
	return sb.totalSize
}

// Clear removes all entries from the buffer
func (sb *StorageBuffer) Clear() {
	sb.mu.Lock()
	defer sb.mu.Unlock()

	for i := 0; i < sb.size; i++ {
		sb.entries[i] = nil
	}

	sb.head = 0
	sb.tail = 0
	sb.count = 0
	sb.totalSize = 0
}

// estimateEntrySize estimates the memory size of a storage entry
func (sb *StorageBuffer) estimateEntrySize(entry *StorageEntry) int64 {
	if entry == nil {
		return 0
	}

	size := int64(0)

	// Key size
	size += int64(len(entry.Key))

	// Value size (rough estimation)
	switch v := entry.Value.(type) {
	case string:
		size += int64(len(v))
	case []byte:
		size += int64(len(v))
	case int, int32, int64, float32, float64:
		size += 8
	default:
		// Rough estimate for complex types
		size += 64
	}

	// Tags size
	tagsMap := entry.Tags.ToMap()
	for key, value := range tagsMap {
		size += int64(len(key) + len(value))
	}

	// Timestamp and other fields
	size += 32

	return size
}
