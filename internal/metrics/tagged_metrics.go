// Package metrics provides metrics collection and reporting functionality
package metrics

import (
	"sync"
	"sync/atomic"
)

// TaggedMetric represents a metric with associated tags
type TaggedMetric interface {
	GetTags() *TagSet
	GetName() string
	GetType() string
}

// TaggedCounter is a Counter with associated tags
type TaggedCounter struct {
	*Counter
	name string
	tags *TagSet
}

// NewTaggedCounter creates a new tagged counter
func NewTaggedCounter(name string, tags *TagSet) *TaggedCounter {
	if tags == nil {
		tags = NewTagSet(nil)
	}
	return &TaggedCounter{
		Counter: NewCounter(),
		name:    name,
		tags:    tags,
	}
}

// NewTaggedCounterWithTags creates a new tagged counter from a tag map
func NewTaggedCounterWithTags(name string, tags map[string]string) *TaggedCounter {
	return NewTaggedCounter(name, NewTagSet(tags))
}

// GetTags returns the tags associated with this counter
func (tc *TaggedCounter) GetTags() *TagSet {
	return tc.tags
}

// GetName returns the name of this counter
func (tc *TaggedCounter) GetName() string {
	return tc.name
}

// GetType returns the type of this metric
func (tc *TaggedCounter) GetType() string {
	return "counter"
}

// WithTag returns a new TaggedCounter with an additional tag
func (tc *TaggedCounter) WithTag(key, value string) *TaggedCounter {
	newTags := tc.tags.With(key, value)
	return &TaggedCounter{
		Counter: tc.Counter,
		name:    tc.name,
		tags:    newTags,
	}
}

// TaggedGauge is a Gauge with associated tags
type TaggedGauge struct {
	*Gauge
	name string
	tags *TagSet
}

// NewTaggedGauge creates a new tagged gauge
func NewTaggedGauge(name string, tags *TagSet) *TaggedGauge {
	if tags == nil {
		tags = NewTagSet(nil)
	}
	return &TaggedGauge{
		Gauge: NewGauge(),
		name:  name,
		tags:  tags,
	}
}

// NewTaggedGaugeWithTags creates a new tagged gauge from a tag map
func NewTaggedGaugeWithTags(name string, tags map[string]string) *TaggedGauge {
	return NewTaggedGauge(name, NewTagSet(tags))
}

// GetTags returns the tags associated with this gauge
func (tg *TaggedGauge) GetTags() *TagSet {
	return tg.tags
}

// GetName returns the name of this gauge
func (tg *TaggedGauge) GetName() string {
	return tg.name
}

// GetType returns the type of this metric
func (tg *TaggedGauge) GetType() string {
	return "gauge"
}

// WithTag returns a new TaggedGauge with an additional tag
func (tg *TaggedGauge) WithTag(key, value string) *TaggedGauge {
	newTags := tg.tags.With(key, value)
	return &TaggedGauge{
		Gauge: tg.Gauge,
		name:  tg.name,
		tags:  newTags,
	}
}

// TaggedHistogram is a Histogram with associated tags
type TaggedHistogram struct {
	*Histogram
	name string
	tags *TagSet
}

// NewTaggedHistogram creates a new tagged histogram
func NewTaggedHistogram(name string, tags *TagSet) *TaggedHistogram {
	if tags == nil {
		tags = NewTagSet(nil)
	}
	return &TaggedHistogram{
		Histogram: NewHistogram(),
		name:      name,
		tags:      tags,
	}
}

// NewTaggedHistogramWithTags creates a new tagged histogram from a tag map
func NewTaggedHistogramWithTags(name string, tags map[string]string) *TaggedHistogram {
	return NewTaggedHistogram(name, NewTagSet(tags))
}

// NewTaggedHistogramWithBuckets creates a new tagged histogram with custom buckets
func NewTaggedHistogramWithBuckets(name string, tags *TagSet, buckets []float64) *TaggedHistogram {
	if tags == nil {
		tags = NewTagSet(nil)
	}
	return &TaggedHistogram{
		Histogram: NewHistogramWithBuckets(buckets),
		name:      name,
		tags:      tags,
	}
}

// GetTags returns the tags associated with this histogram
func (th *TaggedHistogram) GetTags() *TagSet {
	return th.tags
}

// GetName returns the name of this histogram
func (th *TaggedHistogram) GetName() string {
	return th.name
}

// GetType returns the type of this metric
func (th *TaggedHistogram) GetType() string {
	return "histogram"
}

// WithTag returns a new TaggedHistogram with an additional tag
func (th *TaggedHistogram) WithTag(key, value string) *TaggedHistogram {
	newTags := th.tags.With(key, value)
	return &TaggedHistogram{
		Histogram: th.Histogram,
		name:      th.name,
		tags:      newTags,
	}
}

// MetricRegistry provides centralized management of tagged metrics
type MetricRegistry struct {
	mu       sync.RWMutex
	counters map[string]*TaggedCounter
	gauges   map[string]*TaggedGauge
	histos   map[string]*TaggedHistogram
	index    *TagIndex
}

// NewMetricRegistry creates a new metric registry
func NewMetricRegistry() *MetricRegistry {
	return &MetricRegistry{
		counters: make(map[string]*TaggedCounter),
		gauges:   make(map[string]*TaggedGauge),
		histos:   make(map[string]*TaggedHistogram),
		index:    NewTagIndex(),
	}
}

// RegisterCounter registers a tagged counter
func (mr *MetricRegistry) RegisterCounter(name string, tags *TagSet) *TaggedCounter {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	counter := NewTaggedCounter(name, tags)
	mr.counters[name] = counter
	mr.index.Register(name, tags, "counter")
	return counter
}

// RegisterCounterWithTags registers a tagged counter from a tag map
func (mr *MetricRegistry) RegisterCounterWithTags(name string, tags map[string]string) *TaggedCounter {
	return mr.RegisterCounter(name, NewTagSet(tags))
}

// RegisterGauge registers a tagged gauge
func (mr *MetricRegistry) RegisterGauge(name string, tags *TagSet) *TaggedGauge {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	gauge := NewTaggedGauge(name, tags)
	mr.gauges[name] = gauge
	mr.index.Register(name, tags, "gauge")
	return gauge
}

// RegisterGaugeWithTags registers a tagged gauge from a tag map
func (mr *MetricRegistry) RegisterGaugeWithTags(name string, tags map[string]string) *TaggedGauge {
	return mr.RegisterGauge(name, NewTagSet(tags))
}

// RegisterHistogram registers a tagged histogram
func (mr *MetricRegistry) RegisterHistogram(name string, tags *TagSet) *TaggedHistogram {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	histogram := NewTaggedHistogram(name, tags)
	mr.histos[name] = histogram
	mr.index.Register(name, tags, "histogram")
	return histogram
}

// RegisterHistogramWithTags registers a tagged histogram from a tag map
func (mr *MetricRegistry) RegisterHistogramWithTags(name string, tags map[string]string) *TaggedHistogram {
	return mr.RegisterHistogram(name, NewTagSet(tags))
}

// RegisterHistogramWithBuckets registers a tagged histogram with custom buckets
func (mr *MetricRegistry) RegisterHistogramWithBuckets(name string, tags *TagSet, buckets []float64) *TaggedHistogram {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	histogram := NewTaggedHistogramWithBuckets(name, tags, buckets)
	mr.histos[name] = histogram
	mr.index.Register(name, tags, "histogram")
	return histogram
}

// GetCounter retrieves a registered counter by name
func (mr *MetricRegistry) GetCounter(name string) *TaggedCounter {
	mr.mu.RLock()
	defer mr.mu.RUnlock()
	return mr.counters[name]
}

// GetGauge retrieves a registered gauge by name
func (mr *MetricRegistry) GetGauge(name string) *TaggedGauge {
	mr.mu.RLock()
	defer mr.mu.RUnlock()
	return mr.gauges[name]
}

// GetHistogram retrieves a registered histogram by name
func (mr *MetricRegistry) GetHistogram(name string) *TaggedHistogram {
	mr.mu.RLock()
	defer mr.mu.RUnlock()
	return mr.histos[name]
}

// UnregisterCounter removes a counter from the registry
func (mr *MetricRegistry) UnregisterCounter(name string) {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	delete(mr.counters, name)
	mr.index.Unregister(name)
}

// UnregisterGauge removes a gauge from the registry
func (mr *MetricRegistry) UnregisterGauge(name string) {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	delete(mr.gauges, name)
	mr.index.Unregister(name)
}

// UnregisterHistogram removes a histogram from the registry
func (mr *MetricRegistry) UnregisterHistogram(name string) {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	delete(mr.histos, name)
	mr.index.Unregister(name)
}

// FindCounters returns counters matching the given filter
func (mr *MetricRegistry) FindCounters(filter *TagFilter) []*TaggedCounter {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	metricNames := mr.index.Find(filter)
	var result []*TaggedCounter
	for _, name := range metricNames {
		if counter, exists := mr.counters[name]; exists {
			result = append(result, counter)
		}
	}
	return result
}

// FindGauges returns gauges matching the given filter
func (mr *MetricRegistry) FindGauges(filter *TagFilter) []*TaggedGauge {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	metricNames := mr.index.Find(filter)
	var result []*TaggedGauge
	for _, name := range metricNames {
		if gauge, exists := mr.gauges[name]; exists {
			result = append(result, gauge)
		}
	}
	return result
}

// FindHistograms returns histograms matching the given filter
func (mr *MetricRegistry) FindHistograms(filter *TagFilter) []*TaggedHistogram {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	metricNames := mr.index.Find(filter)
	var result []*TaggedHistogram
	for _, name := range metricNames {
		if histogram, exists := mr.histos[name]; exists {
			result = append(result, histogram)
		}
	}
	return result
}

// FindMetrics returns all metrics matching the given filter
func (mr *MetricRegistry) FindMetrics(filter *TagFilter) []TaggedMetric {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	metricNames := mr.index.Find(filter)
	var result []TaggedMetric

	for _, name := range metricNames {
		if counter, exists := mr.counters[name]; exists {
			result = append(result, counter)
		} else if gauge, exists := mr.gauges[name]; exists {
			result = append(result, gauge)
		} else if histogram, exists := mr.histos[name]; exists {
			result = append(result, histogram)
		}
	}
	return result
}

// ListAllCounters returns all registered counter names
func (mr *MetricRegistry) ListAllCounters() []string {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	result := make([]string, 0, len(mr.counters))
	for name := range mr.counters {
		result = append(result, name)
	}
	return result
}

// ListAllGauges returns all registered gauge names
func (mr *MetricRegistry) ListAllGauges() []string {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	result := make([]string, 0, len(mr.gauges))
	for name := range mr.gauges {
		result = append(result, name)
	}
	return result
}

// ListAllHistograms returns all registered histogram names
func (mr *MetricRegistry) ListAllHistograms() []string {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	result := make([]string, 0, len(mr.histos))
	for name := range mr.histos {
		result = append(result, name)
	}
	return result
}

// GetIndex returns the underlying tag index
func (mr *MetricRegistry) GetIndex() *TagIndex {
	return mr.index
}

// Stats returns statistics about the registry
type RegistryStats struct {
	CounterCount   int `json:"counter_count"`
	GaugeCount     int `json:"gauge_count"`
	HistogramCount int `json:"histogram_count"`
	TotalMetrics   int `json:"total_metrics"`
}

// GetStats returns statistics about the registry
func (mr *MetricRegistry) GetStats() RegistryStats {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	counterCount := len(mr.counters)
	gaugeCount := len(mr.gauges)
	histogramCount := len(mr.histos)

	return RegistryStats{
		CounterCount:   counterCount,
		GaugeCount:     gaugeCount,
		HistogramCount: histogramCount,
		TotalMetrics:   counterCount + gaugeCount + histogramCount,
	}
}

// Clear removes all metrics from the registry
func (mr *MetricRegistry) Clear() {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	mr.counters = make(map[string]*TaggedCounter)
	mr.gauges = make(map[string]*TaggedGauge)
	mr.histos = make(map[string]*TaggedHistogram)
	mr.index = NewTagIndex()
}

// TaggedCollectionManager extends CollectionManager with tag-aware functionality
type TaggedCollectionManager struct {
	*CollectionManager
	registry *MetricRegistry
	mu       sync.RWMutex
}

// NewTaggedCollectionManager creates a new tagged collection manager
func NewTaggedCollectionManager() *TaggedCollectionManager {
	return &TaggedCollectionManager{
		CollectionManager: NewCollectionManager(),
		registry:          NewMetricRegistry(),
	}
}

// NewTaggedCollectionManagerWithConfig creates a tagged collection manager with custom config
func NewTaggedCollectionManagerWithConfig(config CollectionConfig) *TaggedCollectionManager {
	return &TaggedCollectionManager{
		CollectionManager: NewCollectionManagerWithConfig(config),
		registry:          NewMetricRegistry(),
	}
}

// RegisterTaggedCounter registers a tagged counter for collection
func (tcm *TaggedCollectionManager) RegisterTaggedCounter(name string, counter *TaggedCounter) {
	tcm.mu.Lock()
	defer tcm.mu.Unlock()

	// Register in the collection manager using the base Counter
	tcm.CollectionManager.RegisterCounter(name, counter.Counter, counter.GetTags().ToMap())

	// Also register in our registry
	tcm.registry.counters[name] = counter
	tcm.registry.index.Register(name, counter.GetTags(), "counter")
}

// RegisterTaggedGauge registers a tagged gauge for collection
func (tcm *TaggedCollectionManager) RegisterTaggedGauge(name string, gauge *TaggedGauge) {
	tcm.mu.Lock()
	defer tcm.mu.Unlock()

	// Register in the collection manager using the base Gauge
	tcm.CollectionManager.RegisterGauge(name, gauge.Gauge, gauge.GetTags().ToMap())

	// Also register in our registry
	tcm.registry.gauges[name] = gauge
	tcm.registry.index.Register(name, gauge.GetTags(), "gauge")
}

// RegisterTaggedHistogram registers a tagged histogram for collection
func (tcm *TaggedCollectionManager) RegisterTaggedHistogram(name string, histogram *TaggedHistogram) {
	tcm.mu.Lock()
	defer tcm.mu.Unlock()

	// Register in the collection manager using the base Histogram
	tcm.CollectionManager.RegisterHistogram(name, histogram.Histogram, histogram.GetTags().ToMap())

	// Also register in our registry
	tcm.registry.histos[name] = histogram
	tcm.registry.index.Register(name, histogram.GetTags(), "histogram")
}

// GetRegistry returns the underlying metric registry
func (tcm *TaggedCollectionManager) GetRegistry() *MetricRegistry {
	return tcm.registry
}

// FindAndCollect starts collection for metrics matching the filter
func (tcm *TaggedCollectionManager) FindAndCollect(filter *TagFilter) int {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	metricNames := tcm.registry.index.Find(filter)
	count := 0

	for _, name := range metricNames {
		tcm.CollectionManager.EnableSource(name)
		count++
	}

	return count
}

// FindAndStop stops collection for metrics matching the filter
func (tcm *TaggedCollectionManager) FindAndStop(filter *TagFilter) int {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	metricNames := tcm.registry.index.Find(filter)
	count := 0

	for _, name := range metricNames {
		tcm.CollectionManager.DisableSource(name)
		count++
	}

	return count
}

// GetTaggedStats returns collection statistics grouped by tags
type TaggedCollectionStats struct {
	BaseStats     CollectionStats           `json:"base_stats"`
	RegistryStats RegistryStats             `json:"registry_stats"`
	TagStats      map[string]map[string]int `json:"tag_stats"` // tag key -> tag value -> count
}

// GetTaggedStats returns comprehensive statistics including tag information
func (tcm *TaggedCollectionManager) GetTaggedStats() TaggedCollectionStats {
	tcm.mu.RLock()
	defer tcm.mu.RUnlock()

	baseStats := tcm.CollectionManager.GetStats()
	registryStats := tcm.registry.GetStats()

	// Compute tag statistics
	tagStats := make(map[string]map[string]int)
	allMetrics := tcm.registry.index.ListAll()

	for _, metricName := range allMetrics {
		if tags := tcm.registry.index.GetTags(metricName); tags != nil {
			for key, value := range tags.ToMap() {
				if tagStats[key] == nil {
					tagStats[key] = make(map[string]int)
				}
				tagStats[key][value]++
			}
		}
	}

	return TaggedCollectionStats{
		BaseStats:     baseStats,
		RegistryStats: registryStats,
		TagStats:      tagStats,
	}
}

// Global registry instance for convenience
var DefaultRegistry = NewMetricRegistry()

// Global functions for convenience

// RegisterCounter registers a counter in the default registry
func RegisterCounter(name string, tags map[string]string) *TaggedCounter {
	return DefaultRegistry.RegisterCounterWithTags(name, tags)
}

// RegisterGauge registers a gauge in the default registry
func RegisterGauge(name string, tags map[string]string) *TaggedGauge {
	return DefaultRegistry.RegisterGaugeWithTags(name, tags)
}

// RegisterHistogram registers a histogram in the default registry
func RegisterHistogram(name string, tags map[string]string) *TaggedHistogram {
	return DefaultRegistry.RegisterHistogramWithTags(name, tags)
}

// FindMetrics finds metrics in the default registry
func FindMetrics(filter *TagFilter) []TaggedMetric {
	return DefaultRegistry.FindMetrics(filter)
}

// Counter-specific atomic operations for high-performance scenarios
var globalCounterRegistry = struct {
	mu       sync.RWMutex
	counters map[string]*int64 // For ultra-fast atomic counters
}{}

func init() {
	globalCounterRegistry.counters = make(map[string]*int64)
}

// FastCounter provides ultra-fast atomic counter operations
func FastCounter(name string) *int64 {
	globalCounterRegistry.mu.RLock()
	if counter, exists := globalCounterRegistry.counters[name]; exists {
		globalCounterRegistry.mu.RUnlock()
		return counter
	}
	globalCounterRegistry.mu.RUnlock()

	globalCounterRegistry.mu.Lock()
	defer globalCounterRegistry.mu.Unlock()

	// Double-check after acquiring write lock
	if counter, exists := globalCounterRegistry.counters[name]; exists {
		return counter
	}

	counter := new(int64)
	globalCounterRegistry.counters[name] = counter
	return counter
}

// FastAdd atomically adds to a fast counter
func FastAdd(name string, delta int64) int64 {
	counter := FastCounter(name)
	return atomic.AddInt64(counter, delta)
}

// FastInc atomically increments a fast counter
func FastInc(name string) int64 {
	return FastAdd(name, 1)
}

// FastGet atomically gets the value of a fast counter
func FastGet(name string) int64 {
	counter := FastCounter(name)
	return atomic.LoadInt64(counter)
}
