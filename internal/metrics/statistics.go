// Package metrics provides statistical functions for metrics aggregation
package metrics

import (
	"errors"
	"math"
	"sort"
)

// Statistical errors
var (
	ErrEmptyDataset     = errors.New("statistics: empty dataset")
	ErrInsufficientData = errors.New("statistics: insufficient data for calculation")
	ErrInvalidInput     = errors.New("statistics: invalid input values")
)

// StatisticalSummary represents a complete statistical summary of a dataset
type StatisticalSummary struct {
	Count         int     `json:"count"`
	Sum           float64 `json:"sum"`
	Mean          float64 `json:"mean"`
	Median        float64 `json:"median"`
	Mode          float64 `json:"mode"`
	ModeFrequency int     `json:"mode_frequency"`
	Min           float64 `json:"min"`
	Max           float64 `json:"max"`
	Range         float64 `json:"range"`
	Variance      float64 `json:"variance"`
	StandardDev   float64 `json:"standard_deviation"`
	Skewness      float64 `json:"skewness"`
	Kurtosis      float64 `json:"kurtosis"`
	Q1            float64 `json:"q1"`  // 25th percentile
	Q3            float64 `json:"q3"`  // 75th percentile
	IQR           float64 `json:"iqr"` // Interquartile range
	HasOutliers   bool    `json:"has_outliers"`
	OutlierCount  int     `json:"outlier_count"`
}

// Mean calculates the arithmetic mean of a slice of float64 values
// Returns ErrEmptyDataset if the slice is empty
func Mean(data []float64) (float64, error) {
	if len(data) == 0 {
		return 0, ErrEmptyDataset
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return 0, ErrInvalidInput
	}

	var sum float64
	for _, v := range filtered {
		sum += v
	}

	return sum / float64(len(filtered)), nil
}

// Median calculates the median value of a slice of float64 values
// Returns ErrEmptyDataset if the slice is empty
func Median(data []float64) (float64, error) {
	if len(data) == 0 {
		return 0, ErrEmptyDataset
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return 0, ErrInvalidInput
	}

	// Create a copy to avoid modifying the original slice
	sorted := make([]float64, len(filtered))
	copy(sorted, filtered)
	sort.Float64s(sorted)

	n := len(sorted)
	if n%2 == 0 {
		// Even number of elements: average of middle two
		return (sorted[n/2-1] + sorted[n/2]) / 2, nil
	}
	// Odd number of elements: middle element
	return sorted[n/2], nil
}

// Mode calculates the mode (most frequent value) of a slice of float64 values
// Returns the mode value and its frequency count
// For continuous data, values are grouped by a small tolerance
func Mode(data []float64) (float64, int, error) {
	if len(data) == 0 {
		return 0, 0, ErrEmptyDataset
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return 0, 0, ErrInvalidInput
	}

	// For floating point values, we need to group similar values
	const tolerance = 1e-10
	frequency := make(map[float64]int)

	for _, v := range filtered {
		// Find if there's already a similar value within tolerance
		found := false
		for key := range frequency {
			if math.Abs(v-key) < tolerance {
				frequency[key]++
				found = true
				break
			}
		}
		if !found {
			frequency[v] = 1
		}
	}

	var modeValue float64
	var maxFreq int

	for value, freq := range frequency {
		if freq > maxFreq {
			maxFreq = freq
			modeValue = value
		}
	}

	return modeValue, maxFreq, nil
}

// Min returns the minimum value in a slice of float64 values
// Returns ErrEmptyDataset if the slice is empty
func Min(data []float64) (float64, error) {
	if len(data) == 0 {
		return 0, ErrEmptyDataset
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return 0, ErrInvalidInput
	}

	min := filtered[0]
	for _, v := range filtered[1:] {
		if v < min {
			min = v
		}
	}

	return min, nil
}

// Max returns the maximum value in a slice of float64 values
// Returns ErrEmptyDataset if the slice is empty
func Max(data []float64) (float64, error) {
	if len(data) == 0 {
		return 0, ErrEmptyDataset
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return 0, ErrInvalidInput
	}

	max := filtered[0]
	for _, v := range filtered[1:] {
		if v > max {
			max = v
		}
	}

	return max, nil
}

// Range returns the range (max - min) of a slice of float64 values
func Range(data []float64) (float64, error) {
	if len(data) == 0 {
		return 0, ErrEmptyDataset
	}

	min, err := Min(data)
	if err != nil {
		return 0, err
	}

	max, err := Max(data)
	if err != nil {
		return 0, err
	}

	return max - min, nil
}

// Variance calculates the population variance of a slice of float64 values
// For sample variance, use SampleVariance instead
func Variance(data []float64) (float64, error) {
	if len(data) == 0 {
		return 0, ErrEmptyDataset
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return 0, ErrInvalidInput
	}

	mean, err := Mean(filtered)
	if err != nil {
		return 0, err
	}

	var sum float64
	for _, v := range filtered {
		diff := v - mean
		sum += diff * diff
	}

	return sum / float64(len(filtered)), nil
}

// SampleVariance calculates the sample variance of a slice of float64 values
// Uses Bessel's correction (divides by n-1 instead of n)
func SampleVariance(data []float64) (float64, error) {
	if len(data) <= 1 {
		return 0, ErrInsufficientData
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) <= 1 {
		return 0, ErrInsufficientData
	}

	mean, err := Mean(filtered)
	if err != nil {
		return 0, err
	}

	var sum float64
	for _, v := range filtered {
		diff := v - mean
		sum += diff * diff
	}

	return sum / float64(len(filtered)-1), nil
}

// StandardDeviation calculates the population standard deviation
func StandardDeviation(data []float64) (float64, error) {
	if len(data) == 0 {
		return 0, ErrEmptyDataset
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return 0, ErrInvalidInput
	}

	mean, err := Mean(filtered)
	if err != nil {
		return 0, err
	}

	var sum float64
	for _, v := range filtered {
		diff := v - mean
		sum += diff * diff
	}

	variance := sum / float64(len(filtered))
	return math.Sqrt(variance), nil
}

// SampleStandardDeviation calculates the sample standard deviation
func SampleStandardDeviation(data []float64) (float64, error) {
	variance, err := SampleVariance(data)
	if err != nil {
		return 0, err
	}

	return math.Sqrt(variance), nil
}

// Percentile calculates the p-th percentile of a slice of float64 values
// p should be between 0 and 100 (e.g., 50 for median, 95 for 95th percentile)
func Percentile(data []float64, p float64) (float64, error) {
	if len(data) == 0 {
		return 0, ErrEmptyDataset
	}

	if p < 0 || p > 100 {
		return 0, ErrInvalidInput
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return 0, ErrInvalidInput
	}

	// Create a copy to avoid modifying the original slice
	sorted := make([]float64, len(filtered))
	copy(sorted, filtered)
	sort.Float64s(sorted)

	n := len(sorted)

	// Handle edge cases
	if p == 0 {
		return sorted[0], nil
	}
	if p == 100 {
		return sorted[n-1], nil
	}

	// Calculate index using the linear interpolation method
	index := (p / 100) * float64(n-1)
	lower := int(math.Floor(index))
	upper := int(math.Ceil(index))

	if lower == upper {
		return sorted[lower], nil
	}

	// Linear interpolation between the two adjacent values
	weight := index - float64(lower)
	return sorted[lower]*(1-weight) + sorted[upper]*weight, nil
}

// Quantiles calculates common quantiles (Q1, Q2/median, Q3)
func Quantiles(data []float64) (q1, q2, q3 float64, err error) {
	q1, err = Percentile(data, 25)
	if err != nil {
		return 0, 0, 0, err
	}

	q2, err = Percentile(data, 50)
	if err != nil {
		return 0, 0, 0, err
	}

	q3, err = Percentile(data, 75)
	if err != nil {
		return 0, 0, 0, err
	}

	return q1, q2, q3, nil
}

// InterquartileRange calculates the IQR (Q3 - Q1)
func InterquartileRange(data []float64) (float64, error) {
	q1, _, q3, err := Quantiles(data)
	if err != nil {
		return 0, err
	}

	return q3 - q1, nil
}

// Skewness calculates the skewness (measure of asymmetry) of the distribution
func Skewness(data []float64) (float64, error) {
	if len(data) < 3 {
		return 0, ErrInsufficientData
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) < 3 {
		return 0, ErrInsufficientData
	}

	mean, err := Mean(filtered)
	if err != nil {
		return 0, err
	}

	stdDev, err := StandardDeviation(filtered)
	if err != nil {
		return 0, err
	}

	if stdDev == 0 {
		return 0, nil // No skewness if no variation
	}

	var sum float64
	n := float64(len(filtered))

	for _, v := range filtered {
		standardized := (v - mean) / stdDev
		sum += standardized * standardized * standardized
	}

	return sum / n, nil
}

// Kurtosis calculates the kurtosis (measure of "tailedness") of the distribution
func Kurtosis(data []float64) (float64, error) {
	if len(data) < 4 {
		return 0, ErrInsufficientData
	}

	// Filter out NaN and Inf values
	filtered := filterValidValues(data)
	if len(filtered) < 4 {
		return 0, ErrInsufficientData
	}

	mean, err := Mean(filtered)
	if err != nil {
		return 0, err
	}

	stdDev, err := StandardDeviation(filtered)
	if err != nil {
		return 0, err
	}

	if stdDev == 0 {
		return 0, nil // No kurtosis if no variation
	}

	var sum float64
	n := float64(len(filtered))

	for _, v := range filtered {
		standardized := (v - mean) / stdDev
		sum += standardized * standardized * standardized * standardized
	}

	// Subtract 3 to get excess kurtosis (normal distribution has kurtosis of 3)
	return (sum / n) - 3, nil
}

// DetectOutliers identifies outliers using the IQR method
// Returns the indices of outliers and their count
func DetectOutliers(data []float64) ([]int, int, error) {
	if len(data) < 4 {
		return nil, 0, ErrInsufficientData
	}

	q1, _, q3, err := Quantiles(data)
	if err != nil {
		return nil, 0, err
	}

	iqr := q3 - q1
	lowerBound := q1 - 1.5*iqr
	upperBound := q3 + 1.5*iqr

	var outlierIndices []int
	for i, v := range data {
		if !isValidValue(v) {
			continue // Skip invalid values
		}
		if v < lowerBound || v > upperBound {
			outlierIndices = append(outlierIndices, i)
		}
	}

	return outlierIndices, len(outlierIndices), nil
}

// CalculateStatisticalSummary computes a comprehensive statistical summary
func CalculateStatisticalSummary(data []float64) (*StatisticalSummary, error) {
	if len(data) == 0 {
		return nil, ErrEmptyDataset
	}

	// Filter out NaN and Inf values for calculations
	filtered := filterValidValues(data)
	if len(filtered) == 0 {
		return nil, ErrInvalidInput
	}

	summary := &StatisticalSummary{
		Count: len(filtered),
	}

	// Basic calculations
	var err error

	summary.Sum = sum(filtered)

	summary.Mean, err = Mean(filtered)
	if err != nil {
		return nil, err
	}

	summary.Median, err = Median(filtered)
	if err != nil {
		return nil, err
	}

	summary.Mode, summary.ModeFrequency, err = Mode(filtered)
	if err != nil {
		return nil, err
	}

	summary.Min, err = Min(filtered)
	if err != nil {
		return nil, err
	}

	summary.Max, err = Max(filtered)
	if err != nil {
		return nil, err
	}

	summary.Range = summary.Max - summary.Min

	summary.Variance, err = Variance(filtered)
	if err != nil {
		return nil, err
	}

	summary.StandardDev = math.Sqrt(summary.Variance)

	// Advanced calculations (only if we have enough data)
	if len(filtered) >= 3 {
		summary.Skewness, _ = Skewness(filtered)
	}

	if len(filtered) >= 4 {
		summary.Kurtosis, _ = Kurtosis(filtered)
	}

	// Quartiles
	summary.Q1, _, summary.Q3, err = Quantiles(filtered)
	if err != nil {
		return nil, err
	}

	summary.IQR = summary.Q3 - summary.Q1

	// Outlier detection
	if len(filtered) >= 4 {
		_, outlierCount, err := DetectOutliers(data) // Use original data to get correct indices
		if err == nil {
			summary.OutlierCount = outlierCount
			summary.HasOutliers = outlierCount > 0
		}
	}

	return summary, nil
}

// Helper functions

// filterValidValues removes NaN and Inf values from a slice
func filterValidValues(data []float64) []float64 {
	var filtered []float64
	for _, v := range data {
		if !math.IsNaN(v) && !math.IsInf(v, 0) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// isValidValue checks if a value is neither NaN nor infinite
func isValidValue(v float64) bool {
	return !math.IsNaN(v) && !math.IsInf(v, 0)
}

// sum calculates the sum of values (assumes values are already filtered)
func sum(data []float64) float64 {
	var total float64
	for _, v := range data {
		total += v
	}
	return total
}
