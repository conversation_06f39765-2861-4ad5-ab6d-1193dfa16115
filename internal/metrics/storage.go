package metrics

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"
)

// MetricStorage provides centralized storage and memory management for all metrics
type MetricStorage struct {
	config         StorageConfig
	memoryPool     *MemoryPool
	buffers        map[string]*StorageBuffer
	compressionMgr *CompressionManager
	persistenceMgr *PersistenceManager
	mu             sync.RWMutex
	stats          StorageStats
	ctx            context.Context
	cancel         context.CancelFunc
	cleanupTicker  *time.Ticker
	running        int32
}

// StorageConfig defines configuration for the storage system
type StorageConfig struct {
	MaxMemoryUsage     int64         `json:"max_memory_usage"`    // Maximum memory usage in bytes
	BufferSize         int           `json:"buffer_size"`         // Default buffer size for storage buffers
	CleanupInterval    time.Duration `json:"cleanup_interval"`    // Interval for automatic cleanup
	CompressionEnabled bool          `json:"compression_enabled"` // Enable compression for stored data
	CompressionLevel   int           `json:"compression_level"`   // Compression level (1-9)
	PersistenceEnabled bool          `json:"persistence_enabled"` // Enable disk persistence
	PersistencePath    string        `json:"persistence_path"`    // Path for persistent storage
	RetentionPeriod    time.Duration `json:"retention_period"`    // Data retention period
	PoolSize           int           `json:"pool_size"`           // Memory pool size
	GCOptimization     bool          `json:"gc_optimization"`     // Enable GC optimization features
}

// DefaultStorageConfig returns default storage configuration
func DefaultStorageConfig() StorageConfig {
	return StorageConfig{
		MaxMemoryUsage:     100 * 1024 * 1024, // 100MB
		BufferSize:         10000,             // 10K entries per buffer
		CleanupInterval:    5 * time.Minute,   // Cleanup every 5 minutes
		CompressionEnabled: true,              // Enable compression by default
		CompressionLevel:   6,                 // Balanced compression
		PersistenceEnabled: false,             // Disabled by default
		PersistencePath:    "./metrics_data",  // Default persistence path
		RetentionPeriod:    24 * time.Hour,    // 24 hour retention
		PoolSize:           1000,              // 1000 pooled objects
		GCOptimization:     true,              // Enable GC optimization
	}
}

// StorageStats tracks storage system statistics
type StorageStats struct {
	TotalMemoryUsage  int64     `json:"total_memory_usage"`
	BufferCount       int       `json:"buffer_count"`
	CompressedBytes   int64     `json:"compressed_bytes"`
	UncompressedBytes int64     `json:"uncompressed_bytes"`
	CompressionRatio  float64   `json:"compression_ratio"`
	PersistentBytes   int64     `json:"persistent_bytes"`
	PoolHits          int64     `json:"pool_hits"`
	PoolMisses        int64     `json:"pool_misses"`
	PoolUtilization   float64   `json:"pool_utilization"`
	CleanupOperations int64     `json:"cleanup_operations"`
	LastCleanup       time.Time `json:"last_cleanup"`
}

// StorageEntry represents a stored metric entry
type StorageEntry struct {
	Key        string        `json:"key"`
	Value      interface{}   `json:"value"`
	Tags       TagSet        `json:"tags,omitempty"`
	Timestamp  time.Time     `json:"timestamp"`
	TTL        time.Duration `json:"ttl,omitempty"`
	Compressed bool          `json:"compressed,omitempty"`
}

// NewMetricStorage creates a new metric storage instance
func NewMetricStorage(config StorageConfig) *MetricStorage {
	ctx, cancel := context.WithCancel(context.Background())

	storage := &MetricStorage{
		config:         config,
		memoryPool:     NewMemoryPool(config.PoolSize),
		buffers:        make(map[string]*StorageBuffer),
		compressionMgr: NewCompressionManager(config.CompressionLevel),
		ctx:            ctx,
		cancel:         cancel,
		stats:          StorageStats{},
	}

	// Initialize persistence manager if enabled
	if config.PersistenceEnabled {
		storage.persistenceMgr = NewPersistenceManager(config.PersistencePath, config.RetentionPeriod)
	}

	return storage
}

// Start starts the storage system
func (ms *MetricStorage) Start() error {
	if atomic.CompareAndSwapInt32(&ms.running, 0, 1) {
		ms.cleanupTicker = time.NewTicker(ms.config.CleanupInterval)

		// Start cleanup goroutine
		go ms.runCleanup()

		// Start persistence manager if enabled
		if ms.persistenceMgr != nil {
			if err := ms.persistenceMgr.Start(); err != nil {
				return fmt.Errorf("failed to start persistence manager: %w", err)
			}
		}
	}
	return nil
}

// Stop stops the storage system
func (ms *MetricStorage) Stop() error {
	if atomic.CompareAndSwapInt32(&ms.running, 1, 0) {
		// Cancel context
		ms.cancel()

		// Stop cleanup ticker
		if ms.cleanupTicker != nil {
			ms.cleanupTicker.Stop()
		}

		// Stop persistence manager if enabled
		if ms.persistenceMgr != nil {
			if err := ms.persistenceMgr.Stop(); err != nil {
				return fmt.Errorf("failed to stop persistence manager: %w", err)
			}
		}

		// Perform final cleanup
		ms.cleanup()
	}
	return nil
}

// Store stores a metric entry
func (ms *MetricStorage) Store(key string, value interface{}, tags TagSet, ttl time.Duration) error {
	// Check memory limits before acquiring lock
	if ms.stats.TotalMemoryUsage >= ms.config.MaxMemoryUsage {
		// Attempt cleanup to free memory (without holding lock)
		ms.cleanup()
		if ms.stats.TotalMemoryUsage >= ms.config.MaxMemoryUsage {
			return fmt.Errorf("storage memory limit exceeded: %d bytes", ms.config.MaxMemoryUsage)
		}
	}

	ms.mu.Lock()
	defer ms.mu.Unlock()

	// Get or create buffer for the key
	buffer := ms.getOrCreateBuffer(key)

	// Create storage entry
	entry := &StorageEntry{
		Key:       key,
		Value:     value,
		Tags:      tags,
		Timestamp: time.Now(),
		TTL:       ttl,
	}

	// Compress if enabled
	if ms.config.CompressionEnabled {
		compressed, err := ms.compressionMgr.Compress(entry)
		if err == nil {
			entry = compressed
		}
	}

	// Store in buffer
	if err := buffer.Add(entry); err != nil {
		return fmt.Errorf("failed to store entry: %w", err)
	}

	// Update memory usage
	entrySize := ms.estimateEntrySize(entry)
	atomic.AddInt64(&ms.stats.TotalMemoryUsage, entrySize)

	// Persist if enabled
	if ms.persistenceMgr != nil {
		go ms.persistenceMgr.Persist(entry)
	}

	return nil
}

// Retrieve retrieves metric entries by key
func (ms *MetricStorage) Retrieve(key string, maxAge time.Duration) ([]*StorageEntry, error) {
	ms.mu.RLock()
	buffer, exists := ms.buffers[key]
	ms.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("no data found for key: %s", key)
	}

	entries := buffer.GetAll()
	var result []*StorageEntry

	now := time.Now()
	for _, entry := range entries {
		// Check age
		if maxAge > 0 && now.Sub(entry.Timestamp) > maxAge {
			continue
		}

		// Check TTL
		if entry.TTL > 0 && now.Sub(entry.Timestamp) > entry.TTL {
			continue
		}

		// Decompress if needed
		if entry.Compressed && ms.compressionMgr != nil {
			decompressed, err := ms.compressionMgr.Decompress(entry)
			if err == nil {
				entry = decompressed
			}
		}

		result = append(result, entry)
	}

	return result, nil
}

// RetrieveByTags retrieves metric entries by tag filter
func (ms *MetricStorage) RetrieveByTags(filter *TagFilter, maxAge time.Duration) ([]*StorageEntry, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	var result []*StorageEntry
	now := time.Now()

	for _, buffer := range ms.buffers {
		entries := buffer.GetAll()
		for _, entry := range entries {
			// Check age
			if maxAge > 0 && now.Sub(entry.Timestamp) > maxAge {
				continue
			}

			// Check TTL
			if entry.TTL > 0 && now.Sub(entry.Timestamp) > entry.TTL {
				continue
			}

			// Check tag filter
			if filter != nil && !filter.Matches(&entry.Tags) {
				continue
			}

			// Decompress if needed
			if entry.Compressed && ms.compressionMgr != nil {
				decompressed, err := ms.compressionMgr.Decompress(entry)
				if err == nil {
					entry = decompressed
				}
			}

			result = append(result, entry)
		}
	}

	return result, nil
}

// GetStats returns storage statistics
func (ms *MetricStorage) GetStats() StorageStats {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	stats := ms.stats
	stats.BufferCount = len(ms.buffers)

	// Calculate pool utilization
	if ms.memoryPool != nil {
		poolStats := ms.memoryPool.GetStats()
		stats.PoolHits = poolStats.Hits
		stats.PoolMisses = poolStats.Misses
		stats.PoolUtilization = poolStats.Utilization
	}

	// Calculate compression ratio
	if stats.UncompressedBytes > 0 {
		stats.CompressionRatio = 1.0 - (float64(stats.CompressedBytes) / float64(stats.UncompressedBytes))
	}

	return stats
}

// UpdateConfig updates storage configuration
func (ms *MetricStorage) UpdateConfig(config StorageConfig) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	ms.config = config

	// Update compression manager
	if ms.compressionMgr != nil {
		ms.compressionMgr.SetLevel(config.CompressionLevel)
	}

	// Update persistence manager
	if config.PersistenceEnabled && ms.persistenceMgr == nil {
		ms.persistenceMgr = NewPersistenceManager(config.PersistencePath, config.RetentionPeriod)
		if atomic.LoadInt32(&ms.running) == 1 {
			ms.persistenceMgr.Start()
		}
	} else if !config.PersistenceEnabled && ms.persistenceMgr != nil {
		ms.persistenceMgr.Stop()
		ms.persistenceMgr = nil
	}

	return nil
}

// getOrCreateBuffer gets or creates a storage buffer for a key
func (ms *MetricStorage) getOrCreateBuffer(key string) *StorageBuffer {
	if buffer, exists := ms.buffers[key]; exists {
		return buffer
	}

	buffer := NewStorageBuffer(ms.config.BufferSize, ms.memoryPool)
	ms.buffers[key] = buffer
	return buffer
}

// runCleanup runs periodic cleanup operations
func (ms *MetricStorage) runCleanup() {
	for {
		select {
		case <-ms.ctx.Done():
			return
		case <-ms.cleanupTicker.C:
			ms.cleanup()
		}
	}
}

// cleanup performs memory cleanup and garbage collection optimization
func (ms *MetricStorage) cleanup() {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	now := time.Now()
	totalFreed := int64(0)

	// Clean up expired entries from buffers
	for key, buffer := range ms.buffers {
		freed := buffer.Cleanup(now)
		totalFreed += freed

		// Remove empty buffers
		if buffer.IsEmpty() {
			delete(ms.buffers, key)
		}
	}

	// Update memory usage
	atomic.AddInt64(&ms.stats.TotalMemoryUsage, -totalFreed)

	// Clean up memory pool
	if ms.memoryPool != nil {
		ms.memoryPool.Cleanup()
	}

	// Update stats
	atomic.AddInt64(&ms.stats.CleanupOperations, 1)
	ms.stats.LastCleanup = now

	// Force GC if optimization is enabled and memory usage is high
	if ms.config.GCOptimization && ms.stats.TotalMemoryUsage > ms.config.MaxMemoryUsage/2 {
		// Note: In production, we might use runtime.GC() here, but we'll keep it lightweight
		// runtime.GC()
	}
}

// estimateEntrySize estimates the memory size of a storage entry
func (ms *MetricStorage) estimateEntrySize(entry *StorageEntry) int64 {
	size := int64(0)

	// Key size
	size += int64(len(entry.Key))

	// Value size (rough estimation)
	switch v := entry.Value.(type) {
	case string:
		size += int64(len(v))
	case []byte:
		size += int64(len(v))
	case int, int32, int64, float32, float64:
		size += 8
	default:
		// Rough estimate for complex types
		size += 64
	}

	// Tags size
	tagsMap := entry.Tags.ToMap()
	for key, value := range tagsMap {
		size += int64(len(key) + len(value))
	}

	// Timestamp and other fields
	size += 32

	return size
}
