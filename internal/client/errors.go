// Package client provides comprehensive HTTP error handling for load testing
package client

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"syscall"
	"time"
)

// URLError represents a URL-related error
type URLError = url.Error

// EnhancedErrorType represents the category of HTTP error (enhanced version)
type EnhancedErrorType int

const (
	// Network-related errors
	EnhancedErrorTypeNetwork EnhancedErrorType = iota
	EnhancedErrorTypeTimeout
	EnhancedErrorTypeConnection
	EnhancedErrorTypeDNS
	EnhancedErrorTypeTLS

	// HTTP protocol errors
	EnhancedErrorTypeStatusCode
	EnhancedErrorTypeResponse
	EnhancedErrorTypeParsing
	EnhancedErrorTypeRedirection

	// Client configuration errors
	EnhancedErrorTypeConfiguration
	EnhancedErrorTypeContext
	EnhancedErrorTypeRequest

	// Server-side errors
	EnhancedErrorTypeServerUnavailable
	EnhancedErrorTypeServerTimeout
	EnhancedErrorTypeServerError

	// Unknown/generic errors
	EnhancedErrorTypeUnknown
)

// String returns a human-readable string representation of the error type
func (t EnhancedErrorType) String() string {
	switch t {
	case EnhancedErrorTypeNetwork:
		return "network"
	case EnhancedErrorTypeTimeout:
		return "timeout"
	case EnhancedErrorTypeConnection:
		return "connection"
	case EnhancedErrorTypeDNS:
		return "dns"
	case EnhancedErrorTypeTLS:
		return "tls"
	case EnhancedErrorTypeStatusCode:
		return "status_code"
	case EnhancedErrorTypeResponse:
		return "response"
	case EnhancedErrorTypeParsing:
		return "parsing"
	case EnhancedErrorTypeRedirection:
		return "redirection"
	case EnhancedErrorTypeConfiguration:
		return "configuration"
	case EnhancedErrorTypeContext:
		return "context"
	case EnhancedErrorTypeRequest:
		return "request"
	case EnhancedErrorTypeServerUnavailable:
		return "server_unavailable"
	case EnhancedErrorTypeServerTimeout:
		return "server_timeout"
	case EnhancedErrorTypeServerError:
		return "server_error"
	default:
		return "unknown"
	}
}

// HTTPErrorInterface defines the common interface for all HTTP errors
type HTTPErrorInterface interface {
	error
	GetType() EnhancedErrorType
	GetStatusCode() int
	GetURL() string
	GetDuration() time.Duration
	GetContext() map[string]interface{}
	IsRetriable() bool
	GetRetryDelay() time.Duration
	WithContext(key string, value interface{}) HTTPErrorInterface
}

// BaseHTTPError represents a base HTTP error with enhanced details
type BaseHTTPError struct {
	Type            EnhancedErrorType
	Message         string
	URL             string
	Duration        time.Duration
	StatusCode      int
	Timestamp       time.Time
	Underlying      error
	Retriable       bool
	RetryDelay      time.Duration
	ResponseBody    string
	ResponseHeaders http.Header
	Context         map[string]interface{}
}

// calculateRetryDelay determines the appropriate retry delay based on HTTP response
func calculateRetryDelay(resp *http.Response) time.Duration {
	// Check for Retry-After header
	if retryAfterStr := resp.Header.Get("Retry-After"); retryAfterStr != "" {
		// Try parsing as seconds
		if seconds, err := strconv.Atoi(retryAfterStr); err == nil {
			return time.Duration(seconds) * time.Second
		}

		// Try parsing as HTTP date
		if retryTime, err := http.ParseTime(retryAfterStr); err == nil {
			return time.Until(retryTime)
		}
	}

	// Default retry strategies based on status code
	switch resp.StatusCode {
	case http.StatusTooManyRequests: // 429
		return 5 * time.Second
	case http.StatusServiceUnavailable: // 503
		return 3 * time.Second
	case http.StatusGatewayTimeout: // 504
		return 2 * time.Second
	default:
		return 1 * time.Second
	}
}

// Error implements the error interface
func (e *BaseHTTPError) Error() string {
	if e.StatusCode > 0 {
		return fmt.Sprintf("HTTP %s error: %s (status: %d, url: %s, duration: %v)",
			e.Type.String(), e.Message, e.StatusCode, e.URL, e.Duration)
	}
	return fmt.Sprintf("HTTP %s error: %s (url: %s, duration: %v)",
		e.Type.String(), e.Message, e.URL, e.Duration)
}

// GetType returns the error type
func (e *BaseHTTPError) GetType() EnhancedErrorType {
	return e.Type
}

// GetStatusCode returns the HTTP status code
func (e *BaseHTTPError) GetStatusCode() int {
	return e.StatusCode
}

// GetURL returns the request URL
func (e *BaseHTTPError) GetURL() string {
	return e.URL
}

// GetDuration returns the request duration
func (e *BaseHTTPError) GetDuration() time.Duration {
	return e.Duration
}

// GetContext returns the error context
func (e *BaseHTTPError) GetContext() map[string]interface{} {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	return e.Context
}

// IsRetriable returns whether the error is retriable
func (e *BaseHTTPError) IsRetriable() bool {
	return e.Retriable
}

// GetRetryDelay returns the suggested retry delay
func (e *BaseHTTPError) GetRetryDelay() time.Duration {
	return e.RetryDelay
}

// WithContext adds context to the error
func (e *BaseHTTPError) WithContext(key string, value interface{}) HTTPErrorInterface {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// Unwrap returns the underlying error
func (e *BaseHTTPError) Unwrap() error {
	return e.Underlying
}

// NetworkError represents network-related errors
type NetworkError struct {
	*BaseHTTPError
	NetworkType string `json:"network_type,omitempty"` // tcp, udp, etc.
	LocalAddr   string `json:"local_addr,omitempty"`
	RemoteAddr  string `json:"remote_addr,omitempty"`
}

// NewNetworkError creates a new network error
func NewNetworkError(message, url string, duration time.Duration, underlying error) *NetworkError {
	return &NetworkError{
		BaseHTTPError: &BaseHTTPError{
			Type:       EnhancedErrorTypeNetwork,
			Message:    message,
			URL:        url,
			Duration:   duration,
			Timestamp:  time.Now(),
			Underlying: underlying,
			Retriable:  true,
			RetryDelay: 1 * time.Second,
		},
	}
}

// TimeoutError represents timeout-related errors
type TimeoutError struct {
	*BaseHTTPError
	TimeoutType string        `json:"timeout_type,omitempty"` // dial, response, etc.
	Timeout     time.Duration `json:"timeout,omitempty"`
}

// NewTimeoutError creates a new timeout error
func NewTimeoutError(message, url string, duration time.Duration, timeoutType string, timeout time.Duration, underlying error) *TimeoutError {
	return &TimeoutError{
		BaseHTTPError: &BaseHTTPError{
			Type:       EnhancedErrorTypeTimeout,
			Message:    message,
			URL:        url,
			Duration:   duration,
			Timestamp:  time.Now(),
			Underlying: underlying,
			Retriable:  true,
			RetryDelay: 2 * time.Second,
		},
		TimeoutType: timeoutType,
		Timeout:     timeout,
	}
}

// StatusCodeError represents HTTP status code errors
type StatusCodeError struct {
	*BaseHTTPError
	ResponseHeaders map[string]string `json:"response_headers,omitempty"`
	ResponseSize    int64             `json:"response_size,omitempty"`
}

// NewStatusCodeError creates a new status code error
func NewStatusCodeError(statusCode int, message, url string, duration time.Duration, responseBody string, headers map[string]string) *StatusCodeError {
	retriable := isRetriableStatusCode(statusCode)
	retryDelay := calculateStatusCodeRetryDelay(statusCode)

	return &StatusCodeError{
		BaseHTTPError: &BaseHTTPError{
			Type:         EnhancedErrorTypeStatusCode,
			Message:      message,
			StatusCode:   statusCode,
			URL:          url,
			Duration:     duration,
			Timestamp:    time.Now(),
			ResponseBody: responseBody,
			Retriable:    retriable,
			RetryDelay:   retryDelay,
		},
		ResponseHeaders: headers,
		ResponseSize:    int64(len(responseBody)),
	}
}

// ParseError represents parsing/response processing errors
type ParseError struct {
	*BaseHTTPError
	ParseType    string `json:"parse_type,omitempty"` // json, xml, etc.
	ParsedBytes  int64  `json:"parsed_bytes,omitempty"`
	ExpectedType string `json:"expected_type,omitempty"`
}

// NewParseError creates a new parse error
func NewParseError(message, url string, duration time.Duration, parseType string, underlying error) *ParseError {
	return &ParseError{
		BaseHTTPError: &BaseHTTPError{
			Type:       EnhancedErrorTypeParsing,
			Message:    message,
			URL:        url,
			Duration:   duration,
			Timestamp:  time.Now(),
			Underlying: underlying,
			Retriable:  false, // Parse errors are typically not retriable
			RetryDelay: 0,
		},
		ParseType: parseType,
	}
}

// ConnectionError represents connection-related errors
type ConnectionError struct {
	*BaseHTTPError
	ConnectionType string `json:"connection_type,omitempty"` // new, reuse, etc.
	PoolStats      string `json:"pool_stats,omitempty"`
}

// NewConnectionError creates a new connection error
func NewConnectionError(message, url string, duration time.Duration, underlying error) *ConnectionError {
	return &ConnectionError{
		BaseHTTPError: &BaseHTTPError{
			Type:       EnhancedErrorTypeConnection,
			Message:    message,
			URL:        url,
			Duration:   duration,
			Timestamp:  time.Now(),
			Underlying: underlying,
			Retriable:  true,
			RetryDelay: 1 * time.Second,
		},
	}
}

// DNSError represents DNS resolution errors
type DNSError struct {
	*BaseHTTPError
	Hostname    string   `json:"hostname,omitempty"`
	DNSServers  []string `json:"dns_servers,omitempty"`
	RecordTypes []string `json:"record_types,omitempty"`
}

// NewDNSError creates a new DNS error
func NewDNSError(message, url, hostname string, duration time.Duration, underlying error) *DNSError {
	return &DNSError{
		BaseHTTPError: &BaseHTTPError{
			Type:       EnhancedErrorTypeDNS,
			Message:    message,
			URL:        url,
			Duration:   duration,
			Timestamp:  time.Now(),
			Underlying: underlying,
			Retriable:  true,
			RetryDelay: 5 * time.Second, // DNS errors need longer delays
		},
		Hostname: hostname,
	}
}

// TLSError represents TLS/SSL related errors
type TLSError struct {
	*BaseHTTPError
	CertificateError string `json:"certificate_error,omitempty"`
	TLSVersion       string `json:"tls_version,omitempty"`
	CipherSuite      string `json:"cipher_suite,omitempty"`
}

// NewTLSError creates a new TLS error
func NewTLSError(message, url string, duration time.Duration, underlying error) *TLSError {
	return &TLSError{
		BaseHTTPError: &BaseHTTPError{
			Type:       EnhancedErrorTypeTLS,
			Message:    message,
			URL:        url,
			Duration:   duration,
			Timestamp:  time.Now(),
			Underlying: underlying,
			Retriable:  false, // TLS errors are typically not retriable
			RetryDelay: 0,
		},
	}
}

// ContextError represents context-related errors (cancellation, timeout)
type ContextError struct {
	*BaseHTTPError
	ContextType string `json:"context_type,omitempty"` // cancelled, deadline_exceeded, etc.
}

// NewContextError creates a new context error
func NewContextError(message, url string, duration time.Duration, contextType string, underlying error) *ContextError {
	return &ContextError{
		BaseHTTPError: &BaseHTTPError{
			Type:       EnhancedErrorTypeContext,
			Message:    message,
			URL:        url,
			Duration:   duration,
			Timestamp:  time.Now(),
			Underlying: underlying,
			Retriable:  false, // Context errors are not retriable
			RetryDelay: 0,
		},
		ContextType: contextType,
	}
}

// ErrorCategorizer provides methods for categorizing and analyzing errors
type ErrorCategorizer struct{}

// CategorizeError analyzes an error and returns the appropriate HTTPErrorInterface
func (ec *ErrorCategorizer) CategorizeError(err error, req *http.Request, resp *http.Response, duration time.Duration) HTTPErrorInterface {
	if err == nil {
		return nil
	}

	url := ""
	if req != nil {
		url = req.URL.String()
	}

	// Check for context errors first
	if err == context.Canceled {
		return NewContextError("Request cancelled", url, duration, "cancelled", err)
	}
	if err == context.DeadlineExceeded {
		return NewContextError("Request deadline exceeded", url, duration, "deadline_exceeded", err)
	}

	// Check for URL errors (includes DNS errors)
	if urlErr, ok := err.(*URLError); ok {
		if dnsErr, ok := urlErr.Err.(*net.DNSError); ok {
			return NewDNSError("DNS resolution failed: "+dnsErr.Error(), url, dnsErr.Name, duration, err)
		}
		if netErr, ok := urlErr.Err.(net.Error); ok && netErr.Timeout() {
			return NewTimeoutError("URL timeout: "+urlErr.Error(), url, duration, "url", 0, err)
		}
		return NewNetworkError("URL error: "+urlErr.Error(), url, duration, err)
	}

	// Check for syscall errors
	if opErr, ok := err.(*net.OpError); ok {
		if opErr.Timeout() {
			return NewTimeoutError("Operation timeout", url, duration, "syscall", 0, err)
		}
		if syscallErr, ok := opErr.Err.(*syscall.Errno); ok {
			switch *syscallErr {
			case syscall.ECONNREFUSED:
				return NewConnectionError("Connection refused", url, duration, err)
			case syscall.ECONNRESET:
				return NewConnectionError("Connection reset", url, duration, err)
			case syscall.ETIMEDOUT:
				return NewTimeoutError("Connection timeout", url, duration, "connection", 0, err)
			}
		}
		// If the underlying error is a syscall error, handle it
		if underlyingErr, ok := opErr.Err.(syscall.Errno); ok {
			switch underlyingErr {
			case syscall.ECONNREFUSED:
				return NewConnectionError("Connection refused", url, duration, err)
			case syscall.ECONNRESET:
				return NewConnectionError("Connection reset", url, duration, err)
			case syscall.ETIMEDOUT:
				return NewTimeoutError("Connection timeout", url, duration, "connection", 0, err)
			}
		}
		return NewNetworkError("Network operation error: "+opErr.Error(), url, duration, err)
	}

	// Check for direct syscall errors
	if syscallErr, ok := err.(*syscall.Errno); ok {
		switch *syscallErr {
		case syscall.ECONNREFUSED:
			return NewConnectionError("Connection refused", url, duration, err)
		case syscall.ECONNRESET:
			return NewConnectionError("Connection reset", url, duration, err)
		case syscall.ETIMEDOUT:
			return NewTimeoutError("Connection timeout", url, duration, "connection", 0, err)
		}
	}

	// Handle HTTP response errors
	if resp != nil {
		// Determine error type based on status code
		var errorType EnhancedErrorType
		switch {
		case resp.StatusCode >= 500:
			errorType = EnhancedErrorTypeServerError
		case resp.StatusCode >= 400:
			errorType = EnhancedErrorTypeStatusCode
		default:
			errorType = EnhancedErrorTypeResponse
		}

		return &BaseHTTPError{
			Type:            errorType,
			Message:         fmt.Sprintf("HTTP error: %s", err.Error()),
			URL:             req.URL.String(),
			Duration:        duration,
			StatusCode:      resp.StatusCode,
			Timestamp:       time.Now(),
			Underlying:      err,
			Retriable:       resp.StatusCode >= 500 || resp.StatusCode == http.StatusTooManyRequests,
			RetryDelay:      calculateRetryDelay(resp),
			ResponseBody:    "", // Optionally capture response body
			ResponseHeaders: resp.Header,
		}
	}

	// Check for TLS errors
	if strings.Contains(err.Error(), "tls") || strings.Contains(err.Error(), "certificate") {
		return NewTLSError("TLS error: "+err.Error(), url, duration, err)
	}

	// Default to unknown error
	return &BaseHTTPError{
		Type:       EnhancedErrorTypeUnknown,
		Message:    err.Error(),
		URL:        url,
		Duration:   duration,
		Timestamp:  time.Now(),
		Underlying: err,
		Retriable:  false,
		RetryDelay: 0,
	}
}

// isRetriableStatusCode determines if an HTTP status code is retriable
func isRetriableStatusCode(statusCode int) bool {
	switch statusCode {
	case 408, // Request Timeout
		429, // Too Many Requests
		500, // Internal Server Error
		502, // Bad Gateway
		503, // Service Unavailable
		504, // Gateway Timeout
		507, // Insufficient Storage
		509, // Bandwidth Limit Exceeded
		510: // Not Extended
		return true
	default:
		return false
	}
}

// calculateStatusCodeRetryDelay calculates retry delay based on status code
func calculateStatusCodeRetryDelay(statusCode int) time.Duration {
	switch statusCode {
	case 429: // Too Many Requests
		return 5 * time.Second
	case 503, 504: // Service Unavailable, Gateway Timeout
		return 3 * time.Second
	case 500, 502: // Internal Server Error, Bad Gateway
		return 2 * time.Second
	default:
		return 1 * time.Second
	}
}

// GetRetriableErrorTypes returns a list of error types that are retriable
func GetRetriableErrorTypes() []EnhancedErrorType {
	return []EnhancedErrorType{
		EnhancedErrorTypeNetwork,
		EnhancedErrorTypeTimeout,
		EnhancedErrorTypeConnection,
		EnhancedErrorTypeDNS,
		EnhancedErrorTypeStatusCode, // Only some status codes are retriable
		EnhancedErrorTypeServerUnavailable,
		EnhancedErrorTypeServerTimeout,
	}
}

// GetNonRetriableErrorTypes returns a list of error types that are not retriable
func GetNonRetriableErrorTypes() []EnhancedErrorType {
	return []EnhancedErrorType{
		EnhancedErrorTypeTLS,
		EnhancedErrorTypeParsing,
		EnhancedErrorTypeConfiguration,
		EnhancedErrorTypeContext,
		EnhancedErrorTypeRequest,
		EnhancedErrorTypeResponse,
		EnhancedErrorTypeRedirection,
		EnhancedErrorTypeUnknown,
	}
}

// ErrorStatistics provides statistics about error patterns
type ErrorStatistics struct {
	TotalErrors          int64                       `json:"total_errors"`
	ErrorsByType         map[EnhancedErrorType]int64 `json:"errors_by_type"`
	ErrorsByStatusCode   map[int]int64               `json:"errors_by_status_code"`
	RetriableErrors      int64                       `json:"retriable_errors"`
	NonRetriableErrors   int64                       `json:"non_retriable_errors"`
	AverageErrorDuration time.Duration               `json:"average_error_duration"`
	ErrorRatePercentage  float64                     `json:"error_rate_percentage"`
	LastError            HTTPErrorInterface          `json:"-"`
	ErrorFrequency       map[string]int64            `json:"error_frequency"` // Error message frequency
}

// NewErrorStatistics creates a new error statistics tracker
func NewErrorStatistics() *ErrorStatistics {
	return &ErrorStatistics{
		ErrorsByType:       make(map[EnhancedErrorType]int64),
		ErrorsByStatusCode: make(map[int]int64),
		ErrorFrequency:     make(map[string]int64),
	}
}

// RecordError records an error in the statistics
func (es *ErrorStatistics) RecordError(err HTTPErrorInterface) {
	if err == nil {
		return
	}

	es.TotalErrors++
	es.ErrorsByType[err.GetType()]++

	if statusCode := err.GetStatusCode(); statusCode > 0 {
		es.ErrorsByStatusCode[statusCode]++
	}

	if err.IsRetriable() {
		es.RetriableErrors++
	} else {
		es.NonRetriableErrors++
	}

	es.LastError = err
	es.ErrorFrequency[err.Error()]++
}

// GetMostCommonErrorType returns the most frequently occurring error type
func (es *ErrorStatistics) GetMostCommonErrorType() EnhancedErrorType {
	var maxType EnhancedErrorType
	var maxCount int64

	for errorType, count := range es.ErrorsByType {
		if count > maxCount {
			maxCount = count
			maxType = errorType
		}
	}

	return maxType
}

// GetRetriabilityRatio returns the ratio of retriable to total errors
func (es *ErrorStatistics) GetRetriabilityRatio() float64 {
	if es.TotalErrors == 0 {
		return 0.0
	}
	return float64(es.RetriableErrors) / float64(es.TotalErrors)
}

// EnhancedErrorCategorizer provides advanced error categorization and analysis
type EnhancedErrorCategorizer struct {
	ErrorStatistics *ErrorStatistics
}

// NewEnhancedErrorCategorizer creates a new enhanced error categorizer
func NewEnhancedErrorCategorizer() *EnhancedErrorCategorizer {
	return &EnhancedErrorCategorizer{
		ErrorStatistics: NewErrorStatistics(),
	}
}

// CategorizeError provides advanced error categorization with enhanced context
func (ec *EnhancedErrorCategorizer) CategorizeError(
	err error,
	req *http.Request,
	resp *http.Response,
	duration time.Duration,
) HTTPErrorInterface {
	if err == nil {
		return nil
	}

	// Capture context from request and response
	context := make(map[string]interface{})
	if req != nil {
		context["method"] = req.Method
		context["url"] = req.URL.String()
		context["host"] = req.Host
	}

	// Detailed error type detection
	switch e := err.(type) {
	case *net.OpError:
		return ec.categorizeNetworkError(e, context, duration)
	case *url.Error:
		return ec.categorizeURLError(e, context, duration)
	case *net.DNSError:
		return NewDNSError(
			"DNS resolution failed",
			req.URL.String(),
			e.Name,
			duration,
			err,
		)
	case syscall.Errno:
		return ec.categorizeSyscallError(e, req, context, duration)
	}

	// Check for context-related errors
	if strings.Contains(err.Error(), "context deadline exceeded") {
		return NewTimeoutError(
			"Context deadline exceeded",
			req.URL.String(),
			duration,
			"context",
			duration,
			err,
		)
	}

	if strings.Contains(err.Error(), "context canceled") {
		return NewContextError(
			"Operation cancelled",
			req.URL.String(),
			duration,
			"cancelled",
			err,
		)
	}

	// Handle HTTP response errors
	if resp != nil {
		// Determine error type based on status code
		var errorType EnhancedErrorType
		switch {
		case resp.StatusCode >= 500:
			errorType = EnhancedErrorTypeServerError
		case resp.StatusCode >= 400:
			errorType = EnhancedErrorTypeStatusCode
		default:
			errorType = EnhancedErrorTypeResponse
		}

		return &BaseHTTPError{
			Type:            errorType,
			Message:         fmt.Sprintf("HTTP error: %s", err.Error()),
			URL:             req.URL.String(),
			Duration:        duration,
			StatusCode:      resp.StatusCode,
			Timestamp:       time.Now(),
			Underlying:      err,
			Retriable:       resp.StatusCode >= 500 || resp.StatusCode == http.StatusTooManyRequests,
			RetryDelay:      calculateRetryDelay(resp),
			ResponseBody:    "", // Optionally capture response body
			ResponseHeaders: resp.Header,
		}
	}

	// Generic error fallback
	return &BaseHTTPError{
		Type:       EnhancedErrorTypeUnknown,
		Message:    err.Error(),
		Timestamp:  time.Now(),
		Underlying: err,
		Retriable:  false,
	}
}

// categorizeNetworkError provides detailed network error categorization
func (ec *EnhancedErrorCategorizer) categorizeNetworkError(
	netErr *net.OpError,
	context map[string]interface{},
	duration time.Duration,
) HTTPErrorInterface {
	// Defensive nil checks
	if netErr == nil {
		return &BaseHTTPError{
			Type:      EnhancedErrorTypeUnknown,
			Message:   "Nil network error",
			Timestamp: time.Now(),
			Retriable: false,
		}
	}

	// Ensure context is not nil
	if context == nil {
		context = make(map[string]interface{})
	}

	// Add network operation details to context
	context["network_operation"] = netErr.Op
	if netErr.Addr != nil {
		context["network_address"] = netErr.Addr.String()
	}

	// Check if the error is temporary or permanent
	var isTemporary bool
	if tempErr, ok := netErr.Err.(interface{ Temporary() bool }); ok {
		isTemporary = tempErr.Temporary()
	}

	// Determine retriability based on error characteristics
	retriable := isTemporary ||
		strings.Contains(netErr.Err.Error(), "temporary") ||
		strings.Contains(netErr.Err.Error(), "timeout")

	// Create appropriate network error
	baseError := &BaseHTTPError{
		Type:       EnhancedErrorTypeNetwork,
		Message:    "Persistent network error",
		URL:        context["url"].(string),
		Duration:   duration,
		Timestamp:  time.Now(),
		Underlying: netErr,
		Retriable:  retriable,
		RetryDelay: 1 * time.Second,
	}

	return &NetworkError{
		BaseHTTPError: baseError,
		NetworkType:   netErr.Net,
		LocalAddr:     "", // Populate if possible
		RemoteAddr:    "", // Populate if possible
	}
}

// categorizeURLError handles URL-specific errors
func (ec *EnhancedErrorCategorizer) categorizeURLError(
	urlErr *url.Error,
	context map[string]interface{},
	duration time.Duration,
) HTTPErrorInterface {
	context["original_url"] = urlErr.URL
	context["operation"] = urlErr.Op

	if urlErr.Timeout() {
		return NewTimeoutError(
			"URL operation timed out",
			urlErr.URL,
			duration,
			"url",
			duration,
			urlErr,
		)
	}

	return NewNetworkError(
		"URL resolution error",
		urlErr.URL,
		duration,
		urlErr,
	).WithContext("details", context)
}

// categorizeSyscallError handles low-level system call errors
func (ec *EnhancedErrorCategorizer) categorizeSyscallError(
	sysErr syscall.Errno,
	req *http.Request,
	context map[string]interface{},
	duration time.Duration,
) HTTPErrorInterface {
	context["syscall_errno"] = sysErr

	switch sysErr {
	case syscall.ECONNREFUSED:
		return NewConnectionError(
			"Connection refused",
			req.URL.String(),
			duration,
			sysErr,
		).WithContext("details", context)
	case syscall.ETIMEDOUT:
		return NewTimeoutError(
			"System call timeout",
			req.URL.String(),
			duration,
			"syscall",
			duration,
			sysErr,
		)
	case syscall.ENETUNREACH:
		return NewNetworkError(
			"Network unreachable",
			req.URL.String(),
			duration,
			sysErr,
		).WithContext("details", context)
	default:
		return NewNetworkError(
			fmt.Sprintf("Syscall error: %v", sysErr),
			req.URL.String(),
			duration,
			sysErr,
		).WithContext("details", context)
	}
}

// Enhance ErrorStatistics with more advanced tracking
func (es *ErrorStatistics) CalculateErrorRatePercentage(totalRequests int64) {
	if totalRequests > 0 {
		es.ErrorRatePercentage = (float64(es.TotalErrors) / float64(totalRequests)) * 100
	}
}

// GetMostFrequentErrorMessage returns the most frequently occurring error message
func (es *ErrorStatistics) GetMostFrequentErrorMessage() string {
	var mostFrequentMessage string
	var maxFrequency int64

	for message, frequency := range es.ErrorFrequency {
		if frequency > maxFrequency {
			mostFrequentMessage = message
			maxFrequency = frequency
		}
	}

	return mostFrequentMessage
}

// GetErrorTypeDistribution returns a percentage distribution of error types
func (es *ErrorStatistics) GetErrorTypeDistribution() map[EnhancedErrorType]float64 {
	distribution := make(map[EnhancedErrorType]float64)

	for errorType, count := range es.ErrorsByType {
		distribution[errorType] = (float64(count) / float64(es.TotalErrors)) * 100
	}

	return distribution
}
