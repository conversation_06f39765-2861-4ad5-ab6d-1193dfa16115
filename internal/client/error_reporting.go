package client

import (
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"sync"
	"time"
)

// ErrorReportingConfig configures error reporting behavior
type ErrorReportingConfig struct {
	EnableStackTrace      bool          `json:"enable_stack_trace"`
	EnableRequestLogging  bool          `json:"enable_request_logging"`
	EnableResponseLogging bool          `json:"enable_response_logging"`
	MaxBodyLogSize        int           `json:"max_body_log_size"` // Maximum bytes to log from request/response bodies
	LogLevel              LogLevel      `json:"log_level"`
	RetentionPeriod       time.Duration `json:"retention_period"` // How long to keep error logs
	MetricsEnabled        bool          `json:"metrics_enabled"`
}

// LogLevel represents the verbosity of error logging
type LogLevel int

const (
	LogLevelSilent LogLevel = iota
	LogLevelError
	LogLevelWarn
	LogLevelInfo
	LogLevelDebug
	LogLevelTrace
)

func (l LogLevel) String() string {
	switch l {
	case LogLevelSilent:
		return "SILENT"
	case LogLevelError:
		return "ERROR"
	case LogLevelWarn:
		return "WARN"
	case LogLevelInfo:
		return "INFO"
	case LogLevelDebug:
		return "DEBUG"
	case LogLevelTrace:
		return "TRACE"
	default:
		return "UNKNOWN"
	}
}

// DefaultErrorReportingConfig returns a sensible default configuration
func DefaultErrorReportingConfig() *ErrorReportingConfig {
	return &ErrorReportingConfig{
		EnableStackTrace:      true,
		EnableRequestLogging:  true,
		EnableResponseLogging: true,
		MaxBodyLogSize:        1024, // 1KB
		LogLevel:              LogLevelInfo,
		RetentionPeriod:       24 * time.Hour,
		MetricsEnabled:        true,
	}
}

// ErrorContext holds detailed context information for an error
type ErrorContext struct {
	RequestID       string                 `json:"request_id,omitempty"`
	TraceID         string                 `json:"trace_id,omitempty"`
	Timestamp       time.Time              `json:"timestamp"`
	StackTrace      []string               `json:"stack_trace,omitempty"`
	RequestDetails  *RequestDetails        `json:"request_details,omitempty"`
	ResponseDetails *ResponseDetails       `json:"response_details,omitempty"`
	RetryAttempt    int                    `json:"retry_attempt,omitempty"`
	TotalRetries    int                    `json:"total_retries,omitempty"`
	UserAgent       string                 `json:"user_agent,omitempty"`
	ClientVersion   string                 `json:"client_version,omitempty"`
	Environment     map[string]interface{} `json:"environment,omitempty"`
}

// RequestDetails contains detailed information about the HTTP request
type RequestDetails struct {
	Method      string            `json:"method"`
	URL         string            `json:"url"`
	Headers     map[string]string `json:"headers,omitempty"`
	BodyPreview string            `json:"body_preview,omitempty"` // Truncated body for logging
	BodySize    int64             `json:"body_size"`
	Timeout     time.Duration     `json:"timeout,omitempty"`
}

// ResponseDetails contains detailed information about the HTTP response
type ResponseDetails struct {
	StatusCode      int               `json:"status_code"`
	Status          string            `json:"status"`
	Headers         map[string]string `json:"headers,omitempty"`
	BodyPreview     string            `json:"body_preview,omitempty"` // Truncated body for logging
	BodySize        int64             `json:"body_size"`
	Duration        time.Duration     `json:"duration"`
	ContentType     string            `json:"content_type,omitempty"`
	ContentEncoding string            `json:"content_encoding,omitempty"`
}

// ErrorReport represents a comprehensive error report
type ErrorReport struct {
	ID               string                 `json:"id"`
	Timestamp        time.Time              `json:"timestamp"`
	Error            HTTPErrorInterface     `json:"error"`
	Context          *ErrorContext          `json:"context"`
	Severity         ErrorSeverity          `json:"severity"`
	Category         string                 `json:"category"`
	HumanMessage     string                 `json:"human_message"`
	TechnicalMessage string                 `json:"technical_message"`
	Suggestions      []string               `json:"suggestions,omitempty"`
	RelatedErrors    []string               `json:"related_errors,omitempty"` // IDs of related error reports
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
}

// ErrorSeverity represents the severity level of an error
type ErrorSeverity int

const (
	SeverityLow ErrorSeverity = iota
	SeverityMedium
	SeverityHigh
	SeverityCritical
)

func (s ErrorSeverity) String() string {
	switch s {
	case SeverityLow:
		return "LOW"
	case SeverityMedium:
		return "MEDIUM"
	case SeverityHigh:
		return "HIGH"
	case SeverityCritical:
		return "CRITICAL"
	default:
		return "UNKNOWN"
	}
}

// ErrorReporter handles error reporting, logging, and metrics collection
type ErrorReporter struct {
	config      *ErrorReportingConfig
	categorizer *EnhancedErrorCategorizer
	reports     map[string]*ErrorReport
	metrics     *ErrorMetrics
	mu          sync.RWMutex
	reportChan  chan *ErrorReport
	stopChan    chan struct{}
	formatter   *ErrorFormatter
}

// ErrorMetrics tracks error patterns and statistics
type ErrorMetrics struct {
	TotalErrors        int64                       `json:"total_errors"`
	ErrorsByType       map[EnhancedErrorType]int64 `json:"errors_by_type"`
	ErrorsByStatusCode map[int]int64               `json:"errors_by_status_code"`
	ErrorsBySeverity   map[ErrorSeverity]int64     `json:"errors_by_severity"`
	ErrorsOverTime     map[string]int64            `json:"errors_over_time"` // timestamp -> count
	AvgErrorDuration   time.Duration               `json:"avg_error_duration"`
	RetryStatistics    *RetryStatistics            `json:"retry_statistics"`
	ErrorPatterns      map[string]*ErrorPattern    `json:"error_patterns"`
	mu                 sync.RWMutex                `json:"-"`
}

// RetryStatistics tracks retry-related metrics
type RetryStatistics struct {
	TotalRetries        int64            `json:"total_retries"`
	SuccessfulRetries   int64            `json:"successful_retries"`
	FailedRetries       int64            `json:"failed_retries"`
	RetrySuccessRate    float64          `json:"retry_success_rate"`
	AvgRetriesPerError  float64          `json:"avg_retries_per_error"`
	MaxRetries          int              `json:"max_retries"`
	RetryDelayHistogram map[string]int64 `json:"retry_delay_histogram"` // delay range -> count
}

// ErrorPattern represents a recurring error pattern
type ErrorPattern struct {
	Pattern     string    `json:"pattern"`
	Count       int64     `json:"count"`
	FirstSeen   time.Time `json:"first_seen"`
	LastSeen    time.Time `json:"last_seen"`
	Frequency   float64   `json:"frequency"` // errors per hour
	URLs        []string  `json:"urls,omitempty"`
	StatusCodes []int     `json:"status_codes,omitempty"`
}

// ErrorFormatter provides formatted error messages for different audiences
type ErrorFormatter struct {
	// Configuration for formatting behavior
	includeStackTrace bool
	includeDebugInfo  bool
}

// NewErrorFormatter creates a new error formatter
func NewErrorFormatter() *ErrorFormatter {
	return &ErrorFormatter{
		includeStackTrace: true,
		includeDebugInfo:  true,
	}
}

// FormatHumanMessage formats an error message for end users
func (ef *ErrorFormatter) FormatHumanMessage(err HTTPErrorInterface) string {
	if err == nil {
		return "An unknown error occurred"
	}

	return ef.formatErrorForHumans(err)
}

// FormatTechnicalMessage formats an error message for developers/logs
func (ef *ErrorFormatter) FormatTechnicalMessage(err HTTPErrorInterface) string {
	if err == nil {
		return "HTTPErrorInterface is nil"
	}

	return ef.formatErrorForTechnical(err)
}

// GenerateSuggestions provides actionable suggestions based on the error
func (ef *ErrorFormatter) GenerateSuggestions(err HTTPErrorInterface) []string {
	if err == nil {
		return []string{"Check your request configuration"}
	}

	return ef.generateErrorSuggestions(err)
}

// formatErrorForHumans formats any HTTP error for end users
func (ef *ErrorFormatter) formatErrorForHumans(err HTTPErrorInterface) string {
	statusCode := err.GetStatusCode()
	errorType := err.GetType()

	// Handle specific error types first
	switch errorType {
	case EnhancedErrorTypeTimeout:
		return "The request took too long to complete. Please try again or check your network connection."
	case EnhancedErrorTypeNetwork:
		return "Network connection failed. Please check your internet connection and try again."
	case EnhancedErrorTypeDNS:
		return "Unable to resolve the server address. Please check the URL and your internet connection."
	case EnhancedErrorTypeTLS:
		return "Secure connection failed. Please check the website's security certificate."
	case EnhancedErrorTypeConnection:
		return "Unable to connect to the server. Please check your internet connection and try again."
	}

	// Handle HTTP status codes
	switch {
	case statusCode >= 500:
		return "The server is currently experiencing issues. Please try again later."
	case statusCode == 429:
		return "Too many requests. Please wait a moment before trying again."
	case statusCode == 404:
		return "The requested resource was not found."
	case statusCode == 403:
		return "Access denied. Please check your permissions."
	case statusCode == 401:
		return "Authentication required. Please check your credentials."
	case statusCode >= 400:
		return "There was a problem with your request. Please check the details and try again."
	default:
		if statusCode > 0 {
			return fmt.Sprintf("Request failed with status %d", statusCode)
		}
		return fmt.Sprintf("Request failed: %s", err.Error())
	}
}

// formatErrorForTechnical formats any HTTP error for technical logs
func (ef *ErrorFormatter) formatErrorForTechnical(err HTTPErrorInterface) string {
	statusCode := err.GetStatusCode()
	errorType := err.GetType()
	url := err.GetURL()
	duration := err.GetDuration()

	details := []string{
		fmt.Sprintf("Type: %s", errorType.String()),
		fmt.Sprintf("Retryable: %t", err.IsRetriable()),
	}

	if duration > 0 {
		details = append(details, fmt.Sprintf("Duration: %v", duration))
	}

	if statusCode > 0 {
		return fmt.Sprintf("HTTP %d error: %s (URL: %s) [%s]",
			statusCode, err.Error(), url, strings.Join(details, ", "))
	}

	return fmt.Sprintf("HTTP %s error: %s (URL: %s) [%s]",
		errorType.String(), err.Error(), url, strings.Join(details, ", "))
}

// generateErrorSuggestions generates suggestions for any HTTP error
func (ef *ErrorFormatter) generateErrorSuggestions(err HTTPErrorInterface) []string {
	statusCode := err.GetStatusCode()
	errorType := err.GetType()
	suggestions := []string{}

	// Type-specific suggestions
	switch errorType {
	case EnhancedErrorTypeTimeout:
		suggestions = append(suggestions,
			"Increase request timeout",
			"Optimize request size",
			"Check network latency")
	case EnhancedErrorTypeNetwork:
		suggestions = append(suggestions,
			"Check network connectivity",
			"Verify DNS resolution",
			"Test with different network")
	case EnhancedErrorTypeDNS:
		suggestions = append(suggestions,
			"Verify the hostname is correct",
			"Check DNS server configuration",
			"Try using a different DNS server")
	case EnhancedErrorTypeTLS:
		suggestions = append(suggestions,
			"Check certificate validity",
			"Verify TLS configuration",
			"Update TLS version if needed")
	case EnhancedErrorTypeConnection:
		suggestions = append(suggestions,
			"Check if server is running",
			"Verify port number",
			"Check firewall settings")
	}

	// Status code specific suggestions
	switch {
	case statusCode >= 500:
		suggestions = append(suggestions,
			"Implement retry logic with exponential backoff",
			"Check server logs for more details",
			"Consider circuit breaker pattern for resilience")
	case statusCode == 429:
		suggestions = append(suggestions,
			"Implement rate limiting on client side",
			"Add retry with exponential backoff",
			"Check rate limit headers for guidance")
	case statusCode == 404:
		suggestions = append(suggestions,
			"Verify the URL path is correct",
			"Check if the resource exists",
			"Review API documentation for correct endpoints")
	case statusCode == 403:
		suggestions = append(suggestions,
			"Check API key or authentication token",
			"Verify user permissions",
			"Review access control settings")
	case statusCode == 401:
		suggestions = append(suggestions,
			"Check authentication credentials",
			"Verify token expiration",
			"Review authentication flow")
	case statusCode >= 400:
		suggestions = append(suggestions,
			"Validate request parameters",
			"Check request body format",
			"Review API documentation")
	}

	// General suggestions if no specific ones
	if len(suggestions) == 0 {
		suggestions = []string{
			"Check the error details for more information",
			"Verify your request configuration",
			"Consider implementing retry logic",
		}
	}

	return suggestions
}

// NewErrorReporter creates a new error reporter with the given configuration
func NewErrorReporter(config *ErrorReportingConfig) *ErrorReporter {
	if config == nil {
		config = DefaultErrorReportingConfig()
	}

	reporter := &ErrorReporter{
		config:      config,
		categorizer: NewEnhancedErrorCategorizer(),
		reports:     make(map[string]*ErrorReport),
		metrics:     NewErrorMetrics(),
		reportChan:  make(chan *ErrorReport, 100),
		stopChan:    make(chan struct{}),
		formatter:   NewErrorFormatter(),
	}

	// Start background processing if metrics are enabled
	if config.MetricsEnabled {
		go reporter.processReports()
	}

	return reporter
}

// NewErrorMetrics creates a new error metrics instance
func NewErrorMetrics() *ErrorMetrics {
	return &ErrorMetrics{
		ErrorsByType:       make(map[EnhancedErrorType]int64),
		ErrorsByStatusCode: make(map[int]int64),
		ErrorsBySeverity:   make(map[ErrorSeverity]int64),
		ErrorsOverTime:     make(map[string]int64),
		RetryStatistics: &RetryStatistics{
			RetryDelayHistogram: make(map[string]int64),
		},
		ErrorPatterns: make(map[string]*ErrorPattern),
	}
}

// ReportError creates a comprehensive error report
func (er *ErrorReporter) ReportError(
	err error,
	req *http.Request,
	resp *http.Response,
	duration time.Duration,
	retryAttempt int,
	totalRetries int,
) *ErrorReport {
	// Categorize the error using the enhanced categorizer
	httpErr := er.categorizer.CategorizeError(err, req, resp, duration)

	// Create error context
	context := er.createErrorContext(req, resp, retryAttempt, totalRetries)

	// Generate unique report ID
	reportID := er.generateReportID()

	// Determine severity
	severity := er.determineSeverity(httpErr, retryAttempt, totalRetries)

	// Create the error report
	report := &ErrorReport{
		ID:               reportID,
		Timestamp:        time.Now(),
		Error:            httpErr,
		Context:          context,
		Severity:         severity,
		Category:         httpErr.GetType().String(),
		HumanMessage:     er.formatter.FormatHumanMessage(httpErr),
		TechnicalMessage: er.formatter.FormatTechnicalMessage(httpErr),
		Suggestions:      er.formatter.GenerateSuggestions(httpErr),
		Metadata:         make(map[string]interface{}),
	}

	// Add additional metadata
	report.Metadata["go_version"] = runtime.Version()
	report.Metadata["client_version"] = "NeuralMeterGo/1.0"

	// Store the report
	er.mu.Lock()
	er.reports[reportID] = report
	er.mu.Unlock()

	// Send to processing channel if metrics are enabled
	if er.config.MetricsEnabled {
		select {
		case er.reportChan <- report:
		default:
			// Channel is full, skip this report to avoid blocking
		}
	}

	return report
}

// createErrorContext creates detailed context for an error
func (er *ErrorReporter) createErrorContext(
	req *http.Request,
	resp *http.Response,
	retryAttempt int,
	totalRetries int,
) *ErrorContext {
	context := &ErrorContext{
		Timestamp:    time.Now(),
		RetryAttempt: retryAttempt,
		TotalRetries: totalRetries,
		Environment:  make(map[string]interface{}),
	}

	// Add stack trace if enabled
	if er.config.EnableStackTrace {
		context.StackTrace = er.captureStackTrace()
	}

	// Add request details if enabled and available
	if er.config.EnableRequestLogging && req != nil {
		context.RequestDetails = er.createRequestDetails(req)
	}

	// Add response details if enabled and available
	if er.config.EnableResponseLogging && resp != nil {
		context.ResponseDetails = er.createResponseDetails(resp)
	}

	// Extract trace information from headers
	if req != nil {
		if traceID := req.Header.Get("X-Trace-ID"); traceID != "" {
			context.TraceID = traceID
		}
		if requestID := req.Header.Get("X-Request-ID"); requestID != "" {
			context.RequestID = requestID
		}
		if userAgent := req.Header.Get("User-Agent"); userAgent != "" {
			context.UserAgent = userAgent
		}
	}

	return context
}

// captureStackTrace captures the current stack trace
func (er *ErrorReporter) captureStackTrace() []string {
	const maxStackDepth = 10
	pcs := make([]uintptr, maxStackDepth)
	n := runtime.Callers(3, pcs) // Skip captureStackTrace, createErrorContext, ReportError

	var stackTrace []string
	frames := runtime.CallersFrames(pcs[:n])
	for {
		frame, more := frames.Next()
		if !strings.Contains(frame.File, "runtime/") {
			stackTrace = append(stackTrace, fmt.Sprintf("%s:%d %s", frame.File, frame.Line, frame.Function))
		}
		if !more {
			break
		}
	}

	return stackTrace
}

// createRequestDetails creates detailed request information
func (er *ErrorReporter) createRequestDetails(req *http.Request) *RequestDetails {
	details := &RequestDetails{
		Method:  req.Method,
		URL:     req.URL.String(),
		Headers: make(map[string]string),
	}

	// Copy headers (excluding sensitive ones)
	for key, values := range req.Header {
		if !er.isSensitiveHeader(key) && len(values) > 0 {
			details.Headers[key] = values[0]
		}
	}

	// Add body preview if available and not too large
	if req.Body != nil && req.ContentLength > 0 && req.ContentLength <= int64(er.config.MaxBodyLogSize) {
		// Note: In a real implementation, we'd need to be careful about reading the body
		// as it can only be read once. This would require buffering or using a TeeReader.
		details.BodySize = req.ContentLength
		details.BodyPreview = "[Body content not captured to avoid interfering with request]"
	}

	// Add timeout if set in context
	if deadline, ok := req.Context().Deadline(); ok {
		details.Timeout = time.Until(deadline)
	}

	return details
}

// createResponseDetails creates detailed response information
func (er *ErrorReporter) createResponseDetails(resp *http.Response) *ResponseDetails {
	details := &ResponseDetails{
		StatusCode: resp.StatusCode,
		Status:     resp.Status,
		Headers:    make(map[string]string),
		BodySize:   resp.ContentLength,
	}

	// Copy headers (excluding sensitive ones)
	for key, values := range resp.Header {
		if !er.isSensitiveHeader(key) && len(values) > 0 {
			details.Headers[key] = values[0]
		}
	}

	// Extract content type and encoding
	details.ContentType = resp.Header.Get("Content-Type")
	details.ContentEncoding = resp.Header.Get("Content-Encoding")

	return details
}

// isSensitiveHeader checks if a header contains sensitive information
func (er *ErrorReporter) isSensitiveHeader(headerName string) bool {
	sensitiveHeaders := []string{
		"authorization",
		"cookie",
		"set-cookie",
		"x-api-key",
		"x-auth-token",
		"proxy-authorization",
	}

	lowerHeader := strings.ToLower(headerName)
	for _, sensitive := range sensitiveHeaders {
		if lowerHeader == sensitive {
			return true
		}
	}

	return false
}

// generateReportID generates a unique identifier for an error report
func (er *ErrorReporter) generateReportID() string {
	return fmt.Sprintf("err_%d_%d", time.Now().UnixNano(), runtime.NumGoroutine())
}

// determineSeverity determines the severity of an error
func (er *ErrorReporter) determineSeverity(
	httpErr HTTPErrorInterface,
	retryAttempt int,
	totalRetries int,
) ErrorSeverity {
	// Critical errors
	if !httpErr.IsRetriable() {
		switch httpErr.GetType() {
		case EnhancedErrorTypeConfiguration, EnhancedErrorTypeRequest:
			return SeverityCritical
		}
	}

	// High severity for repeated failures
	if retryAttempt >= totalRetries {
		return SeverityHigh
	}

	// Medium severity for server errors
	statusCode := httpErr.GetStatusCode()
	if statusCode >= 500 {
		return SeverityMedium
	}

	// Low severity for client errors and retriable issues
	return SeverityLow
}

// processReports processes error reports in the background for metrics collection
func (er *ErrorReporter) processReports() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case report := <-er.reportChan:
			er.updateMetrics(report)
		case <-ticker.C:
			er.cleanupOldReports()
		case <-er.stopChan:
			return
		}
	}
}

// updateMetrics updates error metrics with a new report
func (er *ErrorReporter) updateMetrics(report *ErrorReport) {
	er.metrics.mu.Lock()
	defer er.metrics.mu.Unlock()

	er.metrics.TotalErrors++
	er.metrics.ErrorsByType[report.Error.GetType()]++
	er.metrics.ErrorsBySeverity[report.Severity]++

	if statusCode := report.Error.GetStatusCode(); statusCode > 0 {
		er.metrics.ErrorsByStatusCode[statusCode]++
	}

	// Update time-based metrics
	timeKey := report.Timestamp.Format("2006-01-02T15")
	er.metrics.ErrorsOverTime[timeKey]++

	// Update error patterns
	er.updateErrorPatterns(report)

	// Update retry statistics
	if report.Context.RetryAttempt > 0 {
		er.metrics.RetryStatistics.TotalRetries++
		if report.Severity == SeverityLow {
			er.metrics.RetryStatistics.SuccessfulRetries++
		} else {
			er.metrics.RetryStatistics.FailedRetries++
		}
	}
}

// updateErrorPatterns identifies and tracks recurring error patterns
func (er *ErrorReporter) updateErrorPatterns(report *ErrorReport) {
	// Create a pattern key based on error type and status code
	patternKey := fmt.Sprintf("%s_%d", report.Error.GetType().String(), report.Error.GetStatusCode())

	pattern, exists := er.metrics.ErrorPatterns[patternKey]
	if !exists {
		pattern = &ErrorPattern{
			Pattern:     patternKey,
			Count:       0,
			FirstSeen:   report.Timestamp,
			URLs:        make([]string, 0),
			StatusCodes: make([]int, 0),
		}
		er.metrics.ErrorPatterns[patternKey] = pattern
	}

	pattern.Count++
	pattern.LastSeen = report.Timestamp

	// Add URL if not already present
	url := report.Error.GetURL()
	if url != "" {
		found := false
		for _, existingURL := range pattern.URLs {
			if existingURL == url {
				found = true
				break
			}
		}
		if !found && len(pattern.URLs) < 10 { // Limit to 10 URLs per pattern
			pattern.URLs = append(pattern.URLs, url)
		}
	}

	// Add status code if not already present
	statusCode := report.Error.GetStatusCode()
	if statusCode > 0 {
		found := false
		for _, existingCode := range pattern.StatusCodes {
			if existingCode == statusCode {
				found = true
				break
			}
		}
		if !found && len(pattern.StatusCodes) < 10 { // Limit to 10 status codes per pattern
			pattern.StatusCodes = append(pattern.StatusCodes, statusCode)
		}
	}

	// Calculate frequency (errors per hour)
	duration := time.Since(pattern.FirstSeen)
	if duration > 0 {
		pattern.Frequency = float64(pattern.Count) / duration.Hours()
	}
}

// cleanupOldReports removes old error reports based on retention policy
func (er *ErrorReporter) cleanupOldReports() {
	cutoff := time.Now().Add(-er.config.RetentionPeriod)

	er.mu.Lock()
	defer er.mu.Unlock()

	for id, report := range er.reports {
		if report.Timestamp.Before(cutoff) {
			delete(er.reports, id)
		}
	}
}

// GetReport retrieves an error report by ID
func (er *ErrorReporter) GetReport(id string) (*ErrorReport, bool) {
	er.mu.RLock()
	defer er.mu.RUnlock()

	report, exists := er.reports[id]
	return report, exists
}

// GetReports retrieves all error reports
func (er *ErrorReporter) GetReports() map[string]*ErrorReport {
	er.mu.RLock()
	defer er.mu.RUnlock()

	// Create a copy to avoid race conditions
	reports := make(map[string]*ErrorReport, len(er.reports))
	for id, report := range er.reports {
		reports[id] = report
	}

	return reports
}

// GetMetrics returns the current error metrics
func (er *ErrorReporter) GetMetrics() *ErrorMetrics {
	er.metrics.mu.RLock()
	defer er.metrics.mu.RUnlock()

	// Create a deep copy to avoid race conditions
	metricsCopy := &ErrorMetrics{
		TotalErrors:        er.metrics.TotalErrors,
		ErrorsByType:       make(map[EnhancedErrorType]int64),
		ErrorsByStatusCode: make(map[int]int64),
		ErrorsBySeverity:   make(map[ErrorSeverity]int64),
		ErrorsOverTime:     make(map[string]int64),
		AvgErrorDuration:   er.metrics.AvgErrorDuration,
		RetryStatistics:    er.metrics.RetryStatistics,
		ErrorPatterns:      make(map[string]*ErrorPattern),
	}

	for k, v := range er.metrics.ErrorsByType {
		metricsCopy.ErrorsByType[k] = v
	}
	for k, v := range er.metrics.ErrorsByStatusCode {
		metricsCopy.ErrorsByStatusCode[k] = v
	}
	for k, v := range er.metrics.ErrorsBySeverity {
		metricsCopy.ErrorsBySeverity[k] = v
	}
	for k, v := range er.metrics.ErrorsOverTime {
		metricsCopy.ErrorsOverTime[k] = v
	}
	for k, v := range er.metrics.ErrorPatterns {
		metricsCopy.ErrorPatterns[k] = v
	}

	return metricsCopy
}

// Stop gracefully stops the error reporter
func (er *ErrorReporter) Stop() {
	close(er.stopChan)
}

// GetReportsAsJSON returns all error reports as JSON
func (er *ErrorReporter) GetReportsAsJSON() ([]byte, error) {
	reports := er.GetReports()
	return json.MarshalIndent(reports, "", "  ")
}

// GetMetricsAsJSON returns error metrics as JSON
func (er *ErrorReporter) GetMetricsAsJSON() ([]byte, error) {
	metrics := er.GetMetrics()
	return json.MarshalIndent(metrics, "", "  ")
}
