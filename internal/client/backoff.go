package client

import (
	"math"
	"math/rand"
	"sync"
	"time"
)

// BackoffStrategy defines the interface for backoff calculation strategies
type BackoffStrategy interface {
	// CalculateBackoff computes the delay for a given attempt number
	CalculateBackoff(attempt int, baseDelay time.Duration) time.Duration
	// Name returns the name of the backoff strategy
	Name() string
	// Reset resets any internal state (for adaptive strategies)
	Reset()
}

// JitterStrategy defines different jitter algorithms to prevent thundering herd
type JitterStrategy int

const (
	NoJitter           JitterStrategy = iota
	FullJitter                        // Randomize between 0 and calculated delay
	EqualJitter                       // Half base delay + half random up to calculated delay
	DecorrelatedJitter                // Use previous delay as input for randomization
)

func (j JitterStrategy) String() string {
	switch j {
	case NoJitter:
		return "none"
	case FullJitter:
		return "full"
	case EqualJitter:
		return "equal"
	case DecorrelatedJitter:
		return "decorrelated"
	default:
		return "unknown"
	}
}

// BackoffConfig provides comprehensive configuration for backoff strategies
type BackoffConfig struct {
	// Basic configuration
	BaseDelay  time.Duration // Base delay for the first retry
	MaxDelay   time.Duration // Maximum delay allowed
	Multiplier float64       // Backoff multiplier for exponential strategies

	// Jitter configuration
	JitterStrategy JitterStrategy // Type of jitter to apply
	JitterFactor   float64        // Jitter factor (0.0 to 1.0)

	// Adaptive configuration
	EnableAdaptive    bool    // Enable adaptive backoff based on success/failure patterns
	AdaptiveFactor    float64 // How aggressively to adapt (0.1 = conservative, 1.0 = aggressive)
	AdaptiveWindow    int     // Number of recent attempts to consider for adaptation
	AdaptiveMinFactor float64 // Minimum adaptive multiplier
	AdaptiveMaxFactor float64 // Maximum adaptive multiplier

	// Temperature-based configuration
	EnableTemperature bool    // Enable temperature-based backoff adjustment
	TemperatureFactor float64 // How much temperature affects backoff
	CoolingRate       float64 // Rate at which temperature decreases over time
}

// DefaultBackoffConfig returns a sensible default backoff configuration
func DefaultBackoffConfig() *BackoffConfig {
	return &BackoffConfig{
		BaseDelay:         100 * time.Millisecond,
		MaxDelay:          30 * time.Second,
		Multiplier:        2.0,
		JitterStrategy:    EqualJitter,
		JitterFactor:      0.1,
		EnableAdaptive:    true,
		AdaptiveFactor:    0.2,
		AdaptiveWindow:    10,
		AdaptiveMinFactor: 0.5,
		AdaptiveMaxFactor: 3.0,
		EnableTemperature: true,
		TemperatureFactor: 0.3,
		CoolingRate:       0.05,
	}
}

// =============================================================================
// BACKOFF STRATEGY IMPLEMENTATIONS
// =============================================================================

// LinearBackoffStrategy implements linear backoff (delay increases linearly)
type LinearBackoffStrategy struct {
	config      *BackoffConfig
	jitterState *JitterState
	name        string
}

// ExponentialBackoffStrategy implements exponential backoff with advanced jitter
type ExponentialBackoffStrategy struct {
	config      *BackoffConfig
	jitterState *JitterState
	name        string
}

// PolynomialBackoffStrategy implements polynomial backoff (configurable polynomial degree)
type PolynomialBackoffStrategy struct {
	config      *BackoffConfig
	jitterState *JitterState
	degree      float64 // Polynomial degree (2.0 = quadratic, 3.0 = cubic, etc.)
	name        string
}

// AdaptiveBackoffStrategy learns from success/failure patterns to adjust backoff
type AdaptiveBackoffStrategy struct {
	config        *BackoffConfig
	baseStrategy  BackoffStrategy
	jitterState   *JitterState
	adaptiveState *AdaptiveState
	name          string
}

// TemperatureBackoffStrategy adjusts backoff based on system "temperature" (stress level)
type TemperatureBackoffStrategy struct {
	config           *BackoffConfig
	baseStrategy     BackoffStrategy
	jitterState      *JitterState
	temperatureState *TemperatureState
	name             string
}

// JitterState maintains state for decorrelated jitter
type JitterState struct {
	lastDelay time.Duration
	mu        sync.RWMutex
}

// AdaptiveState tracks success/failure patterns for adaptive backoff
type AdaptiveState struct {
	attempts      []AttemptResult // Recent attempt results
	currentFactor float64         // Current adaptive multiplier
	successCount  int             // Recent successes
	failureCount  int             // Recent failures
	mu            sync.RWMutex    // Mutex for thread-safe access
}

// AttemptResult records the result of a retry attempt for adaptive learning
type AttemptResult struct {
	Timestamp time.Time
	Success   bool
	Delay     time.Duration
}

// TemperatureState tracks system temperature for temperature-based backoff
type TemperatureState struct {
	temperature    float64     // Current system temperature (0.0 = cool, 1.0 = hot)
	lastUpdate     time.Time   // Last time temperature was updated
	failureHistory []time.Time // Recent failure timestamps
	mu             sync.RWMutex
}

// NewLinearBackoffStrategy creates a new linear backoff strategy
func NewLinearBackoffStrategy(config *BackoffConfig) BackoffStrategy {
	return &LinearBackoffStrategy{
		config:      config,
		jitterState: &JitterState{},
		name:        "linear",
	}
}

// NewExponentialBackoffStrategy creates a new exponential backoff strategy
func NewExponentialBackoffStrategy(config *BackoffConfig) BackoffStrategy {
	return &ExponentialBackoffStrategy{
		config:      config,
		jitterState: &JitterState{},
		name:        "exponential",
	}
}

// NewPolynomialBackoffStrategy creates a new polynomial backoff strategy
func NewPolynomialBackoffStrategy(config *BackoffConfig, degree float64) BackoffStrategy {
	return &PolynomialBackoffStrategy{
		config:      config,
		jitterState: &JitterState{},
		degree:      degree,
		name:        "polynomial",
	}
}

// NewAdaptiveBackoffStrategy creates a new adaptive backoff strategy
func NewAdaptiveBackoffStrategy(config *BackoffConfig, baseStrategy BackoffStrategy) BackoffStrategy {
	return &AdaptiveBackoffStrategy{
		config:        config,
		baseStrategy:  baseStrategy,
		jitterState:   &JitterState{},
		adaptiveState: NewAdaptiveState(),
		name:          "adaptive_" + baseStrategy.Name(),
	}
}

// NewTemperatureBackoffStrategy creates a new temperature-based backoff strategy
func NewTemperatureBackoffStrategy(config *BackoffConfig, baseStrategy BackoffStrategy) BackoffStrategy {
	return &TemperatureBackoffStrategy{
		config:           config,
		baseStrategy:     baseStrategy,
		jitterState:      &JitterState{},
		temperatureState: NewTemperatureState(),
		name:             "temperature_" + baseStrategy.Name(),
	}
}

// NewAdaptiveState creates a new adaptive state tracker
func NewAdaptiveState() *AdaptiveState {
	return &AdaptiveState{
		attempts:      make([]AttemptResult, 0),
		currentFactor: 1.0,
	}
}

// NewTemperatureState creates a new temperature state tracker
func NewTemperatureState() *TemperatureState {
	return &TemperatureState{
		temperature:    0.0,
		lastUpdate:     time.Now(),
		failureHistory: make([]time.Time, 0),
	}
}

// =============================================================================
// LINEAR BACKOFF IMPLEMENTATION
// =============================================================================

func (s *LinearBackoffStrategy) Name() string {
	return s.name
}

func (s *LinearBackoffStrategy) CalculateBackoff(attempt int, baseDelay time.Duration) time.Duration {
	// Linear backoff: delay = baseDelay * (attempt + 1)
	delay := time.Duration(int64(baseDelay) * int64(attempt+1))

	// Apply maximum delay limit
	if delay > s.config.MaxDelay {
		delay = s.config.MaxDelay
	}

	// Apply jitter
	return s.applyJitter(delay)
}

func (s *LinearBackoffStrategy) Reset() {
	s.jitterState.mu.Lock()
	defer s.jitterState.mu.Unlock()
	s.jitterState.lastDelay = 0
}

// =============================================================================
// EXPONENTIAL BACKOFF IMPLEMENTATION
// =============================================================================

func (s *ExponentialBackoffStrategy) Name() string {
	return s.name
}

func (s *ExponentialBackoffStrategy) CalculateBackoff(attempt int, baseDelay time.Duration) time.Duration {
	// Exponential backoff: delay = baseDelay * multiplier^attempt
	delay := time.Duration(float64(baseDelay) * math.Pow(s.config.Multiplier, float64(attempt)))

	// Apply maximum delay limit
	if delay > s.config.MaxDelay {
		delay = s.config.MaxDelay
	}

	// Apply jitter
	return s.applyJitter(delay)
}

func (s *ExponentialBackoffStrategy) Reset() {
	s.jitterState.mu.Lock()
	defer s.jitterState.mu.Unlock()
	s.jitterState.lastDelay = 0
}

// =============================================================================
// POLYNOMIAL BACKOFF IMPLEMENTATION
// =============================================================================

func (s *PolynomialBackoffStrategy) Name() string {
	return s.name
}

func (s *PolynomialBackoffStrategy) CalculateBackoff(attempt int, baseDelay time.Duration) time.Duration {
	// Polynomial backoff: delay = baseDelay * (attempt + 1)^degree
	multiplier := math.Pow(float64(attempt+1), s.degree)
	delay := time.Duration(float64(baseDelay) * multiplier)

	// Apply maximum delay limit
	if delay > s.config.MaxDelay {
		delay = s.config.MaxDelay
	}

	// Apply jitter
	return s.applyJitter(delay)
}

func (s *PolynomialBackoffStrategy) Reset() {
	s.jitterState.mu.Lock()
	defer s.jitterState.mu.Unlock()
	s.jitterState.lastDelay = 0
}

// =============================================================================
// ADAPTIVE BACKOFF IMPLEMENTATION
// =============================================================================

func (s *AdaptiveBackoffStrategy) Name() string {
	return s.name
}

func (s *AdaptiveBackoffStrategy) CalculateBackoff(attempt int, baseDelay time.Duration) time.Duration {
	// Get base delay from underlying strategy
	baseBackoff := s.baseStrategy.CalculateBackoff(attempt, baseDelay)

	// Apply adaptive factor
	adaptiveFactor := s.adaptiveState.getCurrentFactor()
	adaptiveDelay := time.Duration(float64(baseBackoff) * adaptiveFactor)

	// Apply maximum delay limit
	if adaptiveDelay > s.config.MaxDelay {
		adaptiveDelay = s.config.MaxDelay
	}

	// Apply jitter
	return s.applyJitter(adaptiveDelay)
}

func (s *AdaptiveBackoffStrategy) Reset() {
	s.baseStrategy.Reset()
	s.jitterState.mu.Lock()
	defer s.jitterState.mu.Unlock()
	s.jitterState.lastDelay = 0

	s.adaptiveState.mu.Lock()
	defer s.adaptiveState.mu.Unlock()
	s.adaptiveState.attempts = s.adaptiveState.attempts[:0]
	s.adaptiveState.currentFactor = 1.0
	s.adaptiveState.successCount = 0
	s.adaptiveState.failureCount = 0
}

// RecordAttempt records an attempt result for adaptive learning
func (s *AdaptiveBackoffStrategy) RecordAttempt(success bool, delay time.Duration) {
	s.adaptiveState.recordAttempt(success, delay, s.config)
}

// =============================================================================
// TEMPERATURE BACKOFF IMPLEMENTATION
// =============================================================================

func (s *TemperatureBackoffStrategy) Name() string {
	return s.name
}

func (s *TemperatureBackoffStrategy) CalculateBackoff(attempt int, baseDelay time.Duration) time.Duration {
	// Get base delay from underlying strategy
	baseBackoff := s.baseStrategy.CalculateBackoff(attempt, baseDelay)

	// Apply temperature factor
	temperature := s.temperatureState.getCurrentTemperature()
	temperatureFactor := 1.0 + (temperature * s.config.TemperatureFactor)
	temperatureDelay := time.Duration(float64(baseBackoff) * temperatureFactor)

	// Apply maximum delay limit
	if temperatureDelay > s.config.MaxDelay {
		temperatureDelay = s.config.MaxDelay
	}

	// Apply jitter
	return s.applyJitter(temperatureDelay)
}

func (s *TemperatureBackoffStrategy) Reset() {
	s.baseStrategy.Reset()
	s.jitterState.mu.Lock()
	defer s.jitterState.mu.Unlock()
	s.jitterState.lastDelay = 0

	s.temperatureState.mu.Lock()
	defer s.temperatureState.mu.Unlock()
	s.temperatureState.temperature = 0.0
	s.temperatureState.failureHistory = s.temperatureState.failureHistory[:0]
}

// RecordFailure records a failure for temperature calculation
func (s *TemperatureBackoffStrategy) RecordFailure() {
	s.temperatureState.recordFailure(s.config)
}

// =============================================================================
// JITTER IMPLEMENTATION
// =============================================================================

// applyJitter applies the configured jitter strategy to a delay
func (s *LinearBackoffStrategy) applyJitter(delay time.Duration) time.Duration {
	return applyJitterToDelay(delay, s.config.JitterStrategy, s.config.JitterFactor, s.jitterState)
}

func (s *ExponentialBackoffStrategy) applyJitter(delay time.Duration) time.Duration {
	return applyJitterToDelay(delay, s.config.JitterStrategy, s.config.JitterFactor, s.jitterState)
}

func (s *PolynomialBackoffStrategy) applyJitter(delay time.Duration) time.Duration {
	return applyJitterToDelay(delay, s.config.JitterStrategy, s.config.JitterFactor, s.jitterState)
}

func (s *AdaptiveBackoffStrategy) applyJitter(delay time.Duration) time.Duration {
	return applyJitterToDelay(delay, s.config.JitterStrategy, s.config.JitterFactor, s.jitterState)
}

func (s *TemperatureBackoffStrategy) applyJitter(delay time.Duration) time.Duration {
	return applyJitterToDelay(delay, s.config.JitterStrategy, s.config.JitterFactor, s.jitterState)
}

// applyJitterToDelay applies jitter based on the strategy
func applyJitterToDelay(delay time.Duration, strategy JitterStrategy, factor float64, state *JitterState) time.Duration {
	if strategy == NoJitter {
		return delay
	}

	delayFloat := float64(delay)

	switch strategy {
	case FullJitter:
		// Random value between 0 and delay
		jitteredDelay := rand.Float64() * delayFloat
		return time.Duration(jitteredDelay)

	case EqualJitter:
		// Half base delay + half random up to delay
		halfDelay := delayFloat * 0.5
		jitterRange := delayFloat * 0.5
		jitteredDelay := halfDelay + (rand.Float64() * jitterRange)
		return time.Duration(jitteredDelay)

	case DecorrelatedJitter:
		// Use previous delay as input for randomization
		state.mu.Lock()
		defer state.mu.Unlock()

		lastDelayFloat := float64(state.lastDelay)
		if lastDelayFloat == 0 {
			lastDelayFloat = delayFloat
		}

		// Random value between base delay and 3 * last delay
		minDelay := delayFloat
		maxDelay := math.Min(delayFloat*3, float64(30*time.Second)) // Cap at 30 seconds
		jitteredDelay := minDelay + rand.Float64()*(maxDelay-minDelay)

		state.lastDelay = time.Duration(jitteredDelay)
		return time.Duration(jitteredDelay)

	default:
		return delay
	}
}

// =============================================================================
// ADAPTIVE STATE MANAGEMENT
// =============================================================================

func (as *AdaptiveState) getCurrentFactor() float64 {
	as.mu.RLock()
	defer as.mu.RUnlock()
	return as.currentFactor
}

func (as *AdaptiveState) recordAttempt(success bool, delay time.Duration, config *BackoffConfig) {
	as.mu.Lock()
	defer as.mu.Unlock()

	// Add new attempt result
	result := AttemptResult{
		Timestamp: time.Now(),
		Success:   success,
		Delay:     delay,
	}
	as.attempts = append(as.attempts, result)

	// Keep only recent attempts within the adaptive window
	if len(as.attempts) > config.AdaptiveWindow {
		as.attempts = as.attempts[1:]
	}

	// Update success/failure counts
	if success {
		as.successCount++
	} else {
		as.failureCount++
	}

	// Recalculate adaptive factor
	as.updateAdaptiveFactor(config)
}

func (as *AdaptiveState) updateAdaptiveFactor(config *BackoffConfig) {
	if len(as.attempts) < 3 {
		return // Need enough samples
	}

	// Calculate success rate
	successRate := float64(as.successCount) / float64(len(as.attempts))

	// Adjust factor based on success rate
	// High success rate -> reduce delays
	// Low success rate -> increase delays
	if successRate > 0.8 {
		// High success rate, reduce delays
		as.currentFactor *= (1.0 - config.AdaptiveFactor*0.1)
	} else if successRate < 0.3 {
		// Low success rate, increase delays
		as.currentFactor *= (1.0 + config.AdaptiveFactor*0.2)
	}

	// Apply bounds
	if as.currentFactor < config.AdaptiveMinFactor {
		as.currentFactor = config.AdaptiveMinFactor
	}
	if as.currentFactor > config.AdaptiveMaxFactor {
		as.currentFactor = config.AdaptiveMaxFactor
	}
}

// =============================================================================
// TEMPERATURE STATE MANAGEMENT
// =============================================================================

func (ts *TemperatureState) getCurrentTemperature() float64 {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	// Apply cooling over time
	timeSinceUpdate := time.Since(ts.lastUpdate)
	coolingAmount := timeSinceUpdate.Seconds() * 0.01 // Cool down by 1% per second
	ts.temperature = math.Max(0.0, ts.temperature-coolingAmount)
	ts.lastUpdate = time.Now()

	return ts.temperature
}

func (ts *TemperatureState) recordFailure(config *BackoffConfig) {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	now := time.Now()
	ts.failureHistory = append(ts.failureHistory, now)

	// Keep only recent failures (last 5 minutes)
	cutoff := now.Add(-5 * time.Minute)
	i := 0
	for i < len(ts.failureHistory) && ts.failureHistory[i].Before(cutoff) {
		i++
	}
	ts.failureHistory = ts.failureHistory[i:]

	// Calculate temperature based on recent failure rate
	recentFailures := len(ts.failureHistory)
	if recentFailures > 0 {
		// More recent failures = higher temperature
		failureRate := float64(recentFailures) / 300.0 // failures per second over 5 minutes
		ts.temperature = math.Min(1.0, ts.temperature+failureRate*config.TemperatureFactor)
	}

	ts.lastUpdate = now
}

// =============================================================================
// BACKOFF STRATEGY FACTORY
// =============================================================================

// BackoffStrategyType defines the types of backoff strategies available
type BackoffStrategyType int

const (
	LinearBackoff BackoffStrategyType = iota
	ExponentialBackoff
	PolynomialBackoff
	AdaptiveLinearBackoff
	AdaptiveExponentialBackoff
	TemperatureLinearBackoff
	TemperatureExponentialBackoff
)

func (t BackoffStrategyType) String() string {
	switch t {
	case LinearBackoff:
		return "linear"
	case ExponentialBackoff:
		return "exponential"
	case PolynomialBackoff:
		return "polynomial"
	case AdaptiveLinearBackoff:
		return "adaptive_linear"
	case AdaptiveExponentialBackoff:
		return "adaptive_exponential"
	case TemperatureLinearBackoff:
		return "temperature_linear"
	case TemperatureExponentialBackoff:
		return "temperature_exponential"
	default:
		return "unknown"
	}
}

// CreateBackoffStrategy creates a backoff strategy of the specified type
func CreateBackoffStrategy(strategyType BackoffStrategyType, config *BackoffConfig) BackoffStrategy {
	switch strategyType {
	case LinearBackoff:
		return NewLinearBackoffStrategy(config)
	case ExponentialBackoff:
		return NewExponentialBackoffStrategy(config)
	case PolynomialBackoff:
		return NewPolynomialBackoffStrategy(config, 2.0) // Quadratic by default
	case AdaptiveLinearBackoff:
		baseStrategy := NewLinearBackoffStrategy(config)
		return NewAdaptiveBackoffStrategy(config, baseStrategy)
	case AdaptiveExponentialBackoff:
		baseStrategy := NewExponentialBackoffStrategy(config)
		return NewAdaptiveBackoffStrategy(config, baseStrategy)
	case TemperatureLinearBackoff:
		baseStrategy := NewLinearBackoffStrategy(config)
		return NewTemperatureBackoffStrategy(config, baseStrategy)
	case TemperatureExponentialBackoff:
		baseStrategy := NewExponentialBackoffStrategy(config)
		return NewTemperatureBackoffStrategy(config, baseStrategy)
	default:
		return NewExponentialBackoffStrategy(config)
	}
}
