package client

import (
	"math/rand"
	"time"
)

// =============================================================================
// ERROR-SPECIFIC RETRY POLICIES
// =============================================================================

// ErrorSpecificRetryPolicy implements intelligent retry logic based on error categorization
type ErrorSpecificRetryPolicy struct {
	config *RetryConfig
	// Error type specific configurations
	networkErrorPolicy    RetryPolicyConfig
	timeoutErrorPolicy    RetryPolicyConfig
	connectionErrorPolicy RetryPolicyConfig
	dnsErrorPolicy        RetryPolicyConfig
	tlsErrorPolicy        RetryPolicyConfig
	statusCodeErrorPolicy RetryPolicyConfig
	serverErrorPolicy     RetryPolicyConfig
	rateLimitPolicy       RetryPolicyConfig

	// Default fallback policy
	defaultPolicy RetryPolicyConfig
}

// RetryPolicyConfig defines retry behavior for specific error types
type RetryPolicyConfig struct {
	MaxRetries     int           // Maximum retry attempts for this error type
	BaseDelay      time.Duration // Base delay for this error type
	MaxDelay       time.Duration // Maximum delay for this error type
	BackoffFactor  float64       // Backoff multiplier
	JitterEnabled  bool          // Whether to apply jitter
	CircuitBreaker bool          // Whether to use circuit breaker
	RespectHeaders bool          // Whether to respect Retry-After headers
}

// DefaultErrorSpecificRetryPolicy creates a new error-specific retry policy with sensible defaults
func NewErrorSpecificRetryPolicy(config *RetryConfig) RetryPolicy {
	return &ErrorSpecificRetryPolicy{
		config: config,

		// Network errors - aggressive retries with progressive backoff
		networkErrorPolicy: RetryPolicyConfig{
			MaxRetries:     5,
			BaseDelay:      500 * time.Millisecond,
			MaxDelay:       10 * time.Second,
			BackoffFactor:  2.0,
			JitterEnabled:  true,
			CircuitBreaker: true,
			RespectHeaders: false,
		},

		// Timeout errors - moderate retries with longer delays
		timeoutErrorPolicy: RetryPolicyConfig{
			MaxRetries:     3,
			BaseDelay:      1 * time.Second,
			MaxDelay:       15 * time.Second,
			BackoffFactor:  2.5,
			JitterEnabled:  true,
			CircuitBreaker: true,
			RespectHeaders: false,
		},

		// Connection errors - quick retries, then back off
		connectionErrorPolicy: RetryPolicyConfig{
			MaxRetries:     4,
			BaseDelay:      250 * time.Millisecond,
			MaxDelay:       5 * time.Second,
			BackoffFactor:  2.0,
			JitterEnabled:  true,
			CircuitBreaker: true,
			RespectHeaders: false,
		},

		// DNS errors - longer delays, fewer retries
		dnsErrorPolicy: RetryPolicyConfig{
			MaxRetries:     2,
			BaseDelay:      2 * time.Second,
			MaxDelay:       20 * time.Second,
			BackoffFactor:  3.0,
			JitterEnabled:  true,
			CircuitBreaker: false, // DNS issues usually don't benefit from circuit breaking
			RespectHeaders: false,
		},

		// TLS errors - very conservative, likely configuration issues
		tlsErrorPolicy: RetryPolicyConfig{
			MaxRetries:     1,
			BaseDelay:      1 * time.Second,
			MaxDelay:       5 * time.Second,
			BackoffFactor:  2.0,
			JitterEnabled:  false,
			CircuitBreaker: false,
			RespectHeaders: false,
		},

		// Status code errors - respect server preferences
		statusCodeErrorPolicy: RetryPolicyConfig{
			MaxRetries:     3,
			BaseDelay:      1 * time.Second,
			MaxDelay:       30 * time.Second,
			BackoffFactor:  2.0,
			JitterEnabled:  true,
			CircuitBreaker: true,
			RespectHeaders: true, // Important for status code errors
		},

		// Server errors (5xx) - moderate retries with respect for server state
		serverErrorPolicy: RetryPolicyConfig{
			MaxRetries:     4,
			BaseDelay:      2 * time.Second,
			MaxDelay:       30 * time.Second,
			BackoffFactor:  2.0,
			JitterEnabled:  true,
			CircuitBreaker: true,
			RespectHeaders: true,
		},

		// Rate limiting (429) - always respect Retry-After header
		rateLimitPolicy: RetryPolicyConfig{
			MaxRetries:     6,
			BaseDelay:      5 * time.Second,
			MaxDelay:       300 * time.Second, // 5 minutes max
			BackoffFactor:  1.5,               // Conservative backoff
			JitterEnabled:  true,
			CircuitBreaker: false, // Don't circuit break on rate limits
			RespectHeaders: true,  // Critical for rate limiting
		},

		// Default policy for unknown/uncategorized errors
		defaultPolicy: RetryPolicyConfig{
			MaxRetries:     2,
			BaseDelay:      1 * time.Second,
			MaxDelay:       10 * time.Second,
			BackoffFactor:  2.0,
			JitterEnabled:  true,
			CircuitBreaker: true,
			RespectHeaders: true,
		},
	}
}

// Name returns the name of this retry policy
func (p *ErrorSpecificRetryPolicy) Name() string {
	return "ErrorSpecificRetryPolicy"
}

// ShouldRetry determines whether a request should be retried based on error type analysis
func (p *ErrorSpecificRetryPolicy) ShouldRetry(ctx *RetryContext) bool {
	// Get the appropriate policy configuration for this error
	policyConfig := p.getPolicyForError(ctx.LastError, ctx.LastStatusCode)

	// Check if we've exceeded max retries for this error type
	if ctx.Attempt >= policyConfig.MaxRetries {
		return false
	}

	// If we have an HTTPErrorInterface, use its IsRetriable method
	if httpErr, ok := ctx.LastError.(HTTPErrorInterface); ok {
		return httpErr.IsRetriable()
	}

	// For non-HTTP errors, use basic retry logic
	if ctx.LastError != nil {
		return isTransientError(ctx.LastError)
	}

	// For status code errors, check if the status code is retryable
	if ctx.LastStatusCode > 0 {
		return isRetriableStatusCode(ctx.LastStatusCode)
	}

	return false
}

// CalculateDelay computes the delay before the next retry attempt based on error type
func (p *ErrorSpecificRetryPolicy) CalculateDelay(ctx *RetryContext) time.Duration {
	// Get the appropriate policy configuration for this error
	policyConfig := p.getPolicyForError(ctx.LastError, ctx.LastStatusCode)

	// If the error is an HTTPErrorInterface and we should respect headers, check for suggested delay
	if policyConfig.RespectHeaders {
		if httpErr, ok := ctx.LastError.(HTTPErrorInterface); ok {
			if suggestedDelay := httpErr.GetRetryDelay(); suggestedDelay > 0 {
				// Use suggested delay but cap it at our max delay
				if suggestedDelay > policyConfig.MaxDelay {
					return policyConfig.MaxDelay
				}

				// Apply jitter if enabled
				if policyConfig.JitterEnabled {
					return applyJitter(suggestedDelay, 0.1)
				}
				return suggestedDelay
			}
		}
	}

	// Calculate exponential backoff delay
	delay := calculateExponentialBackoff(
		ctx.Attempt,
		policyConfig.BaseDelay,
		policyConfig.MaxDelay,
		policyConfig.BackoffFactor,
	)

	// Apply jitter if enabled
	if policyConfig.JitterEnabled {
		delay = applyJitter(delay, 0.15) // 15% jitter for error-specific policies
	}

	return delay
}

// getPolicyForError returns the appropriate retry policy configuration based on error type
func (p *ErrorSpecificRetryPolicy) getPolicyForError(err error, statusCode int) RetryPolicyConfig {
	// If we have an HTTPErrorInterface, use its type for policy selection
	if httpErr, ok := err.(HTTPErrorInterface); ok {
		switch httpErr.GetType() {
		case EnhancedErrorTypeNetwork:
			return p.networkErrorPolicy
		case EnhancedErrorTypeTimeout:
			return p.timeoutErrorPolicy
		case EnhancedErrorTypeConnection:
			return p.connectionErrorPolicy
		case EnhancedErrorTypeDNS:
			return p.dnsErrorPolicy
		case EnhancedErrorTypeTLS:
			return p.tlsErrorPolicy
		case EnhancedErrorTypeStatusCode:
			// For status code errors, check specific codes
			switch statusCode {
			case 429: // Too Many Requests
				return p.rateLimitPolicy
			case 500, 502, 503, 504: // Server errors
				return p.serverErrorPolicy
			default:
				return p.statusCodeErrorPolicy
			}
		case EnhancedErrorTypeServerUnavailable, EnhancedErrorTypeServerTimeout, EnhancedErrorTypeServerError:
			return p.serverErrorPolicy
		default:
			return p.defaultPolicy
		}
	}

	// For status code errors without HTTPErrorInterface
	if statusCode > 0 {
		switch statusCode {
		case 429:
			return p.rateLimitPolicy
		case 500, 502, 503, 504:
			return p.serverErrorPolicy
		default:
			return p.statusCodeErrorPolicy
		}
	}

	// Default policy for unknown errors
	return p.defaultPolicy
}

// =============================================================================
// INTELLIGENT ADAPTIVE RETRY POLICY
// =============================================================================

// AdaptiveRetryPolicy learns from error patterns and adjusts retry behavior
type AdaptiveRetryPolicy struct {
	config            *RetryConfig
	errorSpecificBase *ErrorSpecificRetryPolicy

	// Learning state
	errorTypeSuccess map[EnhancedErrorType]int64 // Success count by error type
	errorTypeFailure map[EnhancedErrorType]int64 // Failure count by error type

	// Adaptive configuration
	adaptiveFactor float64 // How much to adjust based on success rate (0.0 to 1.0)
	minSuccessRate float64 // Minimum success rate before reducing retries
	maxSuccessRate float64 // Success rate threshold for increasing retries
}

// NewAdaptiveRetryPolicy creates a new adaptive retry policy
func NewAdaptiveRetryPolicy(config *RetryConfig) RetryPolicy {
	return &AdaptiveRetryPolicy{
		config:            config,
		errorSpecificBase: NewErrorSpecificRetryPolicy(config).(*ErrorSpecificRetryPolicy),
		errorTypeSuccess:  make(map[EnhancedErrorType]int64),
		errorTypeFailure:  make(map[EnhancedErrorType]int64),
		adaptiveFactor:    0.3, // 30% adjustment factor
		minSuccessRate:    0.2, // Below 20% success rate, reduce retries
		maxSuccessRate:    0.8, // Above 80% success rate, can increase retries
	}
}

// Name returns the name of this retry policy
func (p *AdaptiveRetryPolicy) Name() string {
	return "AdaptiveRetryPolicy"
}

// ShouldRetry uses adaptive logic with error-specific base policy
func (p *AdaptiveRetryPolicy) ShouldRetry(ctx *RetryContext) bool {
	// First check base error-specific policy
	if !p.errorSpecificBase.ShouldRetry(ctx) {
		return false
	}

	// Apply adaptive adjustments
	if httpErr, ok := ctx.LastError.(HTTPErrorInterface); ok {
		errorType := httpErr.GetType()
		successRate := p.getSuccessRateForErrorType(errorType)

		// If success rate is very low, reduce retry attempts
		if successRate < p.minSuccessRate && ctx.Attempt >= 1 {
			return false
		}

		// If success rate is high, allow additional retries up to a limit
		if successRate > p.maxSuccessRate {
			policyConfig := p.errorSpecificBase.getPolicyForError(ctx.LastError, ctx.LastStatusCode)
			adaptiveMaxRetries := int(float64(policyConfig.MaxRetries) * (1.0 + p.adaptiveFactor))
			return ctx.Attempt < adaptiveMaxRetries
		}
	}

	return true
}

// CalculateDelay calculates delay with adaptive adjustments
func (p *AdaptiveRetryPolicy) CalculateDelay(ctx *RetryContext) time.Duration {
	baseDelay := p.errorSpecificBase.CalculateDelay(ctx)

	// Apply adaptive adjustments based on error type success rate
	if httpErr, ok := ctx.LastError.(HTTPErrorInterface); ok {
		errorType := httpErr.GetType()
		successRate := p.getSuccessRateForErrorType(errorType)

		// If success rate is low, increase delays
		if successRate < p.minSuccessRate {
			adjustmentFactor := 1.0 + (p.adaptiveFactor * (p.minSuccessRate - successRate))
			baseDelay = time.Duration(float64(baseDelay) * adjustmentFactor)
		}

		// If success rate is high, slightly reduce delays
		if successRate > p.maxSuccessRate {
			adjustmentFactor := 1.0 - (p.adaptiveFactor * 0.5 * (successRate - p.maxSuccessRate))
			baseDelay = time.Duration(float64(baseDelay) * adjustmentFactor)
		}
	}

	return baseDelay
}

// recordSuccess records a successful retry for learning
func (p *AdaptiveRetryPolicy) recordSuccess(errorType EnhancedErrorType) {
	p.errorTypeSuccess[errorType]++
}

// recordFailure records a failed retry for learning
func (p *AdaptiveRetryPolicy) recordFailure(errorType EnhancedErrorType) {
	p.errorTypeFailure[errorType]++
}

// getSuccessRateForErrorType calculates the success rate for a specific error type
func (p *AdaptiveRetryPolicy) getSuccessRateForErrorType(errorType EnhancedErrorType) float64 {
	successes := p.errorTypeSuccess[errorType]
	failures := p.errorTypeFailure[errorType]
	total := successes + failures

	if total == 0 {
		return 0.5 // Neutral success rate when no data
	}

	return float64(successes) / float64(total)
}

// =============================================================================
// CIRCUIT BREAKER AWARE RETRY POLICY
// =============================================================================

// CircuitBreakerAwarePolicy integrates circuit breaker state into retry decisions
type CircuitBreakerAwarePolicy struct {
	basePolicy     RetryPolicy
	circuitBreaker *CircuitBreaker

	// Circuit breaker specific configuration
	failuresToOpen   int           // Number of failures before opening circuit
	successesToClose int           // Number of successes needed to close circuit
	halfOpenTimeout  time.Duration // Timeout before trying half-open
	resetTimeout     time.Duration // Timeout before resetting circuit
}

// NewCircuitBreakerAwarePolicy creates a new circuit breaker aware policy
func NewCircuitBreakerAwarePolicy(basePolicy RetryPolicy, circuitBreaker *CircuitBreaker) RetryPolicy {
	return &CircuitBreakerAwarePolicy{
		basePolicy:       basePolicy,
		circuitBreaker:   circuitBreaker,
		failuresToOpen:   5,
		successesToClose: 3,
		halfOpenTimeout:  30 * time.Second,
		resetTimeout:     60 * time.Second,
	}
}

// Name returns the name of this retry policy
func (p *CircuitBreakerAwarePolicy) Name() string {
	return "CircuitBreakerAware(" + p.basePolicy.Name() + ")"
}

// ShouldRetry checks circuit breaker state before delegating to base policy
func (p *CircuitBreakerAwarePolicy) ShouldRetry(ctx *RetryContext) bool {
	// If circuit breaker is open, don't retry
	if p.circuitBreaker.ShouldBlock() {
		return false
	}

	// Check base policy
	return p.basePolicy.ShouldRetry(ctx)
}

// CalculateDelay delegates to base policy with circuit breaker considerations
func (p *CircuitBreakerAwarePolicy) CalculateDelay(ctx *RetryContext) time.Duration {
	// In half-open state, use longer delays to avoid overloading recovering service
	if p.circuitBreaker.GetState() == CircuitBreakerHalfOpen {
		baseDelay := p.basePolicy.CalculateDelay(ctx)
		return time.Duration(float64(baseDelay) * 1.5) // 50% longer delay in half-open
	}

	return p.basePolicy.CalculateDelay(ctx)
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

// calculateExponentialBackoff calculates exponential backoff delay
func calculateExponentialBackoff(attempt int, baseDelay, maxDelay time.Duration, backoffFactor float64) time.Duration {
	if attempt <= 0 {
		return baseDelay
	}

	// Calculate exponential backoff
	delay := float64(baseDelay)
	for i := 0; i < attempt; i++ {
		delay *= backoffFactor
	}

	// Cap at maximum delay
	if time.Duration(delay) > maxDelay {
		return maxDelay
	}

	return time.Duration(delay)
}

// applyJitter adds jitter to a delay to prevent thundering herd
func applyJitter(delay time.Duration, jitterFactor float64) time.Duration {
	if jitterFactor <= 0 {
		return delay
	}

	// Apply random jitter ±jitterFactor%
	jitterRange := float64(delay) * jitterFactor
	jitter := (rand.Float64()*2 - 1) * jitterRange // Random value between -jitterRange and +jitterRange

	result := time.Duration(float64(delay) + jitter)

	// Ensure delay doesn't go negative
	if result < 0 {
		return delay / 2 // Fallback to half the original delay
	}

	return result
}

// =============================================================================
// POLICY FACTORY AND CONFIGURATION
// =============================================================================

// RetryPolicyFactory creates retry policies with different configurations
type RetryPolicyFactory struct{}

// CreateStandardPolicy creates a standard retry policy
func (f *RetryPolicyFactory) CreateStandardPolicy(config *RetryConfig) RetryPolicy {
	return NewStandardRetryPolicy(config)
}

// CreateErrorSpecificPolicy creates an error-specific retry policy
func (f *RetryPolicyFactory) CreateErrorSpecificPolicy(config *RetryConfig) RetryPolicy {
	return NewErrorSpecificRetryPolicy(config)
}

// CreateAdaptivePolicy creates an adaptive retry policy
func (f *RetryPolicyFactory) CreateAdaptivePolicy(config *RetryConfig) RetryPolicy {
	return NewAdaptiveRetryPolicy(config)
}

// CreateCircuitBreakerAwarePolicy creates a circuit breaker aware policy
func (f *RetryPolicyFactory) CreateCircuitBreakerAwarePolicy(basePolicy RetryPolicy, circuitBreaker *CircuitBreaker) RetryPolicy {
	return NewCircuitBreakerAwarePolicy(basePolicy, circuitBreaker)
}

// CreateSmartPolicy creates a comprehensive retry policy with all features
func (f *RetryPolicyFactory) CreateSmartPolicy(config *RetryConfig, circuitBreaker *CircuitBreaker) RetryPolicy {
	// Create base adaptive policy
	adaptivePolicy := NewAdaptiveRetryPolicy(config)

	// Wrap with circuit breaker awareness
	return NewCircuitBreakerAwarePolicy(adaptivePolicy, circuitBreaker)
}

// PolicyConfiguration allows customization of retry policy behavior
type PolicyConfiguration struct {
	// Global settings
	EnableAdaptiveLearning bool          // Whether to enable adaptive learning
	EnableCircuitBreaker   bool          // Whether to enable circuit breaker
	GlobalTimeout          time.Duration // Global timeout for all retries

	// Error-specific overrides
	NetworkErrorOverride *RetryPolicyConfig // Override for network errors
	TimeoutErrorOverride *RetryPolicyConfig // Override for timeout errors
	ServerErrorOverride  *RetryPolicyConfig // Override for server errors
	RateLimitOverride    *RetryPolicyConfig // Override for rate limit errors

	// Adaptive learning settings
	AdaptiveFactor float64 // Learning rate (0.0 to 1.0)
	MinSuccessRate float64 // Minimum success rate threshold
	MaxSuccessRate float64 // Maximum success rate threshold

	// Circuit breaker settings
	FailuresToOpen   int           // Failures before opening circuit
	SuccessesToClose int           // Successes needed to close circuit
	HalfOpenTimeout  time.Duration // Timeout before trying half-open
}

// NewPolicyConfiguration creates a default policy configuration
func NewPolicyConfiguration() *PolicyConfiguration {
	return &PolicyConfiguration{
		EnableAdaptiveLearning: true,
		EnableCircuitBreaker:   true,
		GlobalTimeout:          5 * time.Minute,
		AdaptiveFactor:         0.3,
		MinSuccessRate:         0.2,
		MaxSuccessRate:         0.8,
		FailuresToOpen:         5,
		SuccessesToClose:       3,
		HalfOpenTimeout:        30 * time.Second,
	}
}

// CreateConfiguredPolicy creates a retry policy with custom configuration
func CreateConfiguredPolicy(config *RetryConfig, policyConfig *PolicyConfiguration) RetryPolicy {
	factory := &RetryPolicyFactory{}

	var basePolicy RetryPolicy

	if policyConfig.EnableAdaptiveLearning {
		basePolicy = factory.CreateAdaptivePolicy(config)
	} else {
		basePolicy = factory.CreateErrorSpecificPolicy(config)
	}

	if policyConfig.EnableCircuitBreaker {
		circuitBreaker := NewCircuitBreaker()
		circuitBreaker.FailureThreshold = policyConfig.FailuresToOpen
		circuitBreaker.SuccessThreshold = policyConfig.SuccessesToClose
		circuitBreaker.Timeout = policyConfig.HalfOpenTimeout

		basePolicy = factory.CreateCircuitBreakerAwarePolicy(basePolicy, circuitBreaker)
	}

	return basePolicy
}
