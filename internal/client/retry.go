package client

import (
	"math"
	"math/rand"
	"sync"
	"time"
)

// CalculateDelay calculates the delay for the next retry attempt using exponential backoff
func CalculateDelay(attempt int, config *RetryConfig) time.Duration {
	if attempt <= 1 {
		return config.InitialDelay
	}

	// Calculate exponential backoff (attempt-1 because first retry is attempt 1)
	delay := float64(config.InitialDelay) * math.Pow(config.BackoffFactor, float64(attempt-1))

	// Apply maximum delay limit
	if delay > float64(config.MaxDelay) {
		delay = float64(config.MaxDelay)
	}

	// Add jitter if enabled
	if config.Jitter {
		jitter := delay * 0.1 * (rand.Float64()*2 - 1) // ±10% jitter
		delay += jitter
	}

	// Ensure minimum delay
	if delay < float64(config.InitialDelay) {
		delay = float64(config.InitialDelay)
	}

	return time.Duration(delay)
}

// ShouldRetry determines if a request should be retried based on error and status code
func ShouldRetry(err error, statusCode int, config *RetryConfig) bool {
	// If retries are disabled, don't retry
	if config.MaxRetries <= 0 {
		return false
	}

	// If no error and successful status code, don't retry
	if err == nil && statusCode >= 200 && statusCode < 300 {
		return false
	}

	// Check for retryable HTTP status codes
	if statusCode > 0 {
		for _, retryableCode := range config.RetryableErrors {
			if statusCode == retryableCode {
				return true
			}
		}
	}

	// Check for retryable network errors
	if err != nil {
		return isTransientError(err)
	}

	return false
}

// Note: isTransientError function is defined in client.go and shared across the package

// RetryExecutor provides a higher-level interface for executing operations with retry logic
type RetryExecutor struct {
	config *RetryConfig
}

// NewRetryExecutor creates a new RetryExecutor with the given configuration
func NewRetryExecutor(config *RetryConfig) *RetryExecutor {
	if config == nil {
		config = DefaultRetryConfig()
	}
	return &RetryExecutor{config: config}
}

// Execute executes a function with retry logic
func (r *RetryExecutor) Execute(operation func() (interface{}, int, error)) (interface{}, error) {
	var lastErr error
	var result interface{}

	for attempt := 0; attempt <= r.config.MaxRetries; attempt++ {
		// Add retry attempt delay (except for first attempt)
		if attempt > 0 {
			delay := CalculateDelay(attempt-1, r.config)
			time.Sleep(delay)
		}

		// Execute the operation
		result, statusCode, err := operation()

		// If successful, return immediately
		if err == nil && statusCode >= 200 && statusCode < 300 {
			return result, nil
		}

		// Check if we should retry
		if !ShouldRetry(err, statusCode, r.config) {
			return result, err
		}

		lastErr = err

		// If we've exhausted all retries, break
		if attempt == r.config.MaxRetries {
			break
		}
	}

	return result, lastErr
}

// =============================================================================
// ENHANCED RETRY ORCHESTRATION FRAMEWORK
// =============================================================================

// RetryPolicy defines the interface for retry decision policies
type RetryPolicy interface {
	// ShouldRetry determines whether a failed request should be retried
	ShouldRetry(ctx *RetryContext) bool
	// CalculateDelay computes the delay before the next retry attempt
	CalculateDelay(ctx *RetryContext) time.Duration
	// Name returns the name of the retry policy for logging and metrics
	Name() string
}

// RetryContext holds the state and metadata for a series of retry attempts
type RetryContext struct {
	// Request information
	Method      string            // HTTP method (GET, POST, etc.)
	URL         string            // Request URL
	Headers     map[string]string // Request headers
	RequestBody []byte            // Request body

	// Retry attempt tracking
	Attempt         int           // Current attempt number (0-based)
	MaxRetries      int           // Maximum allowed retries
	StartTime       time.Time     // When the first attempt started
	LastAttempt     time.Time     // When the last attempt was made
	TotalElapsed    time.Duration // Total time elapsed since first attempt
	CumulativeDelay time.Duration // Total time spent in delays

	// Error tracking
	LastError      error              // Last error encountered
	LastStatusCode int                // Last HTTP status code
	ErrorHistory   []RetryAttemptInfo // History of all attempts

	// Configuration and policies
	Config   *RetryConfig  // Basic retry configuration
	Policies []RetryPolicy // Applied retry policies

	// Circuit breaker state
	CircuitBreakerState CircuitBreakerState // Current circuit breaker state

	// Metrics and monitoring
	Metrics *RetryMetrics // Retry-specific metrics

	// Concurrency control
	mu sync.RWMutex // Mutex for thread-safe access
}

// RetryAttemptInfo records information about a single retry attempt
type RetryAttemptInfo struct {
	AttemptNumber int           // Attempt number (0-based)
	Timestamp     time.Time     // When the attempt was made
	Duration      time.Duration // How long the attempt took
	Error         error         // Error encountered (if any)
	StatusCode    int           // HTTP status code returned
	DelayAfter    time.Duration // Delay applied after this attempt
}

// CircuitBreakerState represents the current state of the circuit breaker
type CircuitBreakerState int

const (
	CircuitBreakerClosed   CircuitBreakerState = iota // Normal operation
	CircuitBreakerOpen                                // Failing fast, not allowing requests
	CircuitBreakerHalfOpen                            // Testing if service has recovered
)

func (s CircuitBreakerState) String() string {
	switch s {
	case CircuitBreakerClosed:
		return "closed"
	case CircuitBreakerOpen:
		return "open"
	case CircuitBreakerHalfOpen:
		return "half-open"
	default:
		return "unknown"
	}
}

// RetryMetrics tracks comprehensive metrics for retry operations
type RetryMetrics struct {
	// Basic retry metrics
	TotalAttempts     int64 // Total number of retry attempts
	SuccessfulRetries int64 // Number of successful retries
	FailedRetries     int64 // Number of failed retries
	TimeoutRetries    int64 // Number of retries due to timeouts
	NetworkRetries    int64 // Number of retries due to network errors
	ServerRetries     int64 // Number of retries due to server errors

	// Timing metrics
	TotalRetryTime   time.Duration // Total time spent in retries
	AverageRetryTime time.Duration // Average time per retry
	MinRetryTime     time.Duration // Minimum retry time
	MaxRetryTime     time.Duration // Maximum retry time

	// Circuit breaker metrics
	CircuitBreakerTrips  int64 // Number of times circuit breaker opened
	CircuitBreakerResets int64 // Number of times circuit breaker closed

	// Policy-specific metrics
	PolicyMetrics map[string]interface{} // Metrics by policy name

	// Concurrency control
	mu sync.RWMutex // Mutex for thread-safe updates
}

// RetryOrchestrator coordinates retry execution with sophisticated policies and metrics
type RetryOrchestrator struct {
	config          *RetryConfig    // Basic retry configuration
	defaultPolicies []RetryPolicy   // Default retry policies to apply
	circuitBreaker  *CircuitBreaker // Circuit breaker for fault tolerance
	metrics         *RetryMetrics   // Global retry metrics
	logger          RetryLogger     // Logger for retry operations

	// Configuration
	enableCircuitBreaker bool          // Whether to use circuit breaker
	globalTimeout        time.Duration // Global timeout for all retries

	// Concurrency control
	mu sync.RWMutex // Mutex for thread-safe access
}

// CircuitBreaker implements the circuit breaker pattern for retry operations
type CircuitBreaker struct {
	// Configuration
	FailureThreshold int           // Number of failures before opening
	SuccessThreshold int           // Number of successes needed to close
	Timeout          time.Duration // Timeout before transitioning to half-open

	// State tracking
	State           CircuitBreakerState // Current state
	FailureCount    int                 // Current failure count
	SuccessCount    int                 // Current success count in half-open state
	LastFailureTime time.Time           // Time of last failure

	// Metrics
	TotalFailures  int64 // Total failures recorded
	TotalSuccesses int64 // Total successes recorded
	TotalTrips     int64 // Total times circuit breaker opened
	TotalResets    int64 // Total times circuit breaker closed

	// Concurrency control
	mu sync.RWMutex // Mutex for thread-safe state changes
}

// RetryLogger interface for logging retry operations
type RetryLogger interface {
	LogRetryAttempt(ctx *RetryContext, attempt int)
	LogRetrySuccess(ctx *RetryContext)
	LogRetryFailure(ctx *RetryContext, finalError error)
	LogCircuitBreakerTrip(reason string)
	LogCircuitBreakerReset()
}

// DefaultRetryLogger provides a simple implementation of RetryLogger
type DefaultRetryLogger struct{}

func (l *DefaultRetryLogger) LogRetryAttempt(ctx *RetryContext, attempt int) {
	// Implementation would use a proper logging framework
	// For now, this is a placeholder
}

func (l *DefaultRetryLogger) LogRetrySuccess(ctx *RetryContext) {
	// Implementation would use a proper logging framework
}

func (l *DefaultRetryLogger) LogRetryFailure(ctx *RetryContext, finalError error) {
	// Implementation would use a proper logging framework
}

func (l *DefaultRetryLogger) LogCircuitBreakerTrip(reason string) {
	// Implementation would use a proper logging framework
}

func (l *DefaultRetryLogger) LogCircuitBreakerReset() {
	// Implementation would use a proper logging framework
}

// NewRetryContext creates a new retry context for a request
func NewRetryContext(method, url string, headers map[string]string, body []byte, config *RetryConfig) *RetryContext {
	return &RetryContext{
		Method:              method,
		URL:                 url,
		Headers:             headers,
		RequestBody:         body,
		Attempt:             0,
		MaxRetries:          config.MaxRetries,
		StartTime:           time.Now(),
		LastAttempt:         time.Now(),
		ErrorHistory:        make([]RetryAttemptInfo, 0),
		Config:              config,
		Policies:            make([]RetryPolicy, 0),
		CircuitBreakerState: CircuitBreakerClosed,
		Metrics:             NewRetryMetrics(),
	}
}

// NewRetryMetrics creates a new RetryMetrics instance
func NewRetryMetrics() *RetryMetrics {
	return &RetryMetrics{
		PolicyMetrics: make(map[string]interface{}),
		MinRetryTime:  time.Duration(math.MaxInt64),
	}
}

// NewRetryOrchestrator creates a new retry orchestrator with default configuration
func NewRetryOrchestrator(config *RetryConfig) *RetryOrchestrator {
	if config == nil {
		config = DefaultRetryConfig()
	}

	orchestrator := &RetryOrchestrator{
		config:               config,
		defaultPolicies:      make([]RetryPolicy, 0),
		circuitBreaker:       NewCircuitBreaker(),
		metrics:              NewRetryMetrics(),
		logger:               &DefaultRetryLogger{},
		enableCircuitBreaker: true,
		globalTimeout:        30 * time.Second,
	}

	// Add default retry policies
	orchestrator.AddPolicy(NewStandardRetryPolicy(config))
	orchestrator.AddPolicy(NewExponentialBackoffPolicy(config))

	return orchestrator
}

// NewCircuitBreaker creates a new circuit breaker with default settings
func NewCircuitBreaker() *CircuitBreaker {
	return &CircuitBreaker{
		FailureThreshold: 5,
		SuccessThreshold: 3,
		Timeout:          60 * time.Second,
		State:            CircuitBreakerClosed,
	}
}

// AddPolicy adds a retry policy to the orchestrator
func (ro *RetryOrchestrator) AddPolicy(policy RetryPolicy) {
	ro.mu.Lock()
	defer ro.mu.Unlock()
	ro.defaultPolicies = append(ro.defaultPolicies, policy)
}

// RecordAttempt records information about a retry attempt
func (ctx *RetryContext) RecordAttempt(duration time.Duration, err error, statusCode int, delayAfter time.Duration) {
	ctx.mu.Lock()
	defer ctx.mu.Unlock()

	attemptInfo := RetryAttemptInfo{
		AttemptNumber: ctx.Attempt,
		Timestamp:     time.Now(),
		Duration:      duration,
		Error:         err,
		StatusCode:    statusCode,
		DelayAfter:    delayAfter,
	}

	ctx.ErrorHistory = append(ctx.ErrorHistory, attemptInfo)
	ctx.LastError = err
	ctx.LastStatusCode = statusCode
	ctx.LastAttempt = time.Now()
	ctx.TotalElapsed = time.Since(ctx.StartTime)
	ctx.CumulativeDelay += delayAfter
}

// ShouldContinueRetrying checks if retrying should continue based on policies and circuit breaker
func (ro *RetryOrchestrator) ShouldContinueRetrying(ctx *RetryContext) bool {
	ro.mu.RLock()
	defer ro.mu.RUnlock()

	// Check circuit breaker state
	if ro.enableCircuitBreaker && ro.circuitBreaker.ShouldBlock() {
		return false
	}

	// Check if we've exceeded maximum retries
	if ctx.Attempt >= ctx.MaxRetries {
		return false
	}

	// Check global timeout
	if ro.globalTimeout > 0 && ctx.TotalElapsed > ro.globalTimeout {
		return false
	}

	// Apply all policies
	for _, policy := range ro.defaultPolicies {
		if !policy.ShouldRetry(ctx) {
			return false
		}
	}

	return true
}

// CalculateNextDelay calculates the delay before the next retry attempt
func (ro *RetryOrchestrator) CalculateNextDelay(ctx *RetryContext) time.Duration {
	ro.mu.RLock()
	defer ro.mu.RUnlock()

	var maxDelay time.Duration = 0

	// Use the maximum delay from all policies
	for _, policy := range ro.defaultPolicies {
		delay := policy.CalculateDelay(ctx)
		if delay > maxDelay {
			maxDelay = delay
		}
	}

	return maxDelay
}

// RecordSuccess records a successful retry operation
func (ro *RetryOrchestrator) RecordSuccess(ctx *RetryContext) {
	ro.mu.Lock()
	defer ro.mu.Unlock()

	ro.metrics.SuccessfulRetries++
	ro.metrics.TotalRetryTime += ctx.TotalElapsed

	if ro.enableCircuitBreaker {
		ro.circuitBreaker.RecordSuccess()
	}

	ro.logger.LogRetrySuccess(ctx)
}

// RecordFailure records a failed retry operation
func (ro *RetryOrchestrator) RecordFailure(ctx *RetryContext, finalError error) {
	ro.mu.Lock()
	defer ro.mu.Unlock()

	ro.metrics.FailedRetries++
	ro.metrics.TotalRetryTime += ctx.TotalElapsed

	if ro.enableCircuitBreaker {
		ro.circuitBreaker.RecordFailure()
	}

	ro.logger.LogRetryFailure(ctx, finalError)
}

// GetMetrics returns a copy of the current retry metrics
func (ro *RetryOrchestrator) GetMetrics() *RetryMetrics {
	ro.mu.RLock()
	defer ro.mu.RUnlock()

	// Return a copy to avoid concurrent access issues
	metricsCopy := *ro.metrics
	metricsCopy.PolicyMetrics = make(map[string]interface{})
	for k, v := range ro.metrics.PolicyMetrics {
		metricsCopy.PolicyMetrics[k] = v
	}

	return &metricsCopy
}

// Circuit breaker methods

// ShouldBlock returns true if the circuit breaker should block requests
func (cb *CircuitBreaker) ShouldBlock() bool {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	switch cb.State {
	case CircuitBreakerOpen:
		// Check if timeout has passed to transition to half-open
		if time.Since(cb.LastFailureTime) > cb.Timeout {
			cb.setState(CircuitBreakerHalfOpen)
			return false
		}
		return true
	case CircuitBreakerHalfOpen:
		// Allow limited requests to test if service has recovered
		return false
	case CircuitBreakerClosed:
		return false
	default:
		return false
	}
}

// RecordSuccess records a successful operation
func (cb *CircuitBreaker) RecordSuccess() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.TotalSuccesses++

	switch cb.State {
	case CircuitBreakerHalfOpen:
		cb.SuccessCount++
		if cb.SuccessCount >= cb.SuccessThreshold {
			cb.setState(CircuitBreakerClosed)
			cb.FailureCount = 0
			cb.SuccessCount = 0
			cb.TotalResets++
		}
	case CircuitBreakerClosed:
		// Reset failure count on success
		cb.FailureCount = 0
	}
}

// RecordFailure records a failed operation
func (cb *CircuitBreaker) RecordFailure() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.TotalFailures++
	cb.LastFailureTime = time.Now()

	switch cb.State {
	case CircuitBreakerClosed:
		cb.FailureCount++
		if cb.FailureCount >= cb.FailureThreshold {
			cb.setState(CircuitBreakerOpen)
			cb.TotalTrips++
		}
	case CircuitBreakerHalfOpen:
		// Immediately go back to open state on failure
		cb.setState(CircuitBreakerOpen)
		cb.SuccessCount = 0
		cb.TotalTrips++
	}
}

// setState changes the circuit breaker state (internal method, assumes lock is held)
func (cb *CircuitBreaker) setState(newState CircuitBreakerState) {
	cb.State = newState
}

// GetState returns the current circuit breaker state
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.State
}

// =============================================================================
// RETRY POLICY IMPLEMENTATIONS
// =============================================================================

// StandardRetryPolicy implements basic retry logic based on RetryConfig
type StandardRetryPolicy struct {
	config *RetryConfig
}

// NewStandardRetryPolicy creates a new standard retry policy
func NewStandardRetryPolicy(config *RetryConfig) RetryPolicy {
	return &StandardRetryPolicy{config: config}
}

// Name returns the name of this policy
func (p *StandardRetryPolicy) Name() string {
	return "standard"
}

// ShouldRetry determines if a request should be retried based on standard logic
func (p *StandardRetryPolicy) ShouldRetry(ctx *RetryContext) bool {
	// Use the existing ShouldRetry function logic
	return ShouldRetry(ctx.LastError, ctx.LastStatusCode, p.config)
}

// CalculateDelay calculates delay using the standard CalculateDelay function
func (p *StandardRetryPolicy) CalculateDelay(ctx *RetryContext) time.Duration {
	// Use the existing CalculateDelay function logic
	return CalculateDelay(ctx.Attempt, p.config)
}

// ExponentialBackoffPolicy implements exponential backoff with enhanced jitter
type ExponentialBackoffPolicy struct {
	config       *RetryConfig
	jitterFactor float64 // Enhanced jitter factor (0.0 to 1.0)
}

// NewExponentialBackoffPolicy creates a new exponential backoff policy
func NewExponentialBackoffPolicy(config *RetryConfig) RetryPolicy {
	return &ExponentialBackoffPolicy{
		config:       config,
		jitterFactor: 0.1, // 10% jitter by default
	}
}

// Name returns the name of this policy
func (p *ExponentialBackoffPolicy) Name() string {
	return "exponential_backoff"
}

// ShouldRetry determines if a request should be retried
func (p *ExponentialBackoffPolicy) ShouldRetry(ctx *RetryContext) bool {
	// Apply standard retry logic but with enhanced considerations
	if !ShouldRetry(ctx.LastError, ctx.LastStatusCode, p.config) {
		return false
	}

	// Additional check: don't retry if we've been trying for too long
	if ctx.TotalElapsed > 30*time.Second {
		return false
	}

	return true
}

// CalculateDelay calculates exponential backoff delay with enhanced jitter
func (p *ExponentialBackoffPolicy) CalculateDelay(ctx *RetryContext) time.Duration {
	if ctx.Attempt <= 0 {
		return p.config.InitialDelay
	}

	// Calculate exponential backoff
	delay := float64(p.config.InitialDelay) * math.Pow(p.config.BackoffFactor, float64(ctx.Attempt))

	// Apply maximum delay limit
	if delay > float64(p.config.MaxDelay) {
		delay = float64(p.config.MaxDelay)
	}

	// Add enhanced jitter if enabled
	if p.config.Jitter {
		jitterRange := delay * p.jitterFactor
		jitter := jitterRange * (rand.Float64()*2 - 1) // ±jitterFactor% jitter
		delay += jitter
	}

	// Ensure minimum delay
	if delay < float64(p.config.InitialDelay) {
		delay = float64(p.config.InitialDelay)
	}

	return time.Duration(delay)
}
