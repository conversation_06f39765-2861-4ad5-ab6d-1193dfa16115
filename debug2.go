package main

import (
	"fmt"
	"time"
	"neuralmetergo/internal/metrics"
)

func main() {
	// Create a time-based window with 5 second duration
	window, err := metrics.NewTimeBasedWindow(time.Second*5, 10, time.Second)
	if err != nil {
		panic(err)
	}

	// Add elements one by one and see what happens
	now := time.Now()
	fmt.Printf("Now: %v\n", now)
	
	// Add first element (outside window)
	elem1 := metrics.WindowElement{Value: 1.0, Timestamp: now.Add(-time.Second * 15)}
	fmt.Printf("Adding element 1: Value=%f, Age=%v\n", elem1.Value, now.Sub(elem1.Timestamp))
	window.Add(elem1)
	fmt.Printf("  Window size after adding elem1: %d\n", window.Size())
	
	// Add second element (edge case)
	elem2 := metrics.WindowElement{Value: 2.0, Timestamp: now.Add(-time.Second * 5)}
	fmt.Printf("Adding element 2: Value=%f, Age=%v\n", elem2.Value, now.Sub(elem2.Timestamp))
	window.Add(elem2)
	fmt.Printf("  Window size after adding elem2: %d\n", window.Size())
	
	// Add third element (clearly in window)
	elem3 := metrics.WindowElement{Value: 3.0, Timestamp: now}
	fmt.Printf("Adding element 3: Value=%f, Age=%v\n", elem3.Value, now.Sub(elem3.Timestamp))
	window.Add(elem3)
	fmt.Printf("  Window size after adding elem3: %d\n", window.Size())

	// Final check
	allElements := window.GetAll()
	fmt.Printf("Final elements in window: %d\n", len(allElements))
	for i, elem := range allElements {
		fmt.Printf("  Element %d: Value=%f, Age from original now=%v\n", 
			i, elem.Value, now.Sub(elem.Timestamp))
	}
}
